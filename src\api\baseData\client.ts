//客户信息
import { defHttp } from '/@/utils/http/axios'
import type { ClientParams, ClientGetResult, CreateUpdateClientParams } from './model/clientModel'

enum Api {
  GetClient = '/erp/cm/get',
  UpdateClient = '/erp/cm/update',
  CreateClient = '/erp/cm/add',
  RemoveClient = '/erp/cm/remove',
  DetailClient = '/erp/cm/detail'
}

export const getClient = (params: ClientParams) =>
  defHttp.get<{
    header: string[]
    data: ClientGetResult
  }>({ url: Api.GetClient, params })

export const updateClient = (params?: CreateUpdateClientParams) =>
  defHttp.post({ url: Api.UpdateClient, params }, { successMessageMode: 'message', isTransformResponse: true })

export const createClient = (params?: CreateUpdateClientParams) =>
  defHttp.post({ url: Api.CreateClient, params }, { successMessageMode: 'message' })

export const removeClient = (params: { id: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.RemoveClient, params }, { successMessageMode: 'message' })

export const detailClient = (params: { id: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.DetailClient, params })
