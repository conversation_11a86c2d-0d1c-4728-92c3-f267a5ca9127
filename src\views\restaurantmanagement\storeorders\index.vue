<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="{ no_cache: 0 }">
      <template #toolbar>
        <Button
          type="primary"
          @click="
            () => {
              reload()
            }
          "
          >更新数据</Button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>

    <detailDrawer @register="registerDetailDrawer" />
  </div>
</template>
<script lang="ts" setup>
import { columns, schemas } from './datas/datas'
import { mfgetOrderList } from '/@/api/restaurantmanagement/storeorders'
import { BasicTable, useTable, TableImg, EditRecordRow, ActionItem, TableAction } from '/@/components/Table'
import detailDrawer from './components/detailDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { Button } from 'ant-design-vue'

const [registerDetailDrawer, { openDrawer }] = useDrawer()

const [registerTable, { reload }] = useTable({
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  columns,
  api: mfgetOrderList,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['checkout_at', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  },
  pagination: {
    pageSize: 10
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '详情',
      onClick: handledetail.bind(null, record)
    }
  ]

  return editButtonList
}
function handledetail(record: any) {
  openDrawer(true, record)
}
</script>
