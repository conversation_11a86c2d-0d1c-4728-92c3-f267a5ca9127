<template>
  <BasicModal @register="register" title="部门绑定" width="500px" @ok="handleOk" :min-height="300">
    <BasicForm @register="registerFrom" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { mfupdateMenuRelDept } from '/@/api/restaurantmanagement/restaurantdishes'

const emit = defineEmits(['success'])

const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  setFieldsValue(data)
})

const [registerFrom, { setFieldsValue, validate }] = useForm({
  showActionButtonGroup: false,
  labelCol: { style: 'width: 100px' },
  schemas: [
    {
      field: 'code',
      label: 'code',
      component: 'Input',
      show: false
    },
    {
      field: 'dept_id',
      label: '部门',
      required: true,
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '空中餐厅',
            value: 173
          },
          {
            label: 'SAWA餐厅',
            value: 174
          },
          {
            label: '麟料理餐厅',
            value: 175
          },
          {
            label: '江南餐厅',
            value: 176
          },
          {
            label: '法餐厅',
            value: 177
          },
          {
            label: '甜点酒水',
            value: 178
          },
          {
            label: '粤棠餐厅',
            value: 189
          }
        ]
      },
      colProps: {
        span: 24
      }
    }
  ]
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    await mfupdateMenuRelDept(formdata)
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    changeOkLoading(false)
  }
}
</script>
