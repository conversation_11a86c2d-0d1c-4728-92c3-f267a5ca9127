import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
const receiptInformationList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      date: '@date',
      order_no: '@guid',
      creator: '财务',
      processor: '关联的员工',
      'fund_id|1': ['0', '1', '2'],
      'customer_profile|1': ['客户档案1', '客户档案2', '客户档案3'],
      department: '测试部门',
      fund_info: '@guid',
      amount: '@natural(1, 1000)',
      sales_order_no: '@guid',
      'status|1': [0, 1, 2]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/receiptInformation',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(receiptInformationList)
    }
  }
] as MockMethod[]
