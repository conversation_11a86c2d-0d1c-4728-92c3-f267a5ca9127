import { FormSchema } from '/@/components/Form'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { getStaffList } from '/@/api/baseData/staff'
import { Rule } from 'ant-design-vue/lib/form'
import { getWorkList, getErpSupplier, getClientList } from '/@/api/commonUtils'
import type { ItemRequestResponse, TPage } from '/@/views/erp/purchaseOrder/datas/types'
import { VxeGridPropTypes, VxeTablePropTypes } from 'vxe-table'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import { ref } from 'vue'
import { getRmbquot } from '/@/api/erp/sales'
import { mul } from '/@/utils/math'
import { Input } from 'ant-design-vue'

// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
// const { getMapSalesWork } = useMapStoreWithOut()

// const workList = getMapSalesWork
// async function handleGetWorkList() {
//   const result = await getWorkList({ pageSize: 99999, type: 1, receivable: 1, status: [1, 3, 4, 5, 6] })
//   for (const workItem of result.items) {
//     workList.value[workItem.id] = workItem.strid ? workItem.strid : ''
//   }
// }
//
// handleGetWorkList()
const pathname = window.location.pathname

//亏本原因
export const lossReason = [
  { label: '销售策略需要，让利部分亏损，整体盈利', value: '销售策略需要，让利部分亏损，整体盈利' },
  { label: '业务员报价过低导致亏损', value: '业务员报价过低导致亏损' },
  { label: '供应商错误报价导致亏损', value: '供应商错误报价导致亏损' },
  { label: '备注其他原因', value: '备注其他原因' }
]

//订单类型  备货定单不需要客户
export const orderType = ref(3)
export const getSalesWork = ref()

export const getSchemasList = (
  isUpdate: boolean,
  type: string,
  page: TPage,
  handleFn?: { validateFields: Function; handleSalesOrderChange: Function }
  // record?: Recordable
): FormSchema[] => [
  {
    field: 'work_id',
    label: '关联销售/备货订单',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => ({
      resultField: 'items',
      api: getWorkList,
      searchMode: true,
      pagingMode: true,
      searchParamField: 'source_uniqid',
      // immediate: true,
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'source_uniqid',
        disabled: !isUpdate || page === 'saleOrder'
      },
      params: {
        item_left: 1,
        // type: 3,
        status: type === 'detail' ? [1, 2, 3, 4, 5, 6] : [2, 3, 4, 5],
        pageSize: '999999',
        auth: 2,
        order: 1,
        is_finish_split: pathname == '/s/' ? 1 : null
      },
      onChange: async (val: number, shall: any) => {
        try {
          formModel.orderType = shall?.type
          // if (['add'].includes(type)) await handleFn!.validateFields!(['work_id'])
          handleFn!.handleSalesOrderChange(val, shall)
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }),
    required: true,
    show: type === 'add',
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  },
  {
    field: 'orderType',
    label: '订单类型',
    component: 'Input',
    show: false
  },
  {
    field: 'work',
    label: '关联销售订单',
    component: 'Input',
    // required: true,
    render: ({ model }) => {
      return model.work?.source_uniqid
    },
    ifShow: ['edit', 'detail'].includes(type),
    colProps: { span: 8 }
  },
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    required: true,
    ifShow: type !== 'add',
    // componentProps: {
    //   disabled: !isUpdate
    // }
    dynamicDisabled: true,
    colProps: { span: 8 }
  },
  {
    field: 'client',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true,
        labelInValue: true
      },
      // onChange: async () => {
      //   try {
      //     await handleFn!.validateFields!(['client'])
      //   } catch (e) {
      //     console.log(e, '客户下拉框error')
      //     throw new Error(`${e}`)
      //   }
      // },
      resultField: 'items'
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 },
    show({ model }) {
      return model.orderType === 3 ? true : false
    }
  },
  {
    field: 'sales_price',
    label: '销售总价',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true,
    slot: 'totalSalesPriceSlot',
    colProps: { span: 8 }
  },
  {
    field: 'currency',
    // dynamicDisabled: type !== 'add',
    label: '货币',
    component: 'Input',
    required: true,
    defaultValue: '人民币',
    componentProps: {
      disabled: true
    },
    colProps: { span: 8 }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
      // disabled: !isUpdate
    },
    required: true,
    colProps: { span: 8 },
    dynamicDisabled: true
  },
  {
    field: 'total_price',
    label: '成本总价',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true,
    slot: 'totalPriceSlot',
    colProps: { span: 8 }
  },
  {
    field: 'tax_amount',
    label: '税金',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true,
    slot: 'TaxAmountSlot',
    colProps: { span: 8 }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      },
      params: {
        pageSize: 9999
      }
      // onChange: async () => {
      //   try {
      //     await handleFn!.validateFields!(['inCharge'])
      //   } catch (e) {
      //     throw new Error(`${e}`)
      //   }
      // }
    },
    ifShow: ['add'].includes(type),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  },
  {
    field: 'is_gbuilder',
    label: '是否Gbuilder',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      disabled: true
    },
    colProps: { span: 8 }
  },
  {
    field: 'cost',
    label: '应付金额',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true,
    slot: 'costSlot',
    colProps: { span: 8 }
  },

  {
    field: 'addpoint',
    label: '开票税点加收金额',
    component: 'InputNumber',
    // dynamicDisabled: true,
    // required: true,
    slot: 'addpoint',
    colProps: { span: 8 }
    // ifShow: ['edit', 'detail'].includes(type)
  },
  {
    field: 'supplier_id',
    label: '供应商',
    required: true,
    component: 'PagingApiSelect',
    componentProps: {
      api: getErpSupplier,
      searchMode: true,
      pagingMode: true,
      searchParamField: 'name',
      pagingSize: 20,
      returnParamsField: 'id',
      returnResultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        disabled: !isUpdate
      },
      resultField: 'items'
    },
    ifShow: ['edit', 'detail'].includes(type) && page !== 'saleOrder',
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  },
  {
    field: 'paid',
    label: '已付金额',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: false, // 梁工要求
    ifShow: ['edit', 'detail'].includes(type),
    componentProps: {
      precision: 4
    },
    colProps: { span: 8 }
  },
  {
    field: 'add_point',
    label: '开票加收税点',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        max: 100,
        min: 0,
        formatter: (value) => `${value}%`,
        onChange(value) {
          if (formModel.sales_goods) {
            formModel.sales_goods?.forEach((item) => {
              item.add_point = value
              item.cost_price = (item.unit_price / (1 + (value || 0) / 100)).toFixed(4)
            })
          }
        },
        disabled: ['detail'].includes(type)
      }
    },
    colProps: { span: 8 },
    ifShow: ['edit', 'detail'].includes(type) && page !== 'saleOrder'
  },
  // {
  //   field: 'imgs',
  //   label: '图片',
  //   component: 'Upload',
  //   componentProps: {
  //     // helpText: '单个图片不超过2m,文件为jpg、png、jpge格式',
  //     // api: supplierUploadFile,
  //     // accept: ['image/*'],
  //     // showPreviewNumber: false
  //     disabled: !isUpdate
  //   },
  //   slot: 'Imgs',
  //   // required: true,
  //   rules: [
  //     {
  //       required: true,
  //       validator: async (_rule: Rule, value: string) => {
  //         if (!value || value.length === 0) return Promise.reject('请上传图片')
  //         return Promise.resolve()
  //       }
  //       // trigger: 'change'
  //     }
  //   ]
  // },
  // {
  //   field: 'auditor',
  //   label: '审核人',
  //   component: 'ApiSelect',
  //   componentProps: {
  //     api: getStaffList,
  //     resultField: 'items',
  //     selectProps: {
  //       allowClear: true,
  //       fieldNames: { key: 'id', value: 'id', label: 'name' },
  //       showSearch: true,
  //       placeholder: '请选择',
  //       optionFilterProp: 'name',
  //       disabled: !isUpdate
  //     },
  //     onChange: async () => {
  //       try {
  //         await handleFn!.validateFields!(['auditor'])
  //       } catch (e) {
  //         console.log(e)
  //       }
  //     }
  //   },
  //   required: true
  // },

  {
    field: 'processor',
    label: '申请人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
      // onChange: async () => {
      //   try {
      //     await handleFn!.validateFields!(['processor'])
      //   } catch (e) {
      //     throw new Error(`${e}`)
      //   }
      // }
    },
    required: true,
    ifShow: ['add'].includes(type),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  },

  {
    field: 'contracting_party',
    label: '我司签约主体',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => ({
      api: cpgetList,
      params: {
        work_id: page == 'saleOrder' ? getSalesWork.value : formModel.work_id
      },
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: ['detail'].includes(type),
    colProps: { span: 8 }
  },
  {
    field: 'foreign_currency_amount',
    label: '外汇总额',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true,
    slot: 'currencyAmount',
    colProps: { span: 8 }
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files',
    componentProps: {
      disabled: !isUpdate
      //   helpText: '单个图片不超过2m',
      //   api: supplierUploadFile,
      //   accept: ['*'],
      //   showPreviewNumber: false
    },
    colProps: { span: 8 }
    // required: true,
    // rules: [
    //   {
    //     required: true,
    //     validator: async (_rule: Rule, value: string) => {
    //       if (!value || value.length === 0) return Promise.reject('请上传附件')
    //       return Promise.resolve()
    //     }
    //     // trigger: 'change'
    //   }
    // ]
  },
  {
    field: 'arrival_at',
    label: '到货日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicDisabled: ['detail'].includes(type),
    colProps: { span: 8 }
  },
  {
    field: 'gbuilder_percentage',
    label: 'Gbuilder服务费抽成比例',
    component: 'InputNumber',
    componentProps: {
      max: 5,
      min: 0,
      precision: 2,
      formatter: (value: number) => {
        return `${value}%`
      },
      disabled: ['detail'].includes(type)
    },
    required(renderCallbackParams) {
      return ['edit', 'detail'].includes(type) && renderCallbackParams.model.is_gbuilder == 1 ? true : false
    },
    ifShow(renderCallbackParams) {
      return ['edit', 'detail'].includes(type) && renderCallbackParams.model.is_gbuilder == 1 ? true : false
    },
    show(renderCallbackParams) {
      return ['edit', 'detail'].includes(type) && renderCallbackParams.model.is_gbuilder == 1 ? true : false
    },
    colProps: { span: 8 }
  },
  {
    field: 'invoice_type',
    label: '发票类型',
    component: 'Select',
    required() {
      return ['add'].includes(type) ? true : false
    },
    componentProps: ({ formModel }) => {
      return {
        options: [
          {
            label: '增值税专用发票',
            value: 1
          },
          {
            label: '普通发票',
            value: 2
          },
          {
            label: '不开票',
            value: 3
          }
        ],
        onChange(val) {
          if (!val) return
          if (formModel.sales_goods) {
            formModel.sales_goods?.forEach((item) => {
              item.supplier_id = null
              item.add_point = 0
              item.unit_price_tax = 0
            })
          }
        },
        disabled: ['edit', 'detail'].includes(type)
      }
    },
    colProps: { span: 8 }
  },
  {
    field: 'is_engineering',
    label: '是否工程订单',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      disabled: ['detail'].includes(type)
    },
    colProps: { span: 8 }
  },
  {
    field: 'enterprise_name',
    label: '供应商开票企业名称',
    component: 'Input',
    required(renderCallbackParams) {
      return renderCallbackParams.model.invoice_type !== 3 ? true : false
    },
    colProps: { span: 8 },
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'exchange_rate',
    label: '汇率',
    component: 'ApiSelect',
    defaultValue: '1.000000',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getRmbquot,
        resultField: 'items',
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          disabled: ['detail'].includes(type),
          onChange(val, shall) {
            if (val == '1.000000') {
              formModel.fg_amount = undefined
              formModel.total_price = undefined
            }
            formModel.rate = val
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
            formModel.currency = shall.name.split('-')[0]
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    },
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'rate',
    label: '汇率比例',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        precision: 4,
        min: 0,
        max: 100,
        onChange(val) {
          formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
        }
      }
    },
    colProps: {
      span: 8
    },
    defaultValue: '1.000000',
    dynamicDisabled: ['detail'].includes(type)
  },
  // {
  //   field: 'currency',
  //   label: '币种',
  //   component: 'Input',
  //   show: false
  // },
  {
    field: 'sales_goods',
    label: '产品列表',
    component: 'Input',
    slot: 'Goods',
    rules: [{ required: true, validator: type === 'add' ? validateGoods : validateEditGoods }]
  }
]

function validateGoods(_rule: Rule, value: ItemRequestResponse[]) {
  if (!value || value.length === 0) return Promise.reject('请先选择关联的销售订单')

  // 检查具体缺失的信息
  const invalidItems: string[] = []
  value.forEach((item, index) => {
    const issues: string[] = []
    if (item.qty_request_left < 0) {
      issues.push('剩余需求数量不能为负数')
    }
    if (!item.supplier_id) {
      issues.push('未选择供应商')
    }
    if (!item.cost_price || item.cost_price < 0) {
      issues.push('成本价格不能为空或小于0')
    }
    // if (['/s/', '/tests/'].includes(pathname)) {
    //   if (!item.material || item.material.trim() === '') {
    //     issues.push('材质不能为空')
    //   }
    //   if (!item.code || item.code.trim() === '') {
    //     issues.push('商品编码不能为空')
    //   }
    //   if (!item.brand || item.brand.trim() === '') {
    //     issues.push('商品品牌不能为空')
    //   }
    //   if (!item.invoice_name || item.invoice_name.trim() === '') {
    //     issues.push('商品开票品名不能为空')
    //   }
    // }
    if (issues.length > 0) {
      invalidItems.push(`第${index + 1}行商品"${item.name || '未知商品'}"：${issues.join('、')}`)
    }
  })

  if (invalidItems.length > 0) {
    let errorMessage = ''

    // 如果错误商品数量较少（≤5个），显示详细信息
    if (invalidItems.length <= 5) {
      errorMessage = `请完善以下信息：\n${invalidItems.join('\n')}`
    } else {
      // 如果错误商品数量较多，显示概要信息
      errorMessage = `共有 ${invalidItems.length} 个商品信息不完整，请检查以下字段：\n`

      // 统计各种错误类型的数量
      const errorStats = {
        costPrice: 0,
        // material: 0,
        // code: 0,
        quantity: 0
      }

      invalidItems.forEach((item) => {
        if (item.includes('成本价格')) errorStats.costPrice++
        // if (item.includes('材质')) errorStats.material++
        // if (item.includes('商品编码')) errorStats.code++
        if (item.includes('数量')) errorStats.quantity++
      })

      // 生成概要错误信息
      const errorSummary: string[] = []
      if (errorStats.costPrice > 0) errorSummary.push(`• ${errorStats.costPrice} 个商品成本价格有误`)
      // if (errorStats.material > 0) errorSummary.push(`• ${errorStats.material} 个商品材质为空`)
      // if (errorStats.code > 0) errorSummary.push(`• ${errorStats.code} 个商品编码为空`)
      if (errorStats.quantity > 0) errorSummary.push(`• ${errorStats.quantity} 个商品数量有误`)

      errorMessage += errorSummary.join('\n')

      // 显示前3个具体错误作为示例
      if (invalidItems.length > 0) {
        errorMessage += '\n\n示例错误：\n' + invalidItems.slice(0, 3).join('\n')
        if (invalidItems.length > 3) {
          errorMessage += `\n... 还有 ${invalidItems.length - 3} 个商品需要检查`
        }
      }
    }

    return Promise.reject(errorMessage)
  }
  return Promise.resolve()
}

function validateEditGoods(_rule: Rule, value: ItemRequestResponse[]) {
  if (!value || value.length === 0) return Promise.reject('请先选择关联的销售订单')

  // 检查具体缺失的信息
  const invalidItems: string[] = []
  value.forEach((item, index) => {
    const issues: string[] = []
    if (item.qty_purchased < 0) {
      issues.push('采购数量不能为负数')
    }
    if (!item.cost_price || item.cost_price < 0) {
      issues.push('成本价格不能为空或小于0')
    }
    // if (['/s/', '/tests/'].includes(pathname)) {
    //   if (!item.material || item.material.trim() === '') {
    //     issues.push('材质不能为空')
    //   }
    //   if (!item.code || item.code.trim() === '') {
    //     issues.push('商品编码不能为空')
    //   }
    //   if (!item.brand || item.brand.trim() === '') {
    //     issues.push('商品品牌不能为空')
    //   }
    //   if (!item.invoice_name || item.invoice_name.trim() === '') {
    //     issues.push('商品开票品名不能为空')
    //   }
    // }
    if (issues.length > 0) {
      invalidItems.push(`第${index + 1}行商品"${item.name || '未知商品'}"：${issues.join('、')}`)
    }
  })

  if (invalidItems.length > 0) {
    let errorMessage = ''

    // 如果错误商品数量较少（≤5个），显示详细信息
    if (invalidItems.length <= 5) {
      errorMessage = `请完善以下信息：\n${invalidItems.join('\n')}`
    } else {
      // 如果错误商品数量较多，显示概要信息
      errorMessage = `共有 ${invalidItems.length} 个商品信息不完整，请检查以下字段：\n`

      // 统计各种错误类型的数量
      const errorStats = {
        supplier: 0,
        costPrice: 0,
        // material: 0,
        // code: 0,
        // brand: 0,
        // invoiceName: 0,
        quantity: 0
      }

      invalidItems.forEach((item) => {
        if (item.includes('未选择供应商')) errorStats.supplier++
        if (item.includes('成本价格')) errorStats.costPrice++
        // if (item.includes('材质')) errorStats.material++
        // if (item.includes('商品编码')) errorStats.code++
        // if (item.includes('商品品牌')) errorStats.brand++
        // if (item.includes('开票品名')) errorStats.invoiceName++
        if (item.includes('数量')) errorStats.quantity++
      })

      // 生成概要错误信息
      const errorSummary: string[] = []
      if (errorStats.supplier > 0) errorSummary.push(`• ${errorStats.supplier} 个商品未选择供应商`)
      if (errorStats.costPrice > 0) errorSummary.push(`• ${errorStats.costPrice} 个商品成本价格有误`)
      // if (errorStats.material > 0) errorSummary.push(`• ${errorStats.material} 个商品材质为空`)
      // if (errorStats.code > 0) errorSummary.push(`• ${errorStats.code} 个商品编码为空`)
      // if (errorStats.brand > 0) errorSummary.push(`• ${errorStats.brand} 个商品品牌为空`)
      // if (errorStats.invoiceName > 0) errorSummary.push(`• ${errorStats.invoiceName} 个商品开票品名为空`)
      if (errorStats.quantity > 0) errorSummary.push(`• ${errorStats.quantity} 个商品数量有误`)

      errorMessage += errorSummary.join('\n')

      // 显示前3个具体错误作为示例
      if (invalidItems.length > 0) {
        errorMessage += '\n\n示例错误：\n' + invalidItems.slice(0, 3).join('\n')
        if (invalidItems.length > 3) {
          errorMessage += `\n... 还有 ${invalidItems.length - 3} 个商品需要检查`
        }
      }
    }

    return Promise.reject(errorMessage)
  }
  return Promise.resolve()
}

export const getDrawerTableColumns = (type: string, currency: string): VxeGridPropTypes.Columns[] => [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    width: 100,
    type: 'expand',
    slots: { content: 'expandContent', header: 'expandHeader' }
  },
  {
    title: '操作',
    field: 'action',
    width: 150,
    // fixed: 'right',
    ifShow: type === 'add',
    slots: { default: 'Action', header: 'ActionHeader' }
  },
  {
    title: '商品名称',
    field: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '产品图片',
    field: 'imgs',
    width: 100,
    resizable: true,
    slots: { default: 'Imgs' }
  },
  {
    title: '描述',
    field: 'desc',
    width: 200,
    resizable: true
  },
  {
    title: '长度(CM)',
    field: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '宽度(CM)',
    field: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '高度(CM)',
    field: 'height',
    width: 100,
    resizable: true
  },
  {
    title: '采购数量',
    field: 'qty_purchased',
    width: 100,
    resizable: true,
    slots: { default: 'QtyPurchased' }
  },
  {
    title: '产品编码',
    field: 'puid',
    width: 150,
    resizable: true
  },
  {
    title: '供应商',
    field: 'supplier_id',
    width: 300,
    editRender: {},
    resizable: true,
    ifShow: !['edit', 'detail'].includes(type),
    slots: { default: 'SupplierId', header: 'SupplierHeader', edit: 'SupplierId' }
  },
  {
    title: '加工商',
    field: 'plant_id',
    width: 300,
    resizable: true,
    slots: { default: 'PlatId', header: 'PlatIdHeader' }
    // ifShow: !['edit', 'detail'].includes(type),
    // edit: true,
    // editRule: true,
    // editComponent: 'ApiSelect',
    // editComponentProps: {
    //   api: getErpSupplier,
    //   selectProps: {
    //     fieldNames: { key: 'key', value: 'id', label: 'name' },
    //     showSearch: true,
    //     placeholder: '请选择'
    //   },
    //   resultField: 'items'
    // }
  },
  // {
  //   title: '采购数量',
  //   field: 'qty_purchased',
  //   width: 100,
  //   resizable: true,
  //   slots: { default: 'QtyPurchased' }
  // },

  {
    title: '外汇单价',
    field: 'foreign_currency_unit_pirce',
    width: 150,
    resizable: true,
    ifShow: !['人民币', 'CNY'].includes(currency),
    slots: { default: 'currencyUnitPirce' }
  },
  {
    title: '含税采购单价',
    field: 'unit_price',
    editRender: {},
    width: 150,
    resizable: true,
    slots: { default: 'UnitPrice', header: 'UnitPriceHeader', edit: 'UnitPrice' }
  },
  {
    title: '采购不含税单价',
    field: 'unit_price_tax',
    width: 150,
    resizable: true,
    slots: { default: 'UnitPriceTax' }
  },
  {
    title: '成本单价',
    field: 'cost_price',
    width: 150,
    editRender: {},
    resizable: true,
    slots: { default: 'CostPrice', edit: 'CostPrice' }
  },
  {
    title: '材质',
    field: 'material',
    width: 150,
    resizable: true,
    ifShow: ['/s/', '/tests/'].includes(pathname),
    slots: {
      header: 'MaterialHeader',
      edit: ({ row }) => <Input v-model:value={row.material} disabled={type === 'detail'}></Input>,
      default: ({ row }) => <Input v-model:value={row.material} disabled={type === 'detail'}></Input>
    }
  },
  {
    title: '海关码',
    field: 'code',
    width: 150,
    ifShow: ['/s/', '/tests/'].includes(pathname),
    resizable: true,
    slots: {
      header: 'CodelHeader',
      edit: ({ row }) => <Input v-model:value={row.code} disabled={type === 'detail'}></Input>,
      default: ({ row }) => <Input v-model:value={row.code} disabled={type === 'detail'}></Input>
    }
  },
  {
    title: '品牌(没有品牌就填写:无品牌)',
    field: 'brand',
    width: 150,
    resizable: true,
    ifShow: ['/s/', '/tests/'].includes(pathname),
    slots: {
      header: 'BrandHeader',
      edit: ({ row }) => <Input v-model:value={row.brand} disabled={type === 'detail'}></Input>,
      default: ({ row }) => <Input v-model:value={row.brand} disabled={type === 'detail'}></Input>
    }
  },
  {
    title: '开票品名',
    field: 'invoice_name',
    width: 150,
    resizable: true,
    ifShow: ['/s/', '/tests/'].includes(pathname),
    slots: {
      header: 'InvoiceHeader',
      edit: ({ row }) => <Input v-model:value={row.invoice_name} disabled={type === 'detail'}></Input>,
      default: ({ row }) => <Input v-model:value={row.invoice_name} disabled={type === 'detail'}></Input>
    }
  },
  {
    title: '采购单位',
    field: 'unit',
    width: 150,
    resizable: true,
    slots: { default: 'Unit' }
  },
  {
    title: '开票税点',
    field: 'tax_point',
    width: 200,
    resizable: true,
    // ifShow: !['edit', 'detail'].includes(type),
    slots: { default: 'TaxPoint' }
  },
  {
    title: '开票加收税点',
    field: 'add_point',
    width: 200,
    resizable: true,
    ifShow: !['edit', 'detail'].includes(type),
    slots: { default: 'AddPoint' }
  },
  {
    title: '亏本原因',
    field: 'lose_remark',
    width: 300,
    resizable: true,
    slots: { default: 'LossReason' }
  },
  {
    title: '亏本备注',
    field: 'loss_reason',
    width: 300,
    resizable: true,
    slots: { default: 'LossRemark' }
  },

  {
    title: '销售单价',
    field: 'unit_sales',
    width: 100,
    resizable: true
    // customRender: ({ text }) => {
    //   return Number(text)
    // }
  },
  {
    title: '订单实际需求数量',
    field: 'qty_request_actual',
    width: 150,
    resizable: true
  },
  {
    title: '已采购数量',
    field: 'purchased_quantity',
    width: 100,
    resizable: true
  },
  {
    title: '剩余收货数量',
    field: 'qty_wait_received',
    width: 100,
    resizable: true,
    ifShow: ['detail'].includes(type)
  },
  {
    title: '成本价',
    field: 'total_cost',
    width: 150,
    resizable: true,
    slots: { default: 'TotalCost' }
    // customRender: ({ record }) => +record.qty_purchased * +record.unit_price
  },
  {
    title: '含税金额',
    field: 'total_cost_tax',
    width: 150,
    resizable: true,
    slots: { default: 'TotalCostTax' }
    // customRender: ({ record }) => +record.qty_purchased * +record.unit_price
  },
  {
    title: '销售价',
    field: 'total_sales',
    width: 100,
    slots: { default: 'TotalSales' }
  },
  {
    title: '产品唯一码',
    field: 'uniqid',
    width: 250,
    resizable: true
  },
  {
    title: '备注',
    field: 'remark',
    width: 120,
    resizable: true
  }
]
//子产品tablecolum
export const tablecolum = (type: string): VxeGridPropTypes.Columns[] => [
  {
    title: '操作',
    field: 'action',
    width: 150,
    // fixed: 'right',
    ifShow: type === 'add',
    slots: { default: 'Action', header: 'ActionHeader' }
  },
  {
    title: 'id',
    field: 'id',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: 'request_id',
    field: 'request_id',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: 'work_id',
    field: 'work_id',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: '产品名称',
    field: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '采购数量',
    field: 'quantity',
    width: 100,
    resizable: true,
    slots: { default: 'Quantity' }
  },
  {
    title: '采购单位',
    field: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '采购单价',
    field: 'unit_price',
    width: 100,
    resizable: true,
    slots: { default: 'UnitPprice' }
  },
  // {
  //   title: '种类占比',
  //   field: 'proportion_org',
  //   width: 100,
  //   resizable: true,
  //   slots: { default: 'proportion_org' }
  // },
  // {
  //   title: '数量比例',
  //   field: 'proportion',
  //   width: 100,
  //   resizable: true,
  //   slots: { default: 'Proportion' }
  // },
  {
    title: '备注',
    field: 'remark',
    width: 200,
    resizable: true
  },
  {
    title: '实际需求数量',
    field: 'quantity_actual',
    width: 200,
    resizable: true
  },
  {
    title: '剩余采购数量',
    field: 'quantity_left',
    width: 200,
    resizable: true
  },
  {
    title: '长度(CM)',
    field: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '宽度(CM)',
    field: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '高度(CM)',
    field: 'height',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    field: 'desc',
    width: 200,
    resizable: true
  },
  {
    title: '图片',
    field: 'imgs',
    width: 100,
    resizable: true,
    slots: { default: 'Imgs' }
  },
  {
    title: '附件',
    field: 'files',
    width: 250,
    resizable: true,
    slots: { default: 'Files' }
  },
  {
    title: 'type',
    field: 'type',
    width: 100,
    resizable: true,
    ifShow: false
  }
]

interface RowFields {
  packing_package_id: number
  packing_package_items_id: number
  quantity_origin: number
  quantity_need: number
  warehouse_id: number
  warehouse_item_id: number
  code: string
  material: string
  brand: string
  invoice_name: string
}

export const validRules = ref<VxeTablePropTypes.EditRules<RowFields>>({
  supplier_id: [{ required: true, message: '必须选择供应商' }],
  unit_price: [{ required: true, message: '必须填写含税采购单价' }],
  cost_price: [{ required: true, message: '必须填写成本单价' }],
  code: [{ required: true, message: '必须填写海关码' }],
  material: [{ required: true, message: '必须填写材质' }],
  brand: [{ required: true, message: '必须填写品牌' }],
  invoice_name: [{ required: true, message: '必须填写开票品名' }]
})
