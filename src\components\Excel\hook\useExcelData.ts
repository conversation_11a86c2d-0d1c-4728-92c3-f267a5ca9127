import * as XLSX from 'xlsx'
import { ref, unref } from 'vue'
// import { isFunction } from '/@/utils/is'
import { indexOf, merge } from 'lodash-es'

export function useExcelData() {
  const rawExcelDataRef = ref<Recordable>({})

  function readData(rawFile: File, opts?: XLSX.ParsingOptions | undefined) {
    const xlsxOpts = merge(
      {
        type: 'array',
        cellDates: true
      },
      opts
    )

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = e.target && e.target.result
          rawExcelDataRef.value = XLSX.read(data, xlsxOpts)
          resolve('')
        } catch (error) {
          reject(error)
        }
      }
      reader.readAsArrayBuffer(rawFile)
    })
  }

  //设置一个cellValueFormatter

  function getDataSource() {
    return unref(rawExcelDataRef)
  }

  function hasSheet(sheetName: string): boolean {
    const sheetNames = getSheetNames()
    return indexOf(sheetNames, sheetName) >= 0
  }

  function getSheetNames(): string[] {
    return unref(rawExcelDataRef).SheetNames ?? []
  }

  function getSheet(sheetName: string): XLSX.WorkSheet {
    return unref(rawExcelDataRef).Sheets[sheetName]
  }

  function getSheetData(worksheet: XLSX.WorkSheet, opts?: XLSX.Sheet2JSONOpts): Recordable[] {
    const sheeOpts = merge(
      {
        raw: true,
        blankrows: false,
        defval: ''
      },
      opts
    )

    //强制要使用有header的模式
    if (!sheeOpts.header) {
      const header = getRowData(worksheet, 1)
      sheeOpts.header = header
    }

    const results = XLSX.utils.sheet_to_json(worksheet, sheeOpts) as object[]

    //将所有value都过一次valueformater
    return results
  }

  function getRowData(sheet: XLSX.WorkSheet, rowIndex: number, endColumn?: string) {
    const rowData: string[] = []
    if (!sheet || !sheet['!ref']) return []

    // A3:B7=>{s:{c:0, r:2}, e:{c:1, r:6}}
    const range = XLSX.utils.decode_range(sheet['!ref'])

    const R = rowIndex - 1
    const endColumnNum = endColumn ? XLSX.utils.decode_col(endColumn) : range.e.c

    for (let C = range.s.c; C <= endColumnNum; ++C) {
      /* walk every column in the range */
      const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
      /* find the cell in the first row */
      let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
      if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
      rowData.push(hdr)
    }
    return rowData
  }

  return {
    readData,
    getDataSource,
    getSheetNames,
    getSheet,
    hasSheet,
    getSheetData,
    getRowData,
    xlsxUtil: XLSX.utils
  }
}
