import { <PERSON>ckMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const inventoryList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      name: '@ctitle',
      'imgs|4': ['@image(1000)'],
      desc1: '@cparagraph',
      warehouse_name: '@cname',
      qty_stocking: '@integer(100, 9999)',
      desc2: '@cparagraph',
      remark: '@cparagraph',
      creator: '@cname',
      created_at: '@datetime'
    })
  }
  return { items: result, total: 50 }
})()

export default [
  {
    url: '/api/inventory/list',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(inventoryList)
    }
  }
  // {
  //   url: '/api/erp/stock/ts/add',
  //   timeout: 1000,
  //   method: 'post',
  //   response: () => {
  //     return resultSuccess({})
  //   }
  // }
] as <PERSON><PERSON><PERSON>ethod[]
