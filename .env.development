# 是否开启mock数据，关闭时需要自行对接后台接口
VITE_USE_MOCK = true

# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = /

# 本地开发代理，可以解决跨域及多地址代理
# 如果接口地址匹配到，则会转发到http://localhost:3000，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
# VITE_PROXY = [["/api","http://localhost:3300/api"],["/upload","http://localhost:3300/upload"]]

# VITE_PROXY = [["/api","https://erp.gbuilderchina.com/api"],["/upload","https://erp.gbuilderchina.com/api/oss/putFile"]]

#VITE_PROXY = [["/api","http://erptest.com/api"],["/upload","http://localhost:3300/upload"]]
#VITE_PROXY = [["/api","http://erp.com/api"],["/updload","http://localhost:3300/upload"]]
# VITE_PROXY = [["/api","http://erpcert.com/api"],["/updload","http://localhost:3300/upload"]]
#
    VITE_PROXY = [["/api","https://erp.gbuilderchina.com/testapi"],["/upload","https://erp.gbuilderchina.com/testapi/oss/putFile"]]

# 测试导入agr
# VITE_PROXY = [["/api","https://erp.gbuilderchina.com/spapi"],["/upload","https://erp.gbuilderchina.com/spapi/oss/putFile"]]


# 是否删除Console.log
VITE_DROP_CONSOLE = false

# 接口地址
# 如果没有跨域问题，直接在这里配置即可
VITE_GLOB_API_URL = /api

# 文件上传接口  可选
VITE_GLOB_UPLOAD_URL = /upload

# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX =

# 环境类型标识 (production: 正式环境, test: 测试环境)
VITE_ENV_TYPE = 'test'
