import { defHttp } from '/@/utils/http/axios'

enum Api {
  invoiceupdate = '/erp/finance/invoice/update',
  invoicegetList = '/erp/finance/invoice/getList',
  invoicesetStatus = '/erp/finance/invoice/setStatus',
  invoicesetIsCancel = '/erp/finance/invoice/setIsCancel',
  invoicedetail = '/erp/finance/invoice/detail',
  invoicebind = '/erp/finance/invoice/bind',
  invoicesetCheckStatus = '/erp/finance/invoice/setCheckStatus',
  invoicebindDetail = '/erp/finance/invoice/bindDetail',
  invoicegetSelPurStrid = '/erp/finance/invoice/getSelPurStrid',
  invoicegetSelNumber = '/erp/finance/invoice/getSelNumber'
}

// 获取
export const invoiceupdate = (params?: {}) =>
  defHttp.post({ url: Api.invoiceupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const invoicegetList = (params?: {}) => defHttp.get({ url: Api.invoicegetList, params })
export const invoicesetStatus = (params?: {}) =>
  defHttp.post({ url: Api.invoicesetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const invoicesetCheckStatus = (params?: {}) =>
  defHttp.post({ url: Api.invoicesetCheckStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const invoicesetIsCancel = (params?: {}) =>
  defHttp.post({ url: Api.invoicesetIsCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const invoicedetail = (params?: {}) => defHttp.get({ url: Api.invoicedetail, params })
export const invoicebind = (params?: {}) =>
  defHttp.post({ url: Api.invoicebind, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const invoicebindDetail = (params?: {}) => defHttp.get({ url: Api.invoicebindDetail, params })
export const invoicegetSelPurStrid = (params?: {}) => defHttp.get({ url: Api.invoicegetSelPurStrid, params })
export const invoicegetSelNumber = (params?: {}) => defHttp.get({ url: Api.invoicegetSelNumber, params })
