<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable" class="p-4" @fetch-success="onFetchSuccess">
      <template #form-InCharges="{ model }">
        <div style="display: flex; align-items: center">
          <Cascader
            v-model:value="model.inCharges"
            @focus="cascaderfocus"
            :options="optionsarr"
            :fieldNames="{ value: 'id', label: 'name', children: 'children' }"
            multiple
            :maxTagCount="4"
            :load-Data="loadData"
            @change="handleChange"
            change-on-select
            placeholder="请选择"
          >
            <template #tagRender="data">
              <Tag :key="data.value" color="blue">{{ data.label }}</Tag>
            </template></Cascader
          >
        </div>
      </template>
      <template #toolbar>
        <a-button type="primary" @click.stop="handleExpenOrCollect">一键{{ isExpendAll ? '收起' : '展开' }}项目</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div class="flex items-center py-3">
            <TableAction :actions="createActions(record)" />
            <Badge :count="record.deliver_num" :numberStyle="{}" v-if="hasPermission([700])">
              <a-button
                size="small"
                type="text"
                class="ml-2"
                @click.stop="handleDeliverLog('project', record[propertyConst.PROJECTNUMBER])"
              >
                交付日期变更日志
              </a-button>
            </Badge>
            <TableAction class="ml-2" :drop-down-actions="createDropDownActions(record)" />
          </div>
        </template>
      </template>
      <template #expandedRowRender="{ record: { work } }">
        <BasicTable
          :dataSource="work"
          :columns="saleColumns"
          :="{
            canResize: false,
            rowKey: 'id',
            pagination: false,
            showIndexColumn: false,
            actionColumn: {
              width: 260,
              title: '操作',
              dataIndex: 'action',
              fixed: 'right'
            }
          }"
          class="p-4"
        >
          <template #bodyCell="{ column: saleColumn, record: saleRecord }">
            <template v-if="saleColumn.dataIndex === 'action'">
              <div class="flex items-center py-3">
                <a-button type="primary" size="small" class="ml-2" @click.stop="handleUpdateDeliver(saleRecord)" v-if="hasPermission([697])"
                  >设置交付日期
                </a-button>
                <Badge :count="saleRecord.deliver_num" :numberStyle="{}" v-if="hasPermission([700])">
                  <a-button size="small" class="ml-2" @click.stop="handleDeliverLog('sale', saleRecord.id)">交付日期变更日志</a-button>
                </Badge>
              </div>
            </template>
          </template>
          <template #expandedRowRender="{ record: saleRecord }">
            <BasicTable
              v-bind="{
                api: getSalesOrderListReq,
                rowKey: 'id',
                pagination: { size: 'small' },
                showIndexColumn: false,
                canResize: false,
                scroll: { y: 500 },
                columns: productColumns,
                isTreeTable: true,
                searchInfo: { work_id: saleRecord.id }
              }"
            >
              <template #toolbar>
                <div class="progress-bar">
                  <div class="left">
                    <div class="status">
                      <a-button
                        type="link"
                        @click.stop="onViewSaleDetail(saleRecord)"
                        class="status"
                        :style="{ color: saleRecord.status === 15 ? '#999' : '', margin: 0, padding: 0 }"
                        >{{ saleRecord.source_uniqid }}</a-button
                      >
                      <span :style="{ color: allStatus[saleRecord.status].color, marginLeft: '10px' }">
                        {{ allStatus[saleRecord.status].label }}</span
                      >
                    </div>
                    <div class="time">预计交货日期: {{ saleRecord.delivery_at }}</div>
                  </div>
                  <div class="right" v-if="![15, 16].includes(saleRecord.status)">
                    <Steps :current="mapStatus[saleRecord.status].index" :status="mapStatus[saleRecord.status].status" progressDot>
                      <template v-for="(toolItem, index) in baseStatusArray" :key="toolItem">
                        <Step :title="toolItem">
                          <template #description v-if="mapStatus[saleRecord.status].index >= index">
                            {{ saleRecord[mapStatus[index].description] }}
                          </template>
                        </Step>
                      </template>
                    </Steps>
                  </div>
                </div>
              </template>
              <template #bodyCell="{ column: productColumn, record: productRecord }">
                <template v-if="productColumn.dataIndex === 'product'">
                  <div class="product">
                    <TableImg :size="60" :simpleShow="true" :imgList="productRecord.imgs" :margin="1" />
                    <div class="product-info">
                      <div class="name">{{ productRecord.name }}</div>
                      <div class="puid">产品编号: {{ productRecord.puid }}</div>
                    </div>
                  </div>
                </template>
              </template>
              <template #expandedRowRender="{ record: productRecord }">
                <BasicTable
                  v-bind="{
                    api: getProgressTracking,
                    columns: productStatusColumns,
                    showIndexColumn: false,
                    pagination: false,
                    canResize: false,
                    searchInfo: { request_id: productRecord.id }
                  }"
                />
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <ViewBooking @register="registerViewBookingModal" />
    <BookingDrawer @register="registerBookingDrawer" @success="reload" />
    <RevisitDrawer @register="registerDrawer" @success="reload" />
    <LinkDrawer @register="registerLinkDrawer" />
    <UpdateAtModal @register="registerUpdateAtModal" @success="reload" />
    <!-- TODO 直接reload实在太消耗性能了,到时候改成更新一行那种 -->
    <ApplyDelayLogModal @register="registerApplyModal" @reload="reload" />
    <GenderAccountModal @register="registerGenderAccountModal" />
    <VerifyDrawer @register="registerSaleDetailDrawer" />
    <VerifyDrawer @register="registerSaleDetailDrawer" />
    <RevisitLogDrawer @register="registerRevisitLogDrawer" />
  </div>
</template>
<script setup lang="ts" name="/revisitManage/revisitLog">
import { ref, nextTick } from 'vue'
import { Steps, Step, message, Badge, Tag, Cascader } from 'ant-design-vue'
import dayjs from 'dayjs'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import type { EditRecordRow, ActionItem } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { useModal } from '/@/components/Modal'
import { getProjectList, getDeliveryLogList } from '/@/api/revisit/index'
import { getSalesOrderListReq } from '/@/api/erp/sales'
import { getProgressTracking } from '/@/api/erp/progressTracking'
import { usePermission } from '/@/hooks/web/usePermission'
import VerifyDrawer from '/@/views/erp/saleOrder/components/VerifyDrawer.vue'
import RevisitDrawer from './components/RevisitDrawer.vue'
import RevisitLogDrawer from './components/RevisitLogDrawer.vue'
import LinkDrawer from './components/LinkDrawer.vue'
import UpdateAtModal from './components/UpdateAtModal.vue'
import ApplyDelayLogModal from './components/ApplyDelayLogModal.vue'
import * as propertyConst from './datas/const'
import { columns, schemas, productStatusColumns, productColumns, mapStatus, allStatus, baseStatusArray, saleColumns } from './datas/datas'
import GenderAccountModal from './components/GenderAccountModal.vue'
import BookingDrawer from '/@/views/erp/bookOutWarehouse/components/bookingDrawer.vue'
import ViewBooking from '/@/views/revisitManage/revisitLog/components/ViewBooking.vue'
import { isEmpty } from 'lodash-es'
import { getDeptTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/baseData/staff'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const activeKey = ref([])
const pageSearchInfo = ref({})
const InChargesarr = ref<any>([])
const deptarray = ref<any>([])

// const formdapt = ref(null)

pageSearchInfo.value = {}
if (window.history.state?.searchParams) {
  pageSearchInfo.value = window.history.state.searchParams
}
const isExpendAll = ref(true)

const [registerViewBookingModal, { openModal: openViewBookingModal }] = useModal()
const [registerBookingDrawer, { openDrawer: openBookingDrawer, setDrawerProps: setBookingDrawerProps }] = useDrawer()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerLinkDrawer, { openDrawer: openLinkDrawer }] = useDrawer()
const [registerUpdateAtModal, { openModal, setModalProps }] = useModal()
const [registerApplyModal, { openModal: openApplyModal, setModalProps: setApplyModalProps }] = useModal()
const [registerGenderAccountModal, { openModal: openGenderAccountModal }] = useModal()
const [registerSaleDetailDrawer, { openDrawer: openSaleDetailDrawer, setDrawerProps: setSaleDetailDrawerProps }] = useDrawer()
const [registerRevisitLogDrawer, { openDrawer: openRevisitLogDrawer, setDrawerProps: setRevisitLogDrawerProps }] = useDrawer()
const [registerTable, { reload, expandAll, setLoading, collapseAll, getForm }] = useTable({
  title: '回访记录',
  api: getProjectList,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    autoAdvancedLine: 1,
    fieldMapToTime: [
      ['follow_up_at', ['follow_up_at_start', 'follow_up_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },

  isTreeTable: true,
  actionColumn: {
    width: 400,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  showTableSetting: true,
  beforeFetch: async (params) => {
    let pageParams = {}
    if (!isEmpty(pageSearchInfo.value)) {
      const form = getForm()
      pageParams = {
        ...pageSearchInfo.value
      }
      await form.setFieldsValue(pageParams)
      pageSearchInfo.value = {}
    }
    if (params.inCharges) {
      params.inCharges = InChargesarr.value
    }
    return {
      ...params,
      ...pageParams
    }
  }
})

function onFetchSuccess() {
  // const cloneActiveKey = activeKey.value
  activeKey.value = []
  nextTick(() => (isExpendAll.value ? expandAll() : collapseAll()))

  //保留原来的展开的话,每次点击查询将会非常卡顿
  // nextTick(() => {
  //   activeKey.value = cloneActiveKey
  // })
}

function handleExpenOrCollect() {
  isExpendAll.value = !isExpendAll.value
  isExpendAll.value ? expandAll() : collapseAll()
}

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '更新回访日期',
      onClick: () => {
        setModalProps({ title: '更新回访日期' })
        openModal(true, { id: record.id, type: propertyConst.REVISITATLABLE, follow_up_at: record.follow_up_at ?? dayjs() })
      },
      disabled: record.follow_up_at ? new Date(record.follow_up_at).getTime() > new Date().getTime() : true,
      color: record.follow_up_at ? (new Date(record.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning',
      ifShow: hasPermission([695])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      ifShow: hasPermission([696])
    }
  ]
}

function onViewSaleDetail(item) {
  setSaleDetailDrawerProps({ title: '销售详情' })
  openSaleDetailDrawer(true, item)
}

function handleEdit(record: EditRecordRow) {
  setDrawerProps({ title: '编辑' })
  openDrawer(true, { record, type: 'edit' })
}

async function handleUpdateDeliver(item) {
  setModalProps({ title: '更新交付日期' })
  openModal(true, { id: item.id, type: propertyConst.DELIVERATLABLE, work_id: item.id })
}

async function handleDeliverLog(type, code) {
  try {
    await setLoading(true)
    const { items } = await getDeliveryLogList(type === 'project' ? { project_number: code } : { work_id: code })
    setApplyModalProps({ title: '交付日期变更日志列表' })
    await setLoading(false)
    if (items && items.length > 0) {
      openApplyModal(true, { type, code, items })
      // setIsRead(type === 'project' ? { project_number: code } : { sale_work_id: code })
    } else {
      message.warning('暂无交付日期变更日志')
    }
  } catch (err) {
    await setLoading(false)
    console.error(err)
  }
}

function createDropDownActions(record) {
  return [
    {
      // icon: 'clarity:note-edit-line',
      label: '客户账号',
      onClick: handleOpenAccount.bind(null, record),
      disabled: !record.client_id,
      ifShow: hasPermission([698])
    },
    {
      icon: 'ant-design:link-outlined',
      label: '话术',
      onClick: handleLink.bind(null, record),
      ifShow: hasPermission([699])
    },
    {
      icon: 'ant-design:edit-outlined',
      label: '创建回访日志',
      onClick: handleRevisitLog.bind(null, 'create', record),
      ifShow: hasPermission([701])
    },
    {
      icon: 'ant-design:eye-outlined',
      label: '查看回访日志',
      onClick: handleRevisitLog.bind(null, 'view', record),
      ifShow: hasPermission([702])
    },
    {
      // icon: '',
      label: '新增预约出库',
      onClick: handleBookWarehouse.bind(null, record)
    },
    {
      label: '查看相关出库预约',
      onClick: handleViewBooking.bind(null, record)
    }
  ]
}

function handleOpenAccount(record) {
  openGenderAccountModal(true, { record })
}

function handleLink(record) {
  openLinkDrawer(true, record)
}

function handleRevisitLog(type: 'create' | 'view', record) {
  if (type === 'create') {
    setRevisitLogDrawerProps({ title: '创建回访日志' })
  } else {
    setRevisitLogDrawerProps({ title: '查看回访日志' })
  }
  openRevisitLogDrawer(true, { type, record })
}

function handleBookWarehouse(record) {
  setBookingDrawerProps({ title: '新建预约', showFooter: true })
  openBookingDrawer(true, { type: 'add', revisitRecord: record })
}

function handleViewBooking(record) {
  openViewBookingModal(true, { record })
}
const optionsarr = ref<any>([])
async function cascaderfocus() {
  if (optionsarr.value.length === 0) {
    optionsarr.value = await getDeptTree()
  }
}
async function loadData(params) {
  const targetOption = params[params.length - 1]
  console.log(targetOption)
  targetOption.loading = true
  targetOption.loading = false
  targetOption?.children.forEach(async (item) => {
    if (item.children.length === 0) {
      item.isLeaf = false
      const a = await getStaffList({ dept_id: item.id })
      item.children = a.items.map((item) => {
        return {
          ...item,
          label: item.name,
          value: item.id
        }
      })
    }
  })
  return (optionsarr.value = [...optionsarr.value])
}
function handleChange(value, selectedOptions) {
  console.log(selectedOptions, value)

  InChargesarr.value = []
  deptarray.value = []
  if (selectedOptions.length > 0) {
    traverseNodes([selectedOptions[0][selectedOptions[0].length - 1]])
    deptarray.value.forEach(async (item) => {
      const a = await getStaffList({ dept_id: item })
      InChargesarr.value.push(...a.items.map((item) => item.id))
    })
  }
  console.log(InChargesarr.value)

  return InChargesarr.value
}

function traverseNodes(nodes) {
  nodes.forEach((node) => {
    if (node.children) {
      deptarray.value.push(node.id) // 只有当节点有子节点时才添加其ID
      traverseNodes(node.children) // 递归处理子节点
    } else {
      InChargesarr.value.push(node.id)
    }
  })
}
</script>

<style lang="less" scoped>
.progress-bar {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 20px;

  .left {
    .status {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 6px;
    }

    .time {
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 6px;
    }
  }

  .right {
    margin-left: 200px;
  }
}

.product {
  display: flex;
  align-items: center;

  .vben-basic-table-img.flex.items-center.mx-auto {
    margin-left: 0;
    margin-right: 8px;
  }

  .name {
    font-weight: 700;
    margin-bottom: 6px;
  }

  .puid {
    color: #999;
    font-size: 13px;
  }
}

.sale-list-right {
  //width: 365px;
  box-sizing: border-box;
}
</style>
