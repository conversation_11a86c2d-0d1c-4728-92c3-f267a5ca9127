import { defHttp } from '/@/utils/http/axios'

enum Api {
  postupdate = '/erp/finance/ac/update',
  getList = '/erp/finance/ac/getList',
  //审核
  setStatus = '/erp/finance/ac/setStatus'
}
//新增预付款
export const postupdate = (params: {}) => defHttp.post({ url: Api.postupdate, params })
export const getList = (params: {}) => defHttp.get({ url: Api.getList, params })

export const setStatus = (params: {}) =>
  defHttp.post({ url: Api.setStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
