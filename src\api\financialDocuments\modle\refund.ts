export interface FundManageListParams {
  creat_at_end?: string // 创建结束时间
  created_at_start?: string // 创建开始时间
  page?: number // 页数
  pageSize?: number // 每页条数
  status?: number // 状态：0-未审核 1-已审核
  strid?: string // 单号
  type?: number // 退款类型：1-退款  2-不退款
  [property: string]: any
}

export interface FundManageItems {
  amount: number // 退款金额
  auditor: number // 审核人id
  auditor_name: string // 审核人名称
  client_id: number // 客户id
  client_name: string // 客户名称
  created_at: string
  creator: number // 创建人id
  creator_name: string // 创建人名称
  id: number // 退款id
  status: number // 状态：0-未审核 1-已审核
  strid: string
  type: number // 退款类型：1-退款  2-不退款
  updated_at: string
  work_id: number // 任务id
  [property: string]: any
}

export interface CreateRefundParams {
  amount: number // 退款总金额
  fdocLists: Array<{ id: number }> // 款项ids
  type: number // 退款类型：1-退款  2-不退款
  work_id: number // 销售任务id
  [property: string]: any
}
