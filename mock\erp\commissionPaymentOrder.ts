import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const commissionPaymentOrderList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      payment_date: '@date',
      order_no: '@guid',
      'fund_return|1-1000': 100,
      remarks: '@ctitle'
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/commissionPaymentOrderList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(commissionPaymentOrderList)
    }
  }
] as MockMethod[]
