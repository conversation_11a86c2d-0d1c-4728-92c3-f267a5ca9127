import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

const types = {
  10: { text: '菜品', color: '#409EFF' },
  20: { text: '套餐', color: '#67C23A' },
  40: { text: '加料', color: '#E6A23C' },
  50: { text: '餐盒', color: '#F56C6C' }
}

export const columns: BasicColumn[] = [
  {
    title: '菜品编码',
    dataIndex: 'code',
    width: 150,
    resizable: true
  },
  {
    title: '菜品名称',
    dataIndex: 'name',
    width: 250,
    resizable: true
  },
  {
    title: '菜品分类类型',
    dataIndex: 'type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: types[text].color }, types[text].text)
    }
  },
  {
    title: '菜品部门',
    dataIndex: 'department',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text ? text : '-'
    }
  },
  {
    title: '菜品分类名称',
    dataIndex: 'small_category',
    width: 150,
    resizable: true
  },
  {
    title: '菜品上级分类名称',
    dataIndex: 'big_category',
    width: 150,
    resizable: true
  },
  {
    title: '菜品单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true
  },
  {
    title: '菜品价格',
    dataIndex: 'price',
    width: 150,
    resizable: true
  },
  {
    title: '菜品下发类型',
    dataIndex: 'p_type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text == 1 ? '总部下发' : '外卖'
    }
  },
  {
    title: '菜品图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 150,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '菜品名称',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'type',
    label: '菜品类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(types).map((key) => {
        return {
          label: types[key].text,
          value: key
        }
      })
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'code',
    label: '菜品编码',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'small_category',
    label: '菜品分类名称',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'dept_id',
    label: '菜品部门',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '空中餐厅',
          value: 173
        },
        {
          label: 'SAWA餐厅',
          value: 174
        },
        {
          label: '麟料理餐厅',
          value: 175
        },
        {
          label: '江南餐厅',
          value: 176
        },
        {
          label: '法餐厅',
          value: 177
        },
        {
          label: '甜点酒水',
          value: 178
        },
        {
          label: '粤棠餐厅',
          value: 189
        }
      ]
    },
    colProps: {
      span: 6
    }
  }
]
