import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { otherIncome } from './modle/types'

enum Api {
  //列表
  getOtherIncomeList = '/erp/stock/or/getList',
  CreateOtherIncome = '/erp/stock/or/add',
  //客户信息
  getcustomerList = '/erp/si/getCustomer',
  //work下拉
  getgetRepList = '/erp/stock/or/getRepList',
  //科目信息下拉
  EditOtherIncome = '/erp/stock/or/update',
  //删除
  deleteOtherIncome = '/erp/stock/or/remove',
  getCategory = '/erp/si/getCategory',
  //详情
  detailsOtherIncome = '/erp/stock/orn/details',
  /********************/
  //创建接口
  EditOtherupdate = '/erp/stock/orn/update',
  //列表
  GetOthergetList = '/erp/stock/orn/getList',
  //详情
  DetailsOtherdetails = '/erp/stock/orn/details',
  //单据审核
  getsetStatus = '/erp/stock/orn/setStatus',
  //删除
  DeleteOtherremove = '/erp/stock/orn/remove',
  //明细列表
  getdetailList = '/erp/stock/orns/getList',
  //excel导出
  getdetailexport = '/erp/stock/orns/export',
  //批量生成其他收入(并审核)
  BatchCreateOtherIncome = '/erp/stock/orn/addOtherReceipts',
  stockornsetCorresPondent = '/erp/stock/orn/setCorresPondent'
}
export const getOtherIncomeList = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.getOtherIncomeList, params })
export const createOtherIncome = (params?: {}) => defHttp.post<BasicFetchResult<otherIncome>>({ url: Api.CreateOtherIncome, params })
export const getcustomerList = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.getcustomerList, params })
export const getgetRepList = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.getgetRepList, params })
export const getCategory = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.getCategory, params })
export const detailsOtherIncome = (params?: {}) => defHttp.get({ url: Api.detailsOtherIncome, params })
export const editOtherIncome = (params?: {}) => defHttp.post<BasicFetchResult<otherIncome>>({ url: Api.EditOtherIncome, params })
export const deleteOtherIncome = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.deleteOtherIncome, params })

/*******************/

export const editOtherupdate = (params?: {}) => defHttp.post({ url: Api.EditOtherupdate, params })
export const getOthergetList = (params?: {}) => defHttp.get({ url: Api.GetOthergetList, params })
export const detailsOtherdetails = (params?: {}) => defHttp.get({ url: Api.DetailsOtherdetails, params })
export const getsetStatus = (params?: {}) => defHttp.get({ url: Api.getsetStatus, params })
export const deleteOtherremove = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.DeleteOtherremove, params })
export const getdetailList = (params?: {}) => defHttp.get<BasicFetchResult<otherIncome>>({ url: Api.getdetailList, params })
export const getdetailexport = (istree, params) =>
  defHttp.get({ url: Api.getdetailexport, params, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })

export const batchCreateOtherIncome = (params: Recordable) =>
  defHttp.post({ url: Api.BatchCreateOtherIncome, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const stockornsetCorresPondent = (params: Recordable) =>
  defHttp.post({ url: Api.stockornsetCorresPondent, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
