import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { h, ref } from 'vue'
import { message, Tag } from 'ant-design-vue'
import { isNull, isUndefined } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
const saleStore = useSaleOrderStore()
import { CopyOutlined } from '@ant-design/icons-vue'

export const mapAudit = {
  0: { label: '未结算', color: '' },
  1: { label: '已结算', color: 'green' },
  2: { label: '待结算', color: 'skyblue' }
}

export function columns(handFn: Function): any {
  const coumns: BasicColumn[] = [
    {
      title: '销售订单号',
      dataIndex: 'source_uniqid',
      width: 200,
      resizable: true,
      customRender: ({ text, record }) => {
        return h(
          'div',
          {
            onClick: () => {
              handFn(record)
            },
            style: {
              color: 'red',
              cursor: 'pointer',
              marginRight: '8px',
              textAlign: 'center' // 文本居中
            }
          },
          [
            text,
            h(
              'span',
              {
                onClick: (event) => {
                  // 阻止事件冒泡，避免触发父元素的点击事件
                  event.stopPropagation()
                  // 复制文本到剪贴板
                  if (text) {
                    navigator.clipboard
                      .writeText(text)
                      .then(() => {
                        message.success('复制成功')
                      })
                      .catch(() => {
                        message.error('复制失败')
                      })
                  }
                },
                style: {
                  cursor: 'pointer',
                  marginLeft: '5px',
                  color: '#1890ff'
                },
                title: '复制'
              },
              [h(CopyOutlined)]
            )
          ]
        )
      }
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ value }) => {
        // return h(Tag, { color: saleStore.statusColor[record.status] }, () => saleStore.saleStatus[record.status])
        return isNullOrUnDef(value) ? '-' : useRender.renderTag(saleStore.saleStatus[value], saleStore.statusColor[value])
      }
    },
    {
      title: '结算状态',
      dataIndex: 'is_audit',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
        // return mapStatus[record.status]?.label
      }
    },
    {
      title: '改单后金额',
      dataIndex: 'receivable_left',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '实收金额',
      dataIndex: 'received_actual',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '已采购销售金额',
      dataIndex: 'purSaleAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '未采购销售金额',
      dataIndex: 'noPurSaleAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '已采购采购金额',
      dataIndex: 'purchaseTotalPrice',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '采购实付金额',
      dataIndex: 'purchasePaidActual',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '采购未付金额',
      dataIndex: 'purchaseNoPay',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '预估毛利',
      dataIndex: 'estimateGrossProfits',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : value
      }
    },
    {
      title: '其他支出总金额',
      dataIndex: 'otherDisburseAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '其他支出未付总金额',
      dataIndex: 'otherNoPayAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '其他收入总金额',
      dataIndex: 'otherReceiptAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '其他收入未收总金额',
      dataIndex: 'otherNoReceiptAmount',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '预估利润',
      dataIndex: 'estimateProfits',
      width: 150,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    }
  ]
  return coumns
}

const paymenttype = {
  1: { text: '定金', color: '#108ee9' },
  2: { text: '尾款', color: '#2d8cf0' },
  3: { text: '全款', color: '#87d068' }
}

const isfinish = {
  1: { text: '已收款', color: 'green' },
  2: { text: '未收款', color: 'red' },
  3: { text: '已付款', color: 'orange' },
  4: { text: '未付款', color: 'blue' }
}

export function childercolumns(handFn: Function): any {
  const columnsFn: BasicColumn[] = [
    {
      title: '单据单号',
      dataIndex: 'strid',
      width: 130,
      resizable: true,
      customRender: ({ text, record }) => {
        return h(
          'div',
          {
            onClick: () => {
              handFn(record)
            },
            style: {
              color: 'red',
              cursor: 'pointer',
              marginRight: '8px',
              textAlign: 'center' // 文本居中
            }
          },
          [
            text,
            h(
              'span',
              {
                onClick: (event) => {
                  // 阻止事件冒泡，避免触发父元素的点击事件
                  event.stopPropagation()
                  // 复制文本到剪贴板
                  if (text) {
                    navigator.clipboard
                      .writeText(text)
                      .then(() => {
                        message.success('复制成功')
                      })
                      .catch(() => {
                        message.error('复制失败')
                      })
                  }
                },
                style: {
                  cursor: 'pointer',
                  marginLeft: '5px',
                  color: '#1890ff'
                },
                title: '复制'
              },
              [h(CopyOutlined)]
            )
          ]
        )
      }
    },
    {
      title: '单据类型',
      dataIndex: 'type',
      width: 100,
      resizable: true
    },
    {
      title: '金额类型',
      dataIndex: 'payment_type',
      width: 100,
      resizable: true,
      customRender({ text }) {
        return isNullOrUnDef(text) ? '-' : h(Tag, { color: paymenttype[text].color }, () => paymenttype[text].text)
      }
    },
    {
      title: '款单是否已完成',
      dataIndex: 'is_finish',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        const income = ref()
        if (record.is_finish == 1) {
          income.value = record.is_income == 1 ? 1 : 3
        } else {
          income.value = record.is_income == 1 ? 2 : 4
        }

        return isNullOrUnDef(text) ? '-' : h(Tag, { color: isfinish[income.value].color }, () => isfinish[income.value].text)
      }
    },
    {
      title: '明细单号',
      dataIndex: 'mx_strid',
      width: 130,
      resizable: true,
      customRender: ({ text, record }) => {
        return h(
          'div',
          {
            onClick: () => {
              handFn(record, 'mx')
            },
            style: {
              color: 'red',
              cursor: 'pointer',
              marginRight: '8px',
              textAlign: 'center' // 文本居中
            }
          },
          [
            text,
            h(
              'span',
              {
                onClick: (event) => {
                  // 阻止事件冒泡，避免触发父元素的点击事件
                  event.stopPropagation()
                  // 复制文本到剪贴板
                  if (text) {
                    navigator.clipboard
                      .writeText(text)
                      .then(() => {
                        message.success('复制成功')
                      })
                      .catch(() => {
                        message.error('复制失败')
                      })
                  }
                },
                style: {
                  cursor: 'pointer',
                  marginLeft: '5px',
                  color: '#1890ff'
                },
                title: '复制'
              },
              [h(CopyOutlined)]
            )
          ]
        )
      }
    },
    {
      title: '部门名称',
      dataIndex: 'department',
      width: 100,
      resizable: true
    },
    {
      title: '供应商名称',
      dataIndex: 'supplier_name',
      width: 100,
      resizable: true
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true
    }
  ]
  return columnsFn
}

export const schemas: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input',
    colProps: {
      span: 6
    }
  }
]
