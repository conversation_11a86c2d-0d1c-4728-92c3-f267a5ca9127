<template>
  <BasicDrawer @register="register" width="90%">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="expandAll">展开全部</Button>
        <Button type="primary" @click="collapseAll">收起全部</Button>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable @register="registerChildTable" :api="(params) => getProjectSalesRequest({ ...params, id: record.id })">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="record.imgs" :simpleShow="true" />
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { nextTick } from 'vue'
import { getProjectSalesOrder, getProjectSalesRequest } from '/@/api/projectOverview'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { Button } from 'ant-design-vue'

const [register] = useDrawerInner(async (data) => {
  const res = await getProjectSalesOrder({ id: data.record.project_number })
  setTableData(res.items)
  nextTick(() => expandAll())
})
const [registerTable, { setTableData, expandAll, collapseAll }] = useTable({
  showIndexColumn: false,
  isTreeTable: true,
  // api: getProjectSalesOrder,
  columns: [
    {
      title: '销售单号',
      dataIndex: 'source_uniqid',
      width: 120,
      resizable: true
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 120,
      resizable: true
    },
    {
      title: '贸易方式',
      dataIndex: 'trade_methods',
      width: 120,
      resizable: true
    },
    {
      title: '项目经理',
      dataIndex: 'inCharge_name',
      width: 150,
      resizable: true
    },
    {
      title: '交付经理',
      dataIndex: 'delivery_incharge_name',
      width: 150,
      resizable: true
    },
    {
      title: '方案经理',
      dataIndex: 'program_incharges_name',
      width: 150,
      resizable: true,
      customRender: ({ text }) => {
        return text ? useRender.renderTags(text) : '-'
      }
    }
  ],
  canResize: false
})

const [registerChildTable] = useTable({
  columns: [
    {
      title: '商品名称',
      dataIndex: 'name',
      width: 120,
      resizable: true
    },
    {
      title: '商品图片',
      dataIndex: 'imgs',
      width: 80,
      resizable: true
    },
    {
      title: '商品描述',
      dataIndex: 'desc',
      width: 80,
      resizable: true
    },
    {
      title: '实际需求数量',
      dataIndex: 'qty_request_actual',
      width: 80,
      resizable: true
    }
  ],
  showIndexColumn: false,
  // pagination: false,
  canResize: false
})
</script>
