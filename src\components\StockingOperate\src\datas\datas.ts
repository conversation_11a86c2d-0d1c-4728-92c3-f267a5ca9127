import { BasicColumn, FormSchema } from '/@/components/Table'
import { computed, h, ref } from 'vue'
import { Image } from 'ant-design-vue'
import OutWarehouse from '../components/OutWarehouse.vue'
import StockingTransform from '../components/StockingTransform.vue'
import { getStoreList } from '/@/api/commonUtils'

import { getDeptTree } from '/@/api/admin/dept'
// import { getSalesOrderList } from '/@/api/erp/sales'

const storeList = ref<{ id: number; name: string }[]>([])
const mapStoreList = computed(() => {
  const mapList = {}
  for (const item of storeList.value) {
    mapList[item.id] = item.name
  }
  return mapList
})

export const mapStep = {
  addOutWarehouse: [
    {
      title: '选择需要操作的库存',
      value: 0,
      component: null
    },
    {
      title: '填写出库信息',
      value: 1,
      component: OutWarehouse
    }
  ],
  addTransform: [
    {
      title: '选择需要操作的库存',
      value: 0,
      component: null
    },
    {
      title: '填写转换信息',
      value: 1,
      component: StockingTransform
    }
  ]
}

export const columns: BasicColumn[] = [
  {
    title: '产品ID',
    dataIndex: 'id',
    width: 100,
    resizable: true
  },
  {
    title: '库存名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '商品编号',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '关联销售任务号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    width: 200,
    resizable: true,
    customRender: ({ record }) => {
      return h(Image, { src: record.imgs })
    }
  },
  {
    title: '入库日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '库存数量',
    dataIndex: 'qty_stocking',
    width: 100,
    resizable: true
  },
  // {
  //   title: '备货数量',
  //   dataIndex: 'qty_reserve',
  //   resizable: true
  // },
  {
    title: '库存单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true
  },
  {
    title: '商品部门',
    dataIndex: 'origin_department',
    width: 200
  },
  {
    title: '入库来源',
    dataIndex: 'src',
    width: 200,
    customRender: ({ record }) => mapSrc[record.src],
    resizable: true
  },
  {
    title: '生产货期',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '所在仓库',
    dataIndex: 'warehouse_id',
    width: 100,
    customRender: ({ record }) => mapStoreList.value[record.warehouse_id],
    resizable: true
  }
]

export const searchFormSchemas: FormSchema[] = [
  {
    field: 'name',
    label: '商品名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'puid',
    label: '商品编号',
    component: 'Input',
    colProps: { span: 8 }
  },
  // {
  //   field: 'desc',
  //   label: '搜索描述',
  //   component: 'Input',
  //   colProps: { span: 8 }
  // },
  // {
  //   field: 'remark',
  //   label: '搜索备注',
  //   component: 'Input',
  //   colProps: { span: 8 }
  // },
  // {
  //   field: 'created_at',
  //   label: '搜索创建时间',
  //   component: 'RangePicker',
  //   colProps: { span: 8 },
  //   componentProps: {
  //     style: {
  //       width: '100%'
  //     }
  //   }
  // },
  {
    field: 'source_uniqid',
    label: '关联销售任务号',
    component: 'Input',
    colProps: { span: 8 }
    // componentProps: {
    //   api: getSalesOrderList,
    //   resultField: 'items',
    //   selectProps: {
    //     fieldNames: { value: 'id', label: 'source_uniqid' },
    //     showSearch: true,
    //     placeholder: '请选择',
    //     optionFilterProp: 'source_uniqid',
    //     allowClear: true
    //   }
    // }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: { span: 8 }
  }
]

export const mapSrc = {
  1: '采购入库',
  2: '转换入库',
  3: '盘点入库',
  4: '直接入库'
}

export async function getMapStoreList() {
  const { items } = await getStoreList()
  storeList.value = items
  console.log(storeList.value)
}
