import { defHttp } from '/@/utils/http/axios'

enum Api {
  scrapgetList = '/erp/stock/scrap/getList',
  scrapupdate = '/erp/stock/scrap/update',
  scrapsetIsCancel = '/erp/stock/scrap/setIsCancel',
  scrapsetStatus = '/erp/stock/scrap/setStatus'
}

export const scrapgetList = (params?: any) => defHttp.get({ url: Api.scrapgetList, params })
export const scrapupdate = (params?: any) =>
  defHttp.post({ url: Api.scrapupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const scrapsetIsCancel = (params?: any) =>
  defHttp.get({ url: Api.scrapsetIsCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const scrapsetStatus = (params?: any) =>
  defHttp.get({ url: Api.scrapsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
