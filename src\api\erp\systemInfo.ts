import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetDeptList = '/department/getSelectTree',
  GetStaffList = '/erp/ad/get',
  GetDept = '/erp/si/getDept'
  // GetApplicantList = '/erp/si/getApplicant',
  // GetInchargeList = '/erp/si/getIncharge'
}

//部门信息获取
export const getDeptList = (params?: {}) =>
  defHttp.get({
    url: Api.GetDeptList,
    params
  })

export const getDept = (params?: {}) =>
  defHttp.get({
    url: Api.GetDept,
    params
  })

//获取运营中心的部门
export const getDeptParent32 = (params?: {}) =>
  defHttp
    .get({
      url: Api.GetDept,
      params
    })
    .then((result) => ({ items: result.items.filter((item) => item.isOperate === 1) }))

//员工信息获取
export const getStaffList = (params?: {}) => defHttp.get({ url: Api.GetStaffList, params: { pageSize: 999999, ...params } })

//申请人信息获取
export const getApplicantList = (params?: {}) => defHttp.get({ url: Api.GetStaffList, params: { pageSize: 999999, ...params } })

//负责人信息获取
export const getInchargeList = (params?: {}) => defHttp.get({ url: Api.GetStaffList, params: { pageSize: 999999, ...params } })
