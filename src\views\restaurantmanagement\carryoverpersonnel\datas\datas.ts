import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

const types = {
  1: { text: '个人', color: '#ff0000' },
  2: { text: '公司', color: '#00ff00' },
  3: { text: '其他', color: '#0000ff' }
}
const ptypes = {
  0: { text: '门店自建', color: 'green' },
  1: { text: '总部下发', color: 'blue' }
}

export const columns: BasicColumn[] = [
  {
    title: '挂账人名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '挂账人部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: types[text].color }, types[text].text)
    }
  },
  {
    title: '挂账来源',
    dataIndex: 'p_type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: ptypes[text].color }, ptypes[text].text)
    }
  },
  {
    title: '挂账科目',
    dataIndex: 'category',
    width: 100,
    resizable: true
  }
]
