import { defHttp } from '/@/utils/http/axios'

enum Api {
  getListByInRetreat = '/erp/retreat/getListByInRetreat',
  setStatusByInRetreat = '/erp/retreat/setStatusByInRetreat',
  setIsCalcelByInRetreat = '/erp/retreat/setIsCalcelByInRetreat',
  //入库退货
  addinRetreat = '/erp/retreat/inRetreat',
  updateInRetreat = '/erp/retreat/updateInRetreat'
}

export const getListByInRetreat = (params?: any) => defHttp.get({ url: Api.getListByInRetreat, params })
export const setStatusByInRetreat = (params?: any) =>
  defHttp.post({ url: Api.setStatusByInRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const setIsCalcelByInRetreat = (params?: any) =>
  defHttp.post({ url: Api.setIsCalcelByInRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//入库单退货
export const addinRetreat = (params: {}) =>
  defHttp.post({ url: Api.addinRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const updateInRetreat = (params: {}) =>
  defHttp.post({ url: Api.updateInRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
