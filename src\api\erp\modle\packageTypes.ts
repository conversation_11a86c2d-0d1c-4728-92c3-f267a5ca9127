export interface PackageListType {
  id: number
  items: PackageListItem[]
  strid: string
  label: string
  value: number
  children: PackageListItem[]
}

export interface PackageListItem {
  batch_code: number
  created_at: string
  dept_id: number
  doc_id: number
  id: number
  name: string
  quantity: number
  quantity_left: number
  request_id: number
  status: number
  stocking_id: number
  updated_at: string
  warehouse_id: number
  work_id: number
  [property: string]: any
}

export interface PackageInfo {
  buyer: string // 装箱单购买方
  country: string // 装箱单国家
  id?: number // 装箱单id（传值则为编辑，否则为新增）
  packingList: PackingListItem[] // 装箱单包裹信息
  shipmentAddr: string // 装箱单出货地址
  shipmentAt: string // 装箱单出货日期
  supplier: string // 装箱单供应商
  [property: string]: any
}

export interface PackingListItem {
  id?: number // 包裹id  传值则为编辑/删除，否则为新增）
  items?: ProductItem[] // 包裹产品信息
  method?: string // 包裹打包方式
  quantity?: number // 包裹数量
  size?: string // 包裹包装尺寸(长*宽*高m)
  type: number // 类型：1添加  2编辑  3 删除
  volume?: string // 包裹体积(CBM)  立方米DD
  weight?: number // 包裹重量(KG)
  [property: string]: any
}

export interface ProductItem {
  code?: string // 产品海关编码
  id?: number // 产品id  传值则为编辑/删除，否则为新增）
  imgs?: string[] // 产品图
  itemOutId?: number // 出库单商品id
  material?: string // 产品材质
  name?: string // 产品名称
  quantity?: number // 产品数量
  remark?: string // 产品备注
  requestId?: number // 需求id
  size?: {
    // 产品尺寸
    height: string // 高(cm)
    length: string // 长(cm)
    width: string // 宽(cm)
    [property: string]: any
  }
  type: number // 类型：1添加  2编辑  3 删除
  unit?: string // 单位
  [property: string]: any
}
