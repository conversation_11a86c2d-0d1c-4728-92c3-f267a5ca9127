import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const saleOrderVerifyList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      Verification_date: '@date',
      order_no: '@guid',
      'status|1': [0, 1]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/saleOrderVerify',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(saleOrderVerifyList)
    }
  }
] as MockMethod[]
