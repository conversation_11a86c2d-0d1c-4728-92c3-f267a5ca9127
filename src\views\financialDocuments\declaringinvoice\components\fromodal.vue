<template>
  <BasicModal @register="register" title="关联采购单" :minHeight="400" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { invoicegetSelNumber, invoicegetSelPurStrid } from '/@/api/financialDocuments/InvoiceManagement'
import { ref } from 'vue'

const [register, { closeModal }] = useModalInner(() => {
  resetFields()
})
// 发票编号
const invoice_number = ref()
// 采购订单编号
const pur_id = ref()
//  采购订单编号
const pur_strid = ref()
// 品类数组
const category = ref()
const emit = defineEmits(['Addok'])

const [registerForm, { updateSchema, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'pur_strid',
      label: '关联采购订单',
      component: 'PagingApiSelect',
      required: true,
      itemProps: {
        validateTrigger: 'blur'
      },
      componentProps: ({ formModel }) => ({
        api: invoicegetSelPurStrid,
        searchParamField: 'pur_strid',
        selectProps: {
          fieldNames: { key: 'pur_strid', value: 'pur_strid', label: 'pur_strid' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(val) {
            if (!val) {
              formModel.pur_strid = undefined
              formModel.number = undefined
              formModel.tax_service_name = undefined
            }
          }
        },
        pagingMode: true,
        searchMode: true,
        resultField: 'items'
      }),
      colProps: {
        span: 24
      }
    },
    {
      field: 'number',
      label: '发票号码',
      component: 'PagingApiSelect',
      required: true,
      componentProps: ({ formModel }) => ({
        api: invoicegetSelNumber,
        searchParamField: 'invoice_number',
        params: {
          pur_strid: formModel ? formModel.pur_strid : ''
        },
        selectProps: {
          fieldNames: { key: 'pru_id', value: 'invoice_number', label: 'invoice_number' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(_, shall) {
            if (!shall) return
            invoice_number.value = shall.invoice_number
            pur_id.value = shall.pur_id
            pur_strid.value = shall.pur_strid
            updateSchema({
              field: 'tax_service_name',
              componentProps: {
                options: shall.item,
                mode: 'multiple',
                fieldNames: { key: 'tax_service_name', value: 'id', label: 'tax_service_name' },
                onChange(_, shall) {
                  if (!shall) return
                  category.value = shall
                },
                selectProps: {
                  showSearch: true,
                  placeholder: '请选择',
                  allowClear: true
                }
              }
            })
          }
        },
        pagingMode: true,
        searchMode: true,
        resultField: 'items'
      }),
      colProps: {
        span: 24
      },
      show({ model }) {
        return model.pur_strid
      }
    },
    {
      field: 'tax_service_name',
      label: '品名',
      component: 'Select',
      required: true,
      componentProps: {
        selectProps: {
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(_, shall) {
            console.log(shall)
          }
        }
      },
      colProps: {
        span: 24
      },
      show({ model }) {
        return model.number && model.pur_strid
      }
    }
  ],
  labelWidth: 120,
  layout: 'vertical'
})

async function handleOk() {
  await validate()

  const newarr = category.value.map((item: any) => {
    return {
      ...item,
      pur_id: pur_id.value,
      number: invoice_number.value,
      pur_strid: pur_strid.value
    }
  })
  closeModal()
  emit('Addok', newarr)
}
</script>
