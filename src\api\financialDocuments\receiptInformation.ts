import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetReceiptInformationList = '/erp/finance/rc/getList',
  AssociationWord = '/erp/finance/rc/relevancy',
  GetReceiptOrderDetails = '/erp/finance/rc/details',
  SplitOrder = '/erp/finance/rc/separate'
}

// 获取
export const getReceiptInformationList = (params?: {}) => defHttp.get({ url: Api.GetReceiptInformationList, params })

// 关联任务
export const associationWord = (params: { work: Array<Recordable>; id: string }) => defHttp.get({ url: Api.AssociationWord, params })

// 获取收款单详情
export const getReceiptOrderDetails = (params: { id: number }) => defHttp.get({ url: Api.GetReceiptOrderDetails, params })

// 拆单
export const splitOrder = (params: { id: number; doc_fund: Array<Recordable> }) => defHttp.get({ url: Api.SplitOrder, params })
