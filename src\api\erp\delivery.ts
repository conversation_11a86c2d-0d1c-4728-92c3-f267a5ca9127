import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetDeliveryList = '/erp/delivery/getList',
  UpdateDelivery = '/erp/delivery/update',
  GetDeliveryDetail = '/erp/delivery/detail',
  ExportDelivery = '/erp/delivery/export',
  ChangeDeliveryStatus = '/erp/delivery/setStatus'
}

//如果以后这文件接口多了就把类型提出去
interface IDoc {
  id?: number //送货单id
  supplier_id: number //供应商id
  supplier_contacts: string //供应商联系人
  supplier_telphone: string //供应商电话
  warehouse_id: number //仓库id
  warehouse_contacts: string //仓库联系人
  warehouse_telphone: string //仓库电话
  warehouse_address: string //仓库地址
  inCharge: number //负责人id
  remark: string //备注
}

interface IItems {
  id?: number //送货单明细ID（编辑需要传，新增不需要）
  doc_purchase_id?: number //采购单ID
  dept_id: number //部门ID
  request_id: number //需求id
  purchase_id: number //采购单商品id
  type: 1 | 2 | 3 //1添加 2编辑 3 删除
}

export const getDeliveryList = (params) => defHttp.get({ url: Api.GetDeliveryList, params })

export const updateDelivery = (params: { doc: IDoc; items: IItems[] }) =>
  defHttp.post({ url: Api.UpdateDelivery, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const getDeliveryDetail = (params: { id: number }) => defHttp.get({ url: Api.GetDeliveryDetail, params })

export const exportDelivery = (params: { id: number }) =>
  defHttp.get({ url: Api.ExportDelivery, params, responseType: 'blob' }, { isTransformResponse: false })

/**
 *送货单审核
 * @param params {id:number,status:0|1}
 * @returns
 */
export const changeDeliveryStatus = (params: { id: number; status: 0 | 1 }) =>
  defHttp.get({ url: Api.ChangeDeliveryStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
