<!-- eslint-disable max-len -->
<template>
  <div class="erp-container">
    <BasicTable
      :ref="(el) => (tableRef = el)"
      @register="registerTable"
      class="p-4"
      @fetch-success="handleFetch"
      @expand="onExpand"
      :data-cachekey="routePath"
    >
      <template #form-textSlot>
        <span class="text-20px"> 未收货贷款合计: {{ totalunamount }} </span>
      </template>
      <template #toolbar>
        <div>
          <a
            style="color: red; font-size: 25px"
            href="https://img.gbuilderchina.com/erp/%E5%85%B3%E4%BA%8E%E9%94%80%E5%94%AE%E8%AE%A2%E5%8D%95%E5%A4%87%E8%B4%A7%E5%8A%9F%E8%83%BD%E6%9B%B4%E6%96%B0%E8%AF%B4%E6%98%8E.docx"
            >做采购单时，如果没有产品展示，请检查是否产品已完成拆分，如不清楚请查阅备货功能指南（点击下载）</a
          >
        </div>
      </template>
      <template #form-advanceBefore>
        <a-button
          type="primary"
          @click="handleExport"
          :loading="disableding"
          :disabled="disableding"
          v-if="hasPermission([522])"
          class="mr-8px"
          ><cloud-download-outlined /> 导出搜索结果
        </a-button>
        <Tooltip>
          <template #title>{{ checkboxErrMessage }}</template>
          <a-button v-if="hasPermission([53, 452])" @click="debouncedGenerate" :loading="disableding" :disabled="disableding" class="mr-8px"
            >生成收款单</a-button
          >
        </Tooltip>
        <Tooltip>
          <template #title>{{ checkboxErrMessage }}</template>
          <a-button @click="account" v-if="hasPermission([132, 453])" :loading="disableding" :disabled="disableding" class="mr-8px"
            >收付款结账审核</a-button
          >
        </Tooltip>
        <a-button @click="handlePrintAgr" v-if="hasPermission([193])" :loading="disableding" :disabled="disableding" class="mr-8px">
          <printer-outlined />
          打印AGR
        </a-button>
        <Tooltip>
          <template #title>{{ checkoutIncomeErrorMessage }}</template>
          <a-button
            type="primary"
            @click="onCreateOtherIncome"
            v-if="hasPermission([307, 454])"
            :loading="disableding"
            :disabled="disableding"
            class="mr-8px"
            >创建其他收入单</a-button
          >
        </Tooltip>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="disableding" :disabled="disableding"
          >条件导出EXCEL</a-button
        >
        <a-button type="primary" @click="handleDownloadTip" danger :loading="disableding" :disabled="disableding"
          >下载费用类订单录入指引</a-button
        >
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #form-receivable="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber
              v-model:value="model.receivable1"
              valueFormat="YYYY-MM-DD 00:00:00"
              placeholder="应收金额初始值"
              style="height: 35px"
              :precision="4"
            />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber
              v-model:value="model.receivable2"
              valueFormat="YYYY-MM-DD 23:59:59"
              placeholder="应收金额最大值"
              style="height: 35px"
              :precision="4"
            />
          </div>
        </FormItemRest>
      </template>
      <template #form-received="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber v-model:value="model.received_start" placeholder="已收金额初始值" style="height: 35px" :precision="4" />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber v-model:value="model.received_end" placeholder="已收金额最大值" style="height: 35px" :precision="4" />
          </div>
        </FormItemRest>
      </template>
      <template #expandedRowRender="{ record: fatherrecord }" v-if="routeName == '/erpFlow/unpurchaseTrackingsplit'">
        <BasicTable
          class="p-4"
          @register="registerChildrenTable"
          :api="getItemsList.bind(null, { pageSize: 9999, source_uniqid: fatherrecord.source_uniqid })"
          v-model:expandedRowKeys="childrenexpandedRowKeys"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="text" :simpleShow="true" />
            </template>
            <template v-if="column.key === 'status'">
              <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>
            </template>
          </template>
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable :columns="tablecolum()" :can-resize="false" :data-source="cellRecord.items_sub" :show-index-column="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.key === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <VerifyDrawer @register="registerDrawer" />
    <EditDrawer @register="registerEditDrawer" @success="reload" />
    <GenerateModal @register="registerModal" @success="handleSuccess" />
    <RetreatDrawer @register="registerRetreatDrawer" @success="reload" />
    <PrintNumberModal @register="registerPrintNumberModal" />
    <PrintAgrTableModal @register="registerPrintAgrTableModalModal" />
    <PurchaseDrawer @register="registerPurchaseDrawer" @success="reload" />
    <BackOrderNumModal @register="registerBackOrderNumModal" @success="reload" />
    <FinanceApprovedModal @register="registerFinanceApprovedModal" @success="reload" />
    <!-- <AddSaleorderDrawer @register="registerSaleorderDrawer" @success="reload" /> -->
    <AddModel @register="registerModel" @success="handleSuccess" @close="clearSelectedRowKeys" />
    <BatchOtherIncomDrawer @register="registerOtherIncomeDrawer" @success="reload" />
    <FlowtransferDrawer @register="registerFlowtransferDrawer" @success="handleSuccess" />
    <PreviewFile @register="registerpreModal" />
    <splitstatusModel @register="registersplitstatusModel" @success="handleSuccess" />
    <UploadModal @register="registerUploadModal" @success="handleSuccess" />
    <uploadExcelModal @register="registerUploadExcelModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="tsx" name="/erp/saleOrder">
import { nextTick, ref, onMounted, watch } from 'vue'
import { debounce } from 'lodash-es'
import { Tooltip, message, Form, InputNumber } from 'ant-design-vue'
import { PrinterOutlined, CloudDownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
// import { useModal } from '/@/components/Modal'
import type { ActionItem } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import AddModel from './components/AddModel.vue'
import {
  getSalesOrderList,
  setSalesStatus,
  getSalesOrderChildren,
  saleRetreat,
  setIsAudit,
  setCencleReady,
  receiptExport,
  exportitemRequest,
  projectwqexportPi
} from '/@/api/erp/sales'
// import { addBatch } from '/@/api/financialDocuments/receiptOrder'
import { usePermission } from '/@/hooks/web/usePermission'
import { useMessage } from '/@/hooks/web/useMessage'
import mitt from '/@/utils/mitt'
import VerifyDrawer from './components/VerifyDrawer.vue'
import EditDrawer from './components/EditDrawer.vue'
import type { IRecord } from './datas/types'
import { columns, warehouseColumns, formConfigFn, tableRef, mapTypeMenu } from './datas/datas'
import { useModal } from '/@/components/Modal'
import GenerateModal from './components/GenerateModal.vue'
import RetreatDrawer from './components/CreateRetreatDrawer.vue'
import PrintNumberModal from '../print/components/PrintNumberModal.vue'
import PrintAgrTableModal from '../print/components/PrintAgrTableModal.vue'
import PurchaseDrawer from '../purchaseOrder/components/purchaseDrawer.vue'
import BatchOtherIncomDrawer from './components/BatchOtherIncomDrawer.vue'
import BackOrderNumModal from './components/BackOrderNumModal.vue'
import FinanceApprovedModal from './components/FinanceApprovedModal.vue'
import FlowtransferDrawer from './components/FlowtransferDrawer.vue'
import { mul } from '/@/utils/math'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getWorkList } from '/@/api/commonUtils'
import { useUserStore } from '/@/store/modules/user'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import { checkboxErrMessage, isCheckboxDisabled, checkoutIncomeErrorMessage } from './datas/fn'

//订单拆分
import { columnsFn, statusMap, tablecolum, childRenColumns } from '../UnPurchaseTracking/datas/datas'
import { exportFile, getItemsList } from '/@/api/erp/UnPurchaseTracking'
import { createImgPreview } from '/@/components/Preview'
import splitstatusModel from './components/splitstatusModel.vue'
import UploadModal from './components/UploadModal.vue'
import uploadExcelModal from './components/uploadExcelModal.vue'
import axios from 'axios'
import { downloadByData } from '/@/utils/file/download'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'

const disableding = ref(false)

watch(
  () => disableding.value,
  () => {
    setLoading(disableding.value)
  }
)

const downloading = ref<boolean>(false)
const childrenexpandedRowKeys = ref<number[]>([])
const totalunamount = ref<number>(0)

const route = useRoute()
const { name: routeName, path: routePath } = route

const { createMessage } = useMessage()
const { hasPermission } = usePermission()
// import { useModal } from '/@/components/Modal'
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer()
const [registerRetreatDrawer, { openDrawer: openRetreatDrawer, setDrawerProps: setRetreatDrawerProps }] = useDrawer()
// const [registerSaleorderDrawer, { openDrawer: openSaleorderDrawer }] = useDrawer()
// const InputNumber = Input.Number
const [registerPurchaseDrawer, { openDrawer: openPurchaseDrawer, setDrawerProps: setPurchaseDrawerProps }] = useDrawer()
const [registerOtherIncomeDrawer, { openDrawer: openOtherIncomeDrawer, setDrawerProps: setOtherIncomeDrawerProps }] = useDrawer()

const FormItemRest = Form.ItemRest

const rootMenuEmitter = mitt()
const saleStore = useSaleOrderStore()
const { warehouseQualityStaffRoleValues } = storeToRefs(saleStore)

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const isWarehouseRole = ref(warehouseQualityStaffRoleValues.value.includes(userInfo.value?.roleValue))

const pathname = window.location.pathname

const [registerTable, { getForm, reload, setTableData, setLoading, getSelectRows, clearSelectedRowKeys, setProps, getSelectRowKeys }] =
  useTable({
    title: '',
    showIndexColumn: false,
    columns: routeName == '/erpFlow/unpurchaseTrackingsplit' ? columnsFn() : isWarehouseRole.value ? warehouseColumns : columns,
    api: getSalesOrderList,
    searchInfo: {
      types: [3, 27]
    },
    pagination: {
      pageSize: 10,
      position: ['bottomRight']
    },
    isTreeTable: true,
    beforeFetch: (params) => {
      return {
        ...params,
        status: routeName == '/erpFlow/unpurchaseTrackingsplit' ? [] : params.status,
        status_lists: routeName == '/erpFlow/unpurchaseTrackingsplit' ? [1, 2, 3, 4, 5] : []
      }
    },
    afterFetch: (data) => {
      totalunamount.value = data
        .filter((item) => item.status !== 16)
        .reduce((pre, cur) => {
          return pre + (Number(cur.receivable_left) - Number(cur.received_actual))
        }, 0)
        .toFixed(4)
    },
    actionColumn: {
      width: isWarehouseRole.value ? 100 : 220,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right'
    },
    rowKey: 'id',
    useSearchForm: true,
    formConfig: formConfigFn(),
    showTableSetting: true,
    rowSelection: {
      getCheckboxProps: (record) => {
        if (record.parent_id !== null) {
          return { disabled: true }
        } else {
          return { disabled: false }
        }
      }
    }
  })

onMounted(() => {
  rootMenuEmitter.emit('sale-order-reload', reload)
  setProps({ formConfig: formConfigFn(setProps, clearSelectedRowKeys, reload, { setProps }, routeName) })
})

/** 注册modal */
const [registerModal, { openModal }] = useModal()
const [registerPrintNumberModal, { openModal: openPrintNumberModal }] = useModal()
const [registerBackOrderNumModal, { openModal: openBackOrderNumModal }] = useModal()
const [registerFinanceApprovedModal, { openModal: openFinanceApprovedModal }] = useModal()

const [registerPrintAgrTableModalModal, { openModal: openPrintAgrTableModalModal }] = useModal()

function createActions(record: IRecord): ActionItem[] {
  const received_actual = record.received_actual || 0
  const allowStock = (received_actual > 0 && received_actual >= mul(record.total_price, 0.1, 0)) || record.total_price == 0
  return [
    {
      icon: 'clarity:success-line',
      label: '生效',
      tooltip: '当订单部门不为独立核算部门,不能点击生效。订单状态为执行中的证明已经点了生效。',
      popConfirm:
        record.type == 27
          ? {
              okText: '确定',
              title: '确定将订单状态设置成生效状态吗',
              cancelText: '取消',
              placement: 'left',
              confirm: handleChangeStatus.bind(null, record, 1)
            }
          : {
              okText: '确定',
              title: (
                <div style={{ fontSize: '19px' }}>
                  <div style={{ fontWeight: 'bold', fontSize: '24px' }}>
                    业务部门: {record.operation_department || '无'}, 订单金额:{record.total_price}
                  </div>
                  <div style={{ width: '400px', marginTop: '10px' }}>
                    订单点击生效后：
                    <p>1、CRM无法修改同步业务部门，请留意</p>
                    <p>
                      2、当订单金额未修改且订单收款大于等于订单金额20%时， 该订单的业绩会按照点击生效前的订单金额核算业绩；
                      当订单金额修改且订单收款大于等于订单金额20%时， 仍然会按照点击生效前的订单金额核算业绩，
                      只有该订单进入备货中的状态（采购跟单发起采购单并且经过主管审核）时才会按照最新的订单金额进行二次核算业绩
                    </p>
                  </div>
                </div>
              ),
              cancelText: '取消',
              placement: 'left',
              confirm: handleChangeStatus.bind(null, record, 1)
            },
      disabled: record.status !== 0 || record.parent_id !== null || record.dept_is_auth == 0,
      ifShow: hasPermission([211, 455]) && routeName !== '/erpFlow/unpurchaseTrackingsplit'
    },
    {
      icon: 'clarity:success-line',
      label: '可备货',
      tooltip: !allowStock
        ? {
            title: '实收金额 必须大于或等于 总价金额的10%方可备货',
            // visible: !allowStock,
            trigger: 'hover',
            placement: 'left'
          }
        : null,
      popConfirm: {
        okText: '确定',
        title: '可备货状态将无法继续同步更新CRM订单信息，是否确认？',
        cancelText: '取消',
        placement: 'left',
        confirm: handleSetCankao.bind(null, record)
      },
      disabled:
        (record.is_withdraw === 1 && [1].includes(record.status)) ||
        (record.is_finance_approved === 1 && record.status === 1) ||
        (record.is_after_sale === 1 && record.status === 1)
          ? false
          : record.status !== 1 || record.parent_id !== null || !allowStock,
      ifShow: hasPermission([54]) && routeName !== '/erpFlow/unpurchaseTrackingsplit' && record.type !== 27
    },
    {
      label: '拆分状态',
      disabled: [1, 2].includes(record.stuatus),
      onClick: handlesplitmodal.bind(null, record, 'status'),
      ifShow: hasPermission(661) && routeName == '/erpFlow/unpurchaseTrackingsplit' && record.type !== 27
    },
    {
      label: '产品拆分',
      ifShow: hasPermission([344]) && routeName == '/erpFlow/unpurchaseTrackingsplit' && record.type !== 27,
      onClick: handlesplit.bind(null, record, 'split'),
      disabled: ![2].includes(record.status) || record.parent_id !== null || record.is_finish_split == 1
    }
  ]
}

function createDropDownActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      // disabled: record.type !== 3,
      ifShow: hasPermission([52, 457])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record, 'edit'),
      disabled: ![0, 1].includes(record.status) || record.parent_id !== null,
      // disabled: record.status !== 0 && record.is_reject == 0,
      ifShow: hasPermission([131]) && record.type !== 27
    },
    {
      icon: 'clarity:close-line',
      label: '取消订单',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成取消状态吗',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleChangeStatus.bind(null, record, 16),
        disabled: record.status !== 0 || record.parent_id !== null
      },
      // disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([55, 474])
    },
    {
      label: '改单/退货教程',
      color: 'error',
      onClick: () => {
        const downloadLink = document.createElement('a')
        downloadLink.href =
          // eslint-disable-next-line max-len
          'https://img.gbuilderchina.com/erp/%E9%94%80%E5%94%AE%E8%AE%A2%E5%8D%95%E6%94%B9%E5%8D%95%E6%88%96%E9%80%80%E8%B4%A7%E6%93%8D%E4%BD%9C%E6%B5%81%E7%A8%8B.docx'
        downloadLink.download = `改单/退货教程.xlsx`

        // 模拟点击下载链接
        downloadLink.click()
      },
      ifShow: record.type !== 27
    },

    {
      label: '退货',
      color: 'error',
      disabled:
        (record.type == 27 && record.status !== 1) ||
        (record.type == 3 && !saleStore.retreatSaleStatus.includes(record.status)) ||
        record.parent_id !== null,
      onClick: handleRetreat.bind(null, record, 'retreat'),
      ifShow: hasPermission([161])
    },
    {
      label: '退货再建单',
      color: 'error',
      disabled: !saleStore.retreatSaleStatus.includes(record.status) || record.parent_id !== null,
      onClick: handleRetreat.bind(null, record, 'new'),
      ifShow: hasPermission([315]) && record.type !== 27
    },
    {
      label: '售后单',
      color: 'error',
      // disabled: !saleStore.retreatSaleStatus.includes(record.status) || record.parent_id !== null ,
      disabled: ![15].includes(record.status) || record.parent_id !== null || record.parent_id !== null,
      onClick: handleRetreat.bind(null, record, 'afterSale'),
      ifShow: hasPermission([316]) && record.type !== 27
    },
    {
      label: '售后单教程下载',
      color: 'success',
      onClick: () => {
        const downloadLink = document.createElement('a')
        downloadLink.href = 'https://img.gbuilderchina.com/erp%E5%94%AE%E5%90%8E%E5%8D%95%E6%95%99%E7%A8%8B.docx'
        downloadLink.download = `ERP售后单教程.docx`

        downloadLink.click()
        //移除元素
        document.body.removeChild(downloadLink)
      }
    },
    {
      label: '打印',
      icon: 'ant-design:printer-outlined',
      onClick: handlePrint.bind(null, record) || record.parent_id !== null,
      ifShow: hasPermission([194]) && record.type !== 27,
      disabled: [0, 16].includes(record.status)
    },
    {
      label: '无法采购请看',
      color: 'error',
      icon: 'ant-design:cloud-download-outlined',
      onClick: () => {
        const downloadLink = document.createElement('a')
        downloadLink.href =
          // eslint-disable-next-line max-len
          'https://img.gbuilderchina.com/erp/%E5%85%B3%E4%BA%8E%E9%94%80%E5%94%AE%E8%AE%A2%E5%8D%95%E5%A4%87%E8%B4%A7%E5%8A%9F%E8%83%BD%E6%9B%B4%E6%96%B0%E8%AF%B4%E6%98%8E.docx'
        downloadLink.download = `备货功能操作说明.xlsx`

        // 模拟点击下载链接
        downloadLink.click()
      },
      ifShow: record.type !== 27
    },
    {
      label: '添加采购单',
      onClick: handleAddPurchase.bind(null, record),
      disabled: [0, 1, 16].includes(record.status) || record.parent_id !== null || (record.is_finish_split == 0 && pathname == '/s/'),
      ifShow: hasPermission([220]) && record.type !== 27
    },
    {
      label: '财务特批',
      icon: 'clarity:success-line',
      // 已经财务特批过一次就不可以再次
      disabled: record.is_finance_approved === 1 || ![0, 1].includes(record.status) || record.parent_id !== null,
      onClick: handleFinanceApproved.bind(null, record),
      ifShow: hasPermission([227]) && record.type !== 27
    },
    {
      label: '流水调拨',
      onClick: handleFlowtransfer.bind(null, record),
      ifShow: hasPermission([298]),
      disabled: !(record.received > 0 && record.is_audit !== 1)
    },
    {
      label: '整单退货',
      // disabled: record.status !== 1 || record.parent_id !== null,
      popConfirm: {
        okText: '确定',
        title: '此操作将不可逆，确定要将整张订单都退货吗',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleRetreatOrder.bind(null, record),
        disabled: record.status !== 1 || record.parent_id !== null
      },
      ifShow: hasPermission([286])
    },
    {
      label: '产品拆分',
      ifShow: hasPermission([344]) && record.type !== 27,
      onClick: handlesplit.bind(null, record, 'split'),
      disabled: ![2].includes(record.status) || record.parent_id !== null || record.is_finish_split == 1
    },
    {
      label: '反结算',
      // disabled: record.is_audit !== 2 || record.parent_id !== null,
      popConfirm: {
        okText: '确定',
        title: '确定要反结算吗？',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleIsAudit.bind(null, record, 'is_audit'),
        disabled: ![1, 2].includes(record.is_audit) || record.parent_id !== null
      },
      ifShow: hasPermission([341])
    },
    {
      label: '取消可备货',
      // disabled: record.is_audit !== 2 || record.parent_id !== null,
      popConfirm: {
        okText: '确定',
        title: '确定要取消可备货吗？',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleIsAudit.bind(null, record, 'stock'),
        disabled: ![2].includes(record.status)
      },
      ifShow: hasPermission([383]) && record.type !== 27
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '附件',
      tooltip: '旧erp数据上传附件',
      onClick: handleUppload.bind(null, record, 'erp'),
      ifShow: hasPermission([594])
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '箱单附件',
      onClick: handleUppload.bind(null, record, 'box'),
      ifShow: hasPermission([738])
    },
    {
      label: '主产品导出',
      ifShow: hasPermission([449]) && record.type !== 27,
      onClick: handledownload.bind(null, record),
      disabled: ![2].includes(record.status) || record.parent_id !== null || record.is_finish_split == 1
    },
    {
      label: '子产品导入',
      ifShow: hasPermission([450]) && record.type !== 27,
      onClick: handlecommUpload.bind(null, record),
      disabled: ![2].includes(record.status) || record.parent_id !== null || record.is_finish_split == 1
    },
    {
      label: '设计师下单时间',
      ifShow: hasPermission([450]) && record.type !== 27,
      onClick: handlesalse.bind(null, record)
    },
    {
      label: '导出产品明细(PI)',
      ifShow: record.type !== 27,
      onClick: handleexcelPI.bind(null, record)
    }
  ]
}

function handleSetCankao(record: IRecord) {
  openBackOrderNumModal(true, record)
}

async function handleChangeStatus(record: IRecord, status: number) {
  try {
    await setSalesStatus({ id: record.id, status: status })

    reload()
  } catch (err) {
    throw new Error(`${err}`)
  }
}

function handleDetail(record: IRecord): void {
  setDrawerProps({ title: '销售详情' })
  openDrawer(true, record)
}

function handleEdit(record: IRecord, type: string) {
  openEditDrawer(true, { record, type })
}

//退货
async function handleRetreat(record: IRecord, way: string) {
  const { items } = await getWorkList({ id: record.id, type: record.type, item_left: 1 })

  if (items.length < 1 && way !== 'afterSale') return message.error('没有可退货的商品')

  setRetreatDrawerProps({ title: way == 'afterSale' ? '售后单' : '销售退货' })
  openRetreatDrawer(true, { record, type: 1, way })
}

//点击打印
function handlePrint(record: IRecord) {
  openPrintNumberModal(true, { record, page: 'saleOrder' })
}

function handleAddPurchase(record: IRecord) {
  setPurchaseDrawerProps({ title: '新增采购订单' })
  openPurchaseDrawer(true, { isUpdate: true, type: 'add', page: 'saleOrder', saleRecord: record })
}

function handleFinanceApproved(record: IRecord) {
  openFinanceApprovedModal(true, record)
}

function handleFetch({ items }: { items: IRecord[] }): void {
  if (routeName == '/erpFlow/unpurchaseTrackingsplit') return
  nextTick(() => {
    setTableData(items.map((item) => ({ ...item, children: [] })))
  })
}
// function handleAddOrder(record: IRecord): void {
//   // openModal(true, record)
//   openSaleorderDrawer(true, { record })
// }

async function onExpand(expanded: boolean, record: IRecord): Promise<void> {
  if (routeName !== '/erpFlow/unpurchaseTrackingsplit') {
    if (!expanded || record.children.length > 0) return
    setLoading(true)
    try {
      const childrenData = await getSalesOrderChildren(record.id, record.basic_work_id)
      Object.assign(
        record.children,
        childrenData.items.map((item) => ({ ...item, children: [] }))
      )
    } catch (err) {
      throw new Error(`${err}`)
    } finally {
      setLoading(false)
    }
  }
}

async function handleExport() {
  try {
    disableding.value = true

    const params = await getForm()?.getFieldsValue()

    const response = await exportFile({
      ...params,
      status: routeName == '/erpFlow/unpurchaseTrackingsplit' ? 2 : routeName == '/erpFlow/tobeconfirmed' ? 1 : params.status,
      types: mapTypeMenu[params.menuTypes]
    })

    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `待确认商品-${+new Date()}.xlsx`)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    disableding.value = false
  }
}

/** 生成收款单 */
const debouncedGenerate = debounce(handleGenerate, 200)

async function handleGenerate() {
  if (getSelectRows().length === 0) return message.error('请选择订单')
  let clientId: Array<number> = []
  const show = ref(false)
  try {
    const selectRows = await getSelectRows()
    if (isCheckboxDisabled('generateReceipt', selectRows)) return

    selectRows.forEach((item) => {
      clientId.push(item.client_id)
      return item.id
    })
    for (let item of getSelectRows()) {
      if (item.payment_type && item.payment_type !== 1) {
        message.warning('该销售订单已完成付款单最后一笔款或全款生成')
        show.value = true
        break
      }
    }
    if (!show.value) {
      if (new Set(clientId).size == 1) {
        openModal(true, { selectRowsData: getSelectRows(), work_ids: getSelectRowKeys() })
      } else {
        const errMessage = '不同客户不能生成同一张收款单！'
        message.error(errMessage)
        throw new Error(errMessage)
      }
    }
  } catch (error) {
    // message.error('生成收款单失败!')
    throw new Error(error)
  }
}

//结账核对
// function handleVerify() {
//   const selectData = getSelectRows()
//   if (selectData.length < 1) return
//   createConfirm({
//     iconType: 'warning',
//     title: () => h('span', '结账核对'),
//     content: () => h('span', '只有状态为执行中的订单才可以进行结账核对,确定继续结账核对吗?'),
//     onOk: async () => {
//       const data = selectData.map((item) => {
//         return item.id
//       })

//       await verifyOrder({
//         is_audit: 1,
//         work_ids: data
//       })
//       handleSuccess()
//     }
//   })
// }

//批量结算日期
const [registerModel, { openModal: openAddModal, setModalProps: setAddModalProps }] = useModal()
function account() {
  const selectData = getSelectRows()
  if (selectData.length === 0) return message.error('请选择订单')
  const data = selectData.map((item) => {
    return { id: item.id }
  })
  const datatype = selectData.every((item) => item.type === 27)

  if (datatype) {
    if (isCheckboxDisabled('expenseAudit', selectData)) return
  } else {
    if (isCheckboxDisabled('closingAudit', selectData)) return
  }

  openAddModal(true, { data: data, type: 'account' })
  setAddModalProps({ title: '订单结算日期' })
}

function handlePrintAgr() {
  openPrintAgrTableModalModal(true, { type: 'saleOrder' })
}

async function onCreateOtherIncome() {
  const selectRows = await getSelectRows()
  if (isCheckboxDisabled('createOtherIncome', selectRows)) return
  setOtherIncomeDrawerProps({
    title: '创建其他收入单'
  })
  openOtherIncomeDrawer(true, selectRows)
}

function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}

async function handleRetreatOrder(record) {
  try {
    const { news } = await saleRetreat(record.id)
    if (news === 'success') {
      createMessage.success('退货成功')
      await reload()
    }
  } catch (err) {
    createMessage.error('退货失败')
    throw new Error(err)
  }
}

async function handleIsAudit(record, type) {
  try {
    const { news } =
      type == 'is_audit' ? await setIsAudit({ id: record.id, is_audit: 0 }) : await setCencleReady({ id: record.id, status: 1 })
    if (news === 'success') {
      type == 'is_audit' ? createMessage.success('反结算成功') : createMessage.success('取消备货成功')
      await reload()
    }
  } catch (err) {
    console.error(err)
    type == 'is_audit' ? createMessage.error('反结算失败') : createMessage.error('取消备货失败')
  }
}

//流水调拨
const [registerFlowtransferDrawer, { openDrawer: openFlowtransferDrawer }] = useDrawer()
function handleFlowtransfer(record) {
  openFlowtransferDrawer(true, { type: 1, record })
}

//产品拆分
function handlesplit(record: IRecord, type: string) {
  // openSplitDrawer(true, record)
  openEditDrawer(true, { record, type })
}

//订单拆分子表格

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: true,
  rowKey: 'id',
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
//展示
const [registerpreModal, { openModal: openpreModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]

  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openpreModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

//订单拆分
const [registersplitstatusModel, { openModal: opensplitstatusModal, setModalProps }] = useModal()
function handlesplitmodal(record, type) {
  opensplitstatusModal(true, { record, type })
  setModalProps({ title: type == 'status' ? '拆分状态更改' : '采购需求日期更改' })
}
//销售订单导出
async function handleBeforeExport() {
  try {
    disableding.value = true
    const fordata = await getForm().getFieldsValue()
    const response = await receiptExport({ ...fordata, excel_type: 3, types: mapTypeMenu[fordata.menuTypes] })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `销售-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    disableding.value = false
  }
}

// 上传附件
const [registerUploadModal, { openModal: openModalUplad }] = useModal()
function handleUppload(record, type) {
  openModalUplad(true, { record, type })
}

//子产品导入
const [registerUploadExcelModal, { openModal: openuploadexclemodal, setModalProps: setUploadExcelModal }] = useModal()
function handlecommUpload(record) {
  openuploadexclemodal(true, record)
  setUploadExcelModal({ title: '子产品导入' })
}

async function handledownload(record) {
  try {
    const response = await exportitemRequest({ work_id: record.id })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)

    downloadLink.download = `销售主产品-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (e) {
    console.log(e)
  }
}

async function handleDownloadTip() {
  try {
    downloading.value = true
    const response = await axios.get('https://img.gbuilderchina.com/erp/purchase/20240823/172459338066c8042350ca8684209851.pdf', {
      responseType: 'blob'
    })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response.data)
    downloadLink.download = `费用类订单录入指引.pdf`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
  } catch (e) {
    createMessage.error('下载失败')
  } finally {
    downloading.value = false
  }
}

function handlesalse(record) {
  openAddModal(true, { data: record.id, type: 'sale' })
  setAddModalProps({ title: '设计师下单' })
}
async function handleexcelPI(record) {
  try {
    downloading.value = true
    const response = await projectwqexportPi({ id: record.id })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `${record.source_uniqid}产品明细.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
  } catch (e) {
    createMessage.error('下载失败')
  } finally {
    downloading.value = false
  }
}
</script>

<style lang="less" scoped>
.vben-basic-table {
  padding-top: 0 !important;
}
:deep(.ant-input-number) {
  width: 235px !important;
}
.footer {
  width: 100%;
  span {
    font-size: 20px;
  }
}
</style>
