<template>
  <BasicModal @register="registerModal" title="预览Office文件" defaultFullscreen destroyOnClose>
    <iframe v-if="attachmentSrc" ref="iframeRef" :src="attachmentSrc" width="100%" style="height: 750px"></iframe>
    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
      <a-button type="primary" @click="handleDownload">下载</a-button>
    </template>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { ref, nextTick } from 'vue'

const attachmentSrc = ref('')
const originUrl = ref<string>('')
const iframeRef = ref<HTMLElement | null>(null)
const [registerModal, { changeLoading, closeModal }] = useModalInner(
  async ({ url, fileType, openType = 'blank' }: { url: string; fileType: string; openType: 'blank' | 'iframe' }) => {
    changeLoading(true)
    if (openType === 'blank') {
      window.open(
        !['pdf'].includes(fileType) ? `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}` : url,
        '_blank'
      )
      closeModal()
      changeLoading(false)
      return
    }
    originUrl.value = url
    attachmentSrc.value = !['pdf'].includes(fileType) ? `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}` : url
    nextTick(() => {
      iframeRef.value?.addEventListener('load', () => {
        changeLoading(false)
      })
    })
  }
)

function handleDownload() {
  window.open(originUrl.value, '_block')
}
</script>

<style lang="less">
#tabeller {
  tr {
    background: #0000;
    border-top: 1px solid #dadde1;
  }
  td {
    border: 1px solid #dadde1 !important;
    padding: 0.75rem;
  }
}
</style>
