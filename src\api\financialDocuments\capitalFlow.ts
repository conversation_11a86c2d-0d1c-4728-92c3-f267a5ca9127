import { defHttp } from '/@/utils/http/axios'
// import { BasicFetchResult } from '/@/api/model/baseModel'
import { capitalFlow, IFundFlowBathImportItem } from './modle/types'

enum Api {
  // GetCapitalFlowList = '/erp/capitalFlow',
  GetCapitalFlowList = '/erp/finance/cw/getList',
  ImportCapitalFlow = '/erp/finance/cw/import',
  AddCapitalFlow = '/erp/finance/cw/add',
  UpdateCapitalFlow = '/erp/finance/cw/update',
  DeleteCapitalFlow = '/erp/finance/cw/remove',
  CapitalFlowDetails = '/erp/finance/cw/details',
  GetFinancialInformation = '/erp/cd/getList',
  ExamineCapital = '/erp/finance/cw/setStatus',
  createDisClause = '/erp/finance/cp/createDisClause',
  createRecClause = '/erp/finance/cw/addOtherReceipt',
  //核对备注
  setCheckRemark = '/erp/finance/cw/setCheckRemark',
  FundFlowBathImport = '/erp/finance/cw/import',
  //流水作废
  cancellation = '/erp/finance/cw/setCancel',
  Export = '/erp/finance/cw/export',
  //提现
  getFundDepositInfo = '/erp/finance/cp/getFundDepositInfo',
  //创建提现分摊
  createrFinCostShare = '/erp/finance/cp/createrFinCostShare',
  //资金调拨删除
  removeAllot = '/erp/finance/cw/removeAllot',
  //创建支出明细拆分
  stockodcreateSplit = '/erp/stock/od/createSplit'
}

// 获取
export const getCapitalFlowList = (params?: {}) => defHttp.get({ url: Api.GetCapitalFlowList, params })

// 导入
// export const importCapitalFlow = (params) => defHttp.post({ url: Api.ImportCapitalFlow, params }, { successMessageMode: 'message' })

// 添加
export const addCapitalFlow = (params: capitalFlow) => defHttp.post({ url: Api.AddCapitalFlow, params })
//新增其他支出单
export const createDisClause = (params: {}) => defHttp.post({ url: Api.createDisClause, params })
//新增其他收入单
export const createRecClause = (params: {}) => defHttp.post({ url: Api.createRecClause, params })
// 更新
export const updateCapitalFlow = (params: capitalFlow) => defHttp.post({ url: Api.UpdateCapitalFlow, params })

// 删除
export const deleteCapitalFlow = (params: { id: number }) => defHttp.get({ url: Api.DeleteCapitalFlow, params })

// 资金流水详情
export const capitalFlowDetails = (params: { id: number }) => defHttp.get({ url: Api.CapitalFlowDetails, params })

// 获取资金资料
export const getFinancialInformation = (params?: {}) => defHttp.get({ url: Api.GetFinancialInformation, params })

// 审核
export const examineCapital = (params?: { ids: Array<Number> }) => defHttp.get({ url: Api.ExamineCapital, params })
//核对备注
export const setCheckRemark = (params?: {}) => defHttp.post({ url: Api.setCheckRemark, params })

export const fundFlowBathImport = (params: { list: IFundFlowBathImportItem[] }) =>
  defHttp.post({
    url: Api.FundFlowBathImport,
    params
  })

// 流水作废
export const postcancellation = (params?: { ids: Array<Number> }) => defHttp.get({ url: Api.cancellation, params })
// 资金调拨删除
export const removeAllot = (params?: { ids: Array<Number> }) => defHttp.get({ url: Api.removeAllot, params })

// 提现
export const getFundDepositInfo = (params?: {}) => defHttp.get({ url: Api.getFundDepositInfo, params })
// 创建
export const createrFinCostShare = (params?: {}) =>
  defHttp.post({ url: Api.createrFinCostShare, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

// 导出文件
export const exportFile = (params: {}) => defHttp.get({ url: Api.Export, params, responseType: 'blob' }, { isTransformResponse: false })
// 按部门
export const stockodcreateSplit = (params: {}) =>
  defHttp.post({ url: Api.stockodcreateSplit, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
