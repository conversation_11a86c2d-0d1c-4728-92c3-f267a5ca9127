import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { paymentOrder } from './modle/types'

enum Api {
  GetPaymentOrderList = '/erp/finance/pc/getList',
  // AddPaymentOrder = '/erp/finance/pc/add',
  UpdatePaymentOrder = '/erp/finance/pc/update',
  GetPaymentOrderDetails = '/erp/finance/pc/details',
  DelPaymentOrder = '/erp/finance/pc/remove',
  AddBatch = '/erp/finance/pc/addBatch',
  ExaminePaymentOrder = '/erp/finance/pc/setAuthCheck',
  CashierChangeStatus = '/erp/finance/pc/setStatus',
  //付款款项更改
  setPaymentType = '/erp/finance/pc/setPaymentType',
  //付款单紧急状态
  setUrgentLevel = '/erp/finance/pc/setUrgentLevel',
  PaymentProject = '/erp/finance/pc/getProjectList',
  pcupdatePayment = '/erp/finance/pc/updatePayment',
  financepcsetInChargeStatus = '/erp/finance/pc/setInChargeStatus',
  //导出
  jobexportPaymentList = '/erp/job/exportPaymentList',
  financepcsetContractingParty = '/erp/finance/pc/setContractingParty'
}

// 获取付款单列表
export const getPaymentOrderList = (params?: {}, isExcel = false) =>
  defHttp.get<BasicFetchResult<paymentOrder>>(
    { url: Api.GetPaymentOrderList, params, responseType: !isExcel ? 'json' : 'blob' },
    { isTransformResponse: !isExcel }
  )

// 新增付款单
// export const addPaymentOrder = (params?: { amount: number; processor: string; works: Array<Recordable> }) =>
//   defHttp.post<BasicFetchResult<any>>({ url: Api.AddPaymentOrder, params })

// 关联流水
export const updatePaymentOrder = (params?: {
  fdoc_id: number
  amount: string
  processor: number
  works: Array<Recordable>
  funds: Array<Recordable>
  collection_at: string
}) => defHttp.post<BasicFetchResult<any>>({ url: Api.UpdatePaymentOrder, params })

// 获取付款单详情
export const getPaymentOrderDetails = (params: { id: number }) => defHttp.get({ url: Api.GetPaymentOrderDetails, params })

// 删除付款单
export const delPaymentOrder = (params: { id: number }) => defHttp.get({ url: Api.DelPaymentOrder, params })

// 生成付款单
export const addBatch = (params: {
  works: Array<{ work_id: number; amount: number; supplier_id?: number }>
  is_allot?: number
  clause: number
  dept_id: number
  is_finance?: number
  g_remark?: string
  payment_type?: number
}) =>
  defHttp.post(
    { url: Api.AddBatch, params },
    // { successMessageMode: 'message', errorMessageMode: 'message' },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )

// 审核付款单
export const examinePaymentOrder = (params: {
  id: string
  is_check: number
  works: Array<number>
  reject_remark?: string
  check_remark?: string
}) => defHttp.get({ url: Api.ExaminePaymentOrder, params })

// 出纳改变状态为已付款
export const cashierChangeStatus = (params: {
  id: number
  remark: string
  collection_at: string
  is_relfund: boolean
  from_plaform?: string
  to_plaform?: string
}) => defHttp.get({ url: Api.CashierChangeStatus, params })

// 付款款项更改
export const setPaymentType = (params: {}) => defHttp.get({ url: Api.setPaymentType, params })
//付款单紧急状态
export const setUrgentLevel = (params: {}) => defHttp.get({ url: Api.setUrgentLevel, params })

// 付款单所属项目列表
export const paymentProject = (params: { id: number }) => defHttp.get({ url: Api.PaymentProject, params })
export const pcupdatePayment = (params?: {}) =>
  defHttp.get({ url: Api.pcupdatePayment, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const financepcsetInChargeStatus = (params?: {}) =>
  defHttp.get({ url: Api.financepcsetInChargeStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const jobexportPaymentList = (params?: {}) => defHttp.get({ url: Api.jobexportPaymentList, params })
export const financepcsetContractingParty = (params?: {}) =>
  defHttp.get({ url: Api.financepcsetContractingParty, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
