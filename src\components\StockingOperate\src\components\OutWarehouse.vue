<template>
  <div v-loading="loading" ref="OutWarehouseRef" class="out-warehouse-form">
    <BasicForm @register="registerForm">
      <template #FilesSlot>
        <FormItemRest>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
          >
            <a-button type="primary">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
        </FormItemRest>
      </template>
      <template #relate_detail="{ model }">
        <FormItemRest>
          <BasicTable @register="registerTable">
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex === 'dept_id'">
                <Popover
                  :getPopupContainer="() => OutWarehouseRef as HTMLElement"
                  trigger="click"
                  title="批量关联已勾选数据的部门"
                  @visible-change="handleHoverChange"
                  :visible="visible"
                >
                  <template #content>
                    <TreeSelect
                      size="small"
                      style="width: 188px"
                      v-model:value="setAllDept"
                      :treeDefaultExpandAll="true"
                      :fieldNames="{ children: 'children', key: 'id', value: 'id', label: 'name' }"
                      :treeData="deptTreeList"
                    />
                    <div class="mt-1 justify-between flex">
                      <a-button class="w-5/2" type="primary" size="small" @click="handleSetAllDept"> 确定 </a-button>
                      <a-button class="w-5/2 ml-2" size="small" @click="visible = false"> 取消 </a-button>
                    </div>
                  </template>
                  <span style="margin-right: 10px">{{ column.customTitle }}</span>
                  <EditOutlined />
                </Popover>
              </template>
              <template v-else>{{ column.customTitle }}</template>
            </template>
            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'quantity' && model.relate_detail">
                <FormItemRest>
                  <InputNumber
                    :max="record.qty_stocking"
                    :min="0.01"
                    v-if="model.relate_detail[index]"
                    v-model:value="model.relate_detail[index].quantity"
                  />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'dept_id' && model.relate_detail">
                <FormItemRest>
                  <TreeSelect
                    v-model:value="model.relate_detail[index].dept_id"
                    v-if="model.relate_detail[index]"
                    :treeDefaultExpandAll="true"
                    :fieldNames="{ children: 'children', key: 'id', value: 'id', label: 'name' }"
                    :treeData="deptTreeList"
                  />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'status' && model.relate_detail">
                <FormItemRest>
                  <Select
                    :disabled="true"
                    v-if="model.relate_detail[index]"
                    :options="mapDetailStatus"
                    v-model:value="model.relate_detail[index].status"
                  />
                </FormItemRest>
              </template>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </div>
</template>

<script setup lang="ts" name="StockingOutWarehouse">
import { BasicForm, useForm } from '/@/components/Form'
import { StockingItem } from '/@/components/StockingOperate/src/types'
import { BasicTable, useTable } from '/@/components/Table'
import { Form, InputNumber, Select, TreeSelect, Popover, message, Upload, UploadFile } from 'ant-design-vue'
import { ref, watch, onMounted } from 'vue'
import { deptTreeList, getDeptTreeList } from '/@/views/erp/outWarehouse/datas/OutWarehouseDrawer'
import { mapDetailStatus, schemas, handleLoadTree, treeData, productColumns } from '../datas/outWarehouse.datas'
import { EditOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { addOutWarehouse } from '/@/api/erp/outWarehouse'
import { useMessage } from '/@/hooks/web/useMessage'
import { DrawerInstance } from '/@/components/Drawer'
// import { mapItemRequest } from '/@/views/erp/outWarehouse/datas/OutWarehouseDrawer'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { getWorkList } from '/@/api/commonUtils'
import defaultUser from '/@/utils/erp/defaultUser'
const props = withDefaults(
  defineProps<{
    stockingList: StockingItem[]
  }>(),
  {
    stockingList: () => []
  }
)

const loading = ref<boolean>(false)
const filesList = ref<UploadFile[]>([])
const OutWarehouseRef = ref<HTMLElement>()
const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()
// const getMapSalesWork = toRef(useMapStoreWithOut(), 'getMapSalesWork')
const { createMessage } = useMessage()
const FormItemRest = Form.ItemRest
const treeExpandedKeys = ref([])
const setAllDept = ref<number>()
const [registerForm, { setFieldsValue, getFieldsValue, validate }] = useForm({
  schemas,
  labelAlign: 'left',
  colon: true,
  showActionButtonGroup: false,
  baseColProps: { span: 6 },
  labelWidth: 110
})

const [registerTable, { getSelectRowKeys }] = useTable({
  showIndexColumn: false,
  columns: productColumns,
  dataSource: props.stockingList,
  inset: true,
  pagination: false,
  maxHeight: 400,
  rowKey: 'id',
  rowSelection: {}
})

watch(
  () => treeExpandedKeys.value,
  (val) => {
    const expend = treeData.value.filter((item: Recordable) => val.includes(item.value) && (!item.children || item.children?.length === 0))
    for (const item of expend) {
      handleLoadTree(item, true)
    }
  }
)
onMounted(async () => {
  await getDeptTreeList()
  handleTreeSelect(props.stockingList)
  setFieldsValue({ applicant: defaultUser!.userId, inCharge: defaultUser!.userId })
})

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'outWarehouse')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

async function handleTreeSelect(val: any[]) {
  try {
    console.log(val)
    loading.value = true
    const mapWorkList = {}
    const workIds = [...new Set(val.map((item) => item.work_id))]
    const works = await Promise.allSettled(workIds.map((item) => getWorkList({ id: item })))
    for (const work of works) {
      if (work.status === 'fulfilled') {
        mapWorkList[work.value?.items[0]?.id] = work.value?.items[0]
      }
    }
    await setFieldsValue({
      relate_detail: val.map((item: Recordable) => ({
        // ...item,
        quantity: item.qty_stocking ? item.qty_stocking : 1,
        warehouse_id: item.warehouse_id ? item.warehouse_id : '',
        dept_id: item.dept_id ? item.dept_id : mapWorkList[item?.work_id]?.dept_id,
        from_at: item.work_id ? item.work_id : '',
        name: item.name,
        status: 0,
        id: item.id,
        // batch_code: mapItemRequest.value[item.request_id]?.batch_code,
        request_id: item.request_id,
        stocking_id: item.id
      }))
    })
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    loading.value = false
  }
}
const visible = ref(false)
function handleHoverChange() {
  const selectKeys = getSelectRowKeys()
  if (selectKeys.length > 0) {
    visible.value = true
  } else {
    message.warning('请选择要编辑的行')
    visible.value = false
  }
}
async function handleSetAllDept() {
  const selectKeys = getSelectRowKeys()
  const formData = getFieldsValue().relate_detail
  for (const item of formData) {
    if (selectKeys.includes(item.id)) {
      item.dept_id = Number(setAllDept.value)
    }
  }
  await setFieldsValue({ relate_detail: formData })
  visible.value = false
}

async function submit({ changeOkLoading, closeDrawer }) {
  await changeOkLoading(true)
  try {
    const values = await validate()
    // const relate_detail = await getDataSource()
    console.log(values)
    const { relate_detail, applicant, inCharge, remark, checkout_at, confirmed_at, files, shipment_inCharge, urgent_level, shipment_type } =
      values
    const commonDoc = {
      applicant,
      inCharge,
      // status,
      remark,
      checkout_at,
      confirmed_at,
      files,
      shipment_inCharge,
      urgent_level,
      shipment_type
    }
    const data = {
      doc: commonDoc,
      items: relate_detail.map((item: Recordable) => {
        const { from_at, batch_code, dept_id, quantity, warehouse_id, status, stocking_id, request_id, name } = item
        const commonItems = {
          work_id: from_at,
          request_id,
          batch_code,
          dept_id,
          quantity,
          name,
          warehouse_id: warehouse_id,
          status,
          stocking_id,
          type: 1
        }
        return { ...commonItems }
      })
    }
    console.log(data)

    const result = await addOutWarehouse(data)
    createMessage.success(result.msg)
    emits('success')
    closeDrawer()
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}

defineExpose({
  submit
})
</script>

<style scoped lang="less">
.out-warehouse-form {
  :deep(.ant-form-item-label > label) {
    padding-left: 5px;
  }
}
</style>
