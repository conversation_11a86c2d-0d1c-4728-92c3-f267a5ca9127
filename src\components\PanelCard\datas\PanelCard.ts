export interface TopTitleProps {
  lefttitle?: string
  data?: string
  righttitle?: string
  strid?: string
}

export interface DetailsValue {
  toptitle?: string
  toptitledata?: string
  bottomtitle?: string
  bottomtitledata?: string
}
export interface PackagesStatus {
  color?: string
  text?: string
}
export interface CardAlert {
  settitle?: string
  setnumber?: number
  endtitle?: string
  endnumber?: number
}
export interface Operations {
  text: string
  label?: string
  onClick?: Fn
  icon?: string
  value?: string
  ifShow?: Boolean
  disabled?: Boolean
  popConfirm?: {
    title?: string
    placement?: 'left' | 'right' | 'top' | 'bottom'
    onConfirm?: Fn
  }
}

export const mapOperateBtn = {
  detail: {
    text: '详情',
    value: 'detail'
  },
  approve: {
    text: '审核',
    value: 'approve',
    // onChange: click.bind(null),
    popConfirm: {
      title: '修改状态后无法再次修改状态，且无法编辑，确定要通过审核吗？',
      placement: 'left'
    }
  },
  edit: {
    text: '修改',
    value: 'edit'
  },
  delete: {
    text: '删除',
    // onChange: handleDel.bind(null, cardData)
    value: 'delete',
    popConfirm: {
      title: '确定删除此数据吗？',
      placement: 'left'
    }
  }
}
