import { defHttp } from '/@/utils/http/axios'
// 发票登记
enum Api {
  InvoicecusgetList = '/taxbook/Invoice/cus/getList',
  Invoicecuscreate = '/taxbook/Invoice/cus/create',
  Invoicecusupdat = '/taxbook/Invoice/cus/update',
  Invoicecusdelete = '/taxbook/Invoice/cus/delete',
  InvoicecussetStatus = '/taxbook/Invoice/cus/setStatus',
  Invoicecusexport = '/taxbook/Invoice/cus/export'
}

// 获取
export const InvoicecusgetList = (params?: {}) => defHttp.get({ url: Api.InvoicecusgetList, params }, { errorMessageMode: 'message' })
export const Invoicecuscreate = (params?: {}) =>
  defHttp.post({ url: Api.Invoicecuscreate, params }, { errorMessageMode: 'message', successMessageMode: 'message' })
export const Invoicecusupdat = (params?: {}) =>
  defHttp.post({ url: Api.Invoicecusupdat, params }, { errorMessageMode: 'message', successMessageMode: 'message' })
export const Invoicecusdelete = (params?: {}) =>
  defHttp.get({ url: Api.Invoicecusdelete, params }, { errorMessageMode: 'message', successMessageMode: 'message' })
export const InvoicecussetStatus = (params?: {}) =>
  defHttp.get({ url: Api.InvoicecussetStatus, params }, { errorMessageMode: 'message', successMessageMode: 'message' })
export const Invoicecusexport = (params?: {}) =>
  defHttp.get(
    { url: Api.Invoicecusexport, params, responseType: 'blob' },
    { isTransformResponse: false, errorMessageMode: 'message', successMessageMode: 'message' }
  )
