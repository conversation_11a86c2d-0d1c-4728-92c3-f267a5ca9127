<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" @click="handleExport" :loading="buttonloading" :disabled="buttonloading" class="mr-8px">
          <cloud-download-outlined /> 导出搜索结果
        </a-button>
      </template>
      <template #toolbar>
        <Button type="primary" v-if="hasPermission([727])" @click="handleChange('delete_all')" :loading="buttonloading"
          >批量删除登记发票</Button
        >
        <Button type="primary" v-if="hasPermission([728])" @click="handleChange('affirm_all')" :loading="buttonloading"
          >批量确认登记发票</Button
        >
        <Button type="primary" v-if="hasPermission([729])" @click="handleAdd" :loading="buttonloading">新增登记发票</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registerDrawer" @success="handlesuccess" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { Invoicecusdelete, Invoicecusexport, InvoicecusgetList, InvoicecussetStatus } from '/@/api/financialDocuments/declaringinvoice'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import { Button, message } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { ref } from 'vue'
import { downloadByData } from '/@/utils/file/download'
const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const buttonloading = ref(false)

const [registerTable, { reload, getSelectRows, setLoading, getForm }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  columns,
  rowKey: 'id',
  api: InvoicecusgetList,
  formConfig: {
    labelWidth: 120,
    schemas,
    fieldMapToTime: [['data', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowSelection: {}
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '确认',
      onClick: handleChange.bind(null, 'affirm', record),
      disabled: record.status === 1,
      ifShow: hasPermission([730])
    },
    {
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: record.status === 1,
      ifShow: hasPermission([731])
    },
    {
      label: '删除',
      popConfirm: {
        title: '确认删除',
        placement: 'left',
        confirm: handleChange.bind(null, 'delete', record),
        disabled: record.status === 1
        // ifShow: hasPermission([113])
      },
      ifShow: hasPermission([732])
    }
  ]
  return editButtonList
}
function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record)
    }
  ]

  return editButtonList
}

async function handlesuccess() {
  await reload()
}

function handleAdd() {
  openDrawer(true, {
    type: 'add'
  })
  setDrawerProps({ title: '新增登记发票', showFooter: true })
}

function handleUpdate(record: EditRecordRow) {
  openDrawer(true, {
    type: 'edit',
    record
  })
  setDrawerProps({ title: '编辑登记发票', showFooter: true })
}
function handleDetail(record: EditRecordRow) {
  openDrawer(true, {
    type: 'detail',
    record
  })
  setDrawerProps({ title: '登记发票详情', showFooter: false })
}

async function handleChange(actionType, record?) {
  setLoading(true)
  buttonloading.value = true
  let doc_id: string[] = []

  try {
    if (['delete_all', 'affirm_all'].includes(actionType)) {
      const selectdata = getSelectRows()
      if (selectdata.length === 0) {
        setLoading(false)
        buttonloading.value = false
        message.error('请选择要操作的发票')
        return
      }

      for (const items of selectdata) {
        if (items.status === 1) {
          setLoading(false)
          buttonloading.value = false
          message.error('请选择未确认的发票')
          return
        }
      }

      doc_id = selectdata.map((item) => item.id)
    } else {
      doc_id = [record.id]
    }

    ;['affirm_all', 'affirm'].includes(actionType)
      ? await InvoicecussetStatus({ doc_ids: doc_id })
      : await Invoicecusdelete({ doc_ids: doc_id })
    await reload()
  } catch (error) {
    console.error(error)
    message.error('操作失败，请稍后重试')
  } finally {
    setLoading(false)
    buttonloading.value = false
  }
}

async function handleExport() {
  try {
    buttonloading.value = true

    const params = await getForm()?.getFieldsValue()

    const response = await Invoicecusexport({
      ...params,
      date1: params.created_at_start.toString(),
      date2: params.created_at_end.toString(),
      created_at_start: void 0,
      created_at_end: void 0
    })

    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `报关发票-${+new Date()}.xlsx`)
    message.success('导出成功')
  } catch (err) {
    message.error('导出失败')
    throw new Error(err)
  } finally {
    buttonloading.value = false
  }
}
</script>
