import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const verifyList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      id: '@id',
      date: '@date',
      order_no: '@guid',
      'status|1': [0, 1]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/purchase/getPurchOrderVerifyList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(verifyList)
    }
  }
] as MockMethod[]
