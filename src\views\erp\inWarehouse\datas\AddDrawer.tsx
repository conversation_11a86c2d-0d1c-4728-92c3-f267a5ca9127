import { h, ref, withDirectives } from 'vue'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
import { isUndefined } from 'lodash-es'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { Rule } from 'ant-design-vue/lib/form'
import { FormSchema } from '/@/components/Form'
import { getDeptSelectTree } from '/@/api/admin/dept'
// import { getErpSupplier } from '/@/api/commonUtils'
// import { getPurchaseSelectList } from '/@/api/erp/purchaseOrder'
import type { RouteRecordName } from 'vue-router'
import { BasicColumn } from '/@/components/Table'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import loadingDirective from '/@/directives/loading'
import { TPage } from '../../purchaseOrder/datas/types'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
export const compStatus = ref<number>(0)

export const searchWorkList = ref<any[]>([])
const loading = ref<boolean>(false)
export const statusOptions: {
  mapOrderStatus: { [key: number]: DefaultOptionType }
  mapDetailStatus: { [key: number]: DefaultOptionType[] }
  mapDetailStatusItem: { [key: number]: string }
} = {
  mapOrderStatus: {
    0: {
      value: 0,
      label: '未入库'
    },
    1: {
      value: 1,
      label: '入库中'
    },
    2: {
      value: 2,
      label: '已入库'
    },
    3: {
      value: 3,
      label: '已清点'
    }
  },
  mapDetailStatus: {
    0: [
      {
        label: '未入库',
        value: 0
      },
      {
        label: '已入库',
        value: 1
      },
      {
        label: '已清点',
        value: 2
      }
    ],
    1: [
      {
        label: '未入库',
        value: 0
      },
      {
        label: '已入库',
        value: 1
      },
      {
        label: '已清点',
        value: 2
      }
    ],
    2: [
      {
        label: '已入库',
        value: 1
      },
      {
        label: '已清点',
        value: 2
      }
    ],
    3: [
      {
        label: '已清点',
        value: 2
      }
    ]
  },
  mapDetailStatusItem: {
    0: '未入库',
    1: '已入库',
    2: '已清点'
  }
}

export const getSchemasList = (
  isUpdate: boolean,
  type: string,
  routeName: string,
  handleFn?: { validateFields: Function; handlePurchaseOrderChange: Function },
  page?: TPage
): FormSchema[] => {
  return [
    {
      field: 'status',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 0,
      render: ({ model }) =>
        h('div', {}, [
          h('span', {}, statusOptions.mapOrderStatus[model.status]?.label ?? '未出库'),
          h(ArrowRightOutlined, { style: 'margin: 0 10px' }),
          h('span', {}, statusOptions.mapOrderStatus[compStatus.value]?.label ?? '未出库')
        ])
    },
    // {
    //   field: 'name',
    //   label: '任务名称',
    //   component: 'Input',
    //   componentProps: {
    //     disabled: !isUpdate || routeName === 'inWarehouseNotice'
    //   },
    //   required: true,
    //   ifShow: type === 'add'
    // },
    {
      field: 'dept_id',
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
          placeholder: '请选择',
          treeDefaultExpandAll: true,
          filterTreeNode: (search: string, item: DefaultOptionType) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
        // disabled: !isUpdate || routeName === '/erp/inWarehouseNotice'
      },
      ifShow: type !== 'add',
      dynamicDisabled: true
      // required: true
    },
    {
      field: 'pkg_num',
      label: '待收包裹数',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: {
        // defaultValue: 1,
        min: 0,
        disabled: !isUpdate || routeName === '/erp/inWarehouseNotice'
      },
      // required: true,
      show: false
    },
    {
      field: 'waybill_num',
      label: '车牌/快递单',
      component: 'Input',
      componentProps: {
        disabled: !isUpdate || routeName === '/erp/inWarehouseNotice'
      }
    },
    {
      ifShow: type === 'add',
      field: 'work',
      label: '关联采购订单',
      component: 'ApiSelect',
      required: true,
      itemProps: {
        validateTrigger: 'blur'
      },
      componentProps: {
        disabled: !isUpdate || routeName === '/erp/inWarehouseNotice',
        api: getRelatePurchaseList,
        params: {
          status: 1,
          pageSize: 999999,
          is_wait: 1
        },
        searchParamField: 'strid',
        immediate: false,
        selectProps: {
          fieldNames: { key: 'doc_id', value: 'doc_id', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          disabled: !isUpdate || page === 'purchase',
          mode: 'multiple',
          labelInValue: true,
          dropdownRender: ({ menuNode }) => {
            const vNode = h('div', {}, menuNode)

            return withDirectives(vNode, [[loadingDirective, loading.value]])
          }
        },
        searchMode: true,
        resultField: 'items',
        onChange: async (val: number) => {
          console.log(val, 'val')

          try {
            if (['add'].includes(type)) await handleFn!.validateFields!(['work_id'])
            handleFn!.handlePurchaseOrderChange(val, loading)
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    {
      field: 'purchase_list',
      label: '采购列表',
      component: 'Input',
      slot: 'Purchase',
      rules: [{ required: true, validator: validateGoods }]
    }
  ]
}

//校验
function validateGoods(_rule: Rule, value: Recordable[]) {
  console.log(value, 'value')
  if (!value || value.length === 0) return Promise.reject('请先选择关联的采购订单')

  // 逐个检查每个商品的必填字段，提供具体的错误信息
  for (let i = 0; i < value.length; i++) {
    const item = value[i]
    const rowNumber = i + 1

    // 检查收货数量
    if (isUndefined(item.qty_total) || item.qty_total === null || item.qty_total === '') {
      return Promise.reject(`第${rowNumber}行：请输入入库数量`)
    }
    if (item.qty_total === 0) {
      return Promise.reject(`第${rowNumber}行：入库数量不能为0`)
    }
    if (item.qty_total < 0) {
      return Promise.reject(`第${rowNumber}行：入库数量不能为负数`)
    }

    // 检查仓库
    if (isUndefined(item.warehouse_id) || item.warehouse_id === null || item.warehouse_id === '') {
      return Promise.reject(`第${rowNumber}行：请选择仓库`)
    }

    // 检查入库时间
    if (isUndefined(item.received_at) || item.received_at === null || item.received_at === '') {
      return Promise.reject(`第${rowNumber}行：请选择入库时间`)
    }
  }

  return Promise.resolve()
}

export const getDrawerTableColumns = (type?: string, status?: number, routeName: RouteRecordName | null | undefined): BasicColumn[] => [
  {
    title: '',
    dataIndex: 'approve',
    width: 50,
    ifShow: type === 'add'
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    width: 200,
    resizable: true
  },
  {
    title: '入库状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    ifShow: [2, 3].includes(status)
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100,
    resizable: true,
    ifShow: (routeName === '/erp/inWarehouse' && hasPermission(321)) || (routeName === '/erp/inWarehouseNotice' && hasPermission(322))
    // ifShow: type === 'add'
  },

  //下面的是可以编辑的
  {
    title: '仓库',
    dataIndex: 'warehouse_id',
    width: 150,
    resizable: true
  },
  {
    title: '收到的日期',
    dataIndex: 'received_at',
    width: 200,
    resizable: true
  },
  {
    title: '入库数量',
    dataIndex: 'qty_total',
    width: 150,
    resizable: true
  },
  // {
  //   title: '已入库商品数',
  //   dataIndex: 'qty_received',
  //   width: 150,
  //   resizable: true,
  //   ifShow: [2, 3].includes(status as number)
  // },
  // {
  //   title: '订单需求数量',
  //   dataIndex: 'qty_request',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 100,
    resizable: true
  },
  {
    title: '已入库数量',
    dataIndex: 'qty_received_total',
    width: 100,
    resizable: true
  },
  {
    title: '订单采购数量',
    dataIndex: 'qty_purchased',
    width: 100,
    resizable: true
  },
  {
    title: '总采购商品数',
    // dataIndex: 'qty_purchased',
    dataIndex: 'qty_purchased_total',
    width: 150,
    resizable: true
  }
  // {
  //   title: '要接收的包裹数',
  //   dataIndex: 'pkg_num',
  //   width: 150,
  //   resizable: true
  // },
  // {
  //   title: '本次入库商品数',
  //   dataIndex: 'qty_received',
  //   width: 150,
  //   resizable: true
  // },
]

// function generateArrParams(start: number, end: number) {
//   const arr: number[] = []
//   let i = start
//   while (end > i) {
//     arr.push(i)
//     i++
//   }
//   return arr
// }

// export const docKeys = ['dept_id', 'erp_num', 'status', 'supplier_id', 'waybill_num', 'id']

/**下面有的字段才会提交 */
export const itemsKeys = [
  'desc',
  'id',
  'imgs',
  'name',
  // 'pkg_num',
  'pkg_received',
  'purchase_id',
  'qty_defective',
  'qty_received',
  // 'qty_stocking',
  'qty_total',
  'received_at',
  'remark',
  'request_id',
  'status',
  'unit',
  'unit_price',
  'warehouse_id',
  'puid',
  'request_id',
  'purchase_id'
]
