<template>
  <div>
    <BasicModal @register="registerModal" v-bind="$attrs" title="查看水印图片" width="60%" :showOkBtn="false">
      <div class="h-1/2">
        <Row :gutter="10">
          <Col :span="4" v-for="(img, idx) in imageList" :key="img">
            <img :src="img" class="mr-2" @click="handleClick(idx)" />
          </Col>
        </Row>
      </div>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { ref } from 'vue'
import { createImgPreview } from '/@/components/Preview/index'
import { Row, Col } from 'ant-design-vue'

const imageList = ref<string[]>([])
const [registerModal] = useModalInner((data) => {
  imageList.value = Object.values(data.image)
})

function handleClick(idx: number) {
  createImgPreview({ imageList: Object.values(imageList.value), index: idx, defaultWidth: 500, maskClosable: true })
}
</script>
