//资金档案
import { defHttp } from '/@/utils/http/axios'
enum Api {
  GetFundsLevel = '/erp/cd/get',
  GetFundsVoucherByName = '/erp/cd/recordlist',
  GetFundsList = '/erp/cd/getCapitaldataList',
  UpdateFunds = '/erp/cd/update'
}

export const getFundsLevel = (params?: { noCache?: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetFundsLevel, params })

export const getFundsVoucherByName = (params?: { page: number; pageSize: number; name?: string }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetFundsVoucherByName, params })

export const getFundsList = (params) => defHttp.get({ url: Api.GetFundsList, params })

export const updateFunds = (params) => defHttp.post({ url: Api.UpdateFunds, params })
