import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetProjectList = '/erp/project/ov/getList',
  GetProjectSalesOrder = '/erp/project/ov/getSaleList',
  GetProjectRequest = '/erp/project/ov/getRequestList',
  GetListByTree = '/erp/project/pro/getListByTree'
}

export const getProjectList = (params?: {}) => {
  return defHttp.get({
    url: Api.GetProjectList,
    params
  })
}

export const getProjectSalesOrder = (params: {}) => defHttp.get({ url: Api.GetProjectSalesOrder, params })

export const getProjectSalesRequest = (params: {}) => defHttp.get({ url: Api.GetProjectRequest, params })

export const getListByTree = (params: {}) => defHttp.get({ url: Api.GetListByTree, params })
export const GetProjectListExcel = (params) =>
  defHttp.get({ url: Api.GetProjectList, params, responseType: 'blob' }, { isTransformResponse: false })
