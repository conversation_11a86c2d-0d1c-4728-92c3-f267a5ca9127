import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'

export const columns: BasicColumn[] = [
  {
    title: '分类名称',
    dataIndex: 'name',
    width: 250,
    resizable: true
  },
  {
    title: '菜品下发类型',
    dataIndex: 'p_type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text == 1 ? '总部下发' : '外卖'
    }
  },
  {
    title: '最后修改日期',
    dataIndex: 'm_date',
    width: 150,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  // {
  //   field: 'type',
  //   label: '菜品类型',
  //   component: 'Select',
  //   componentProps: {
  //     options: Object.keys(types).map((key) => {
  //       return {
  //         label: types[key].text,
  //         value: key
  //       }
  //     })
  //   },
  //   colProps: {
  //     span: 6
  //   }
  // },
  {
    field: 'name',
    label: '分类名称',
    component: 'Input',
    colProps: {
      span: 6
    }
  }
]
