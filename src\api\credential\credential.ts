import type { ICreateCredentialList } from '../model/credentialModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetCredentialList = '/erp/cert/record/getList',
  CreateCredential = '/erp/cert/record/create',
  BatchImportERP = '/erp/cert/record/batchAdd',
  BatchDeleteERP = '/erp/cert/record/batchDelete',
  BatchUpdateStatus = '/erp/cert/record/batchSetStatus',
  ExportPackage = '/erp/cert/record/export',
  //导入凭证
  Exportimport = '/erp/cert/record/import',
  //金额检测
  checkImportAmount = '/erp/cert/record/checkImportAmount',
  //部门档案导出
  exportDept = '/erp/cert/record/exportDept',
  //科目档案导出
  exportAccount = '/erp/cert/record/exportAccount',
  //编辑
  postupdate = '/erp/cert/record/update',
  //财务结算新增
  createEndDate = '/erp/cert/check/createEndDate',
  //财务结算列表
  getEndDateList = '/erp/cert/check/getEndDateList',
  //财务结算删除
  getdelEndDate = '/erp/cert/check/delEndDate',
  //失算平衡
  trialBalancing = '/erp/cert/check/trialBalancing',
  //手工录入凭证单号获取
  getOrderNumber = '/erp/cert/record/getOrderNumber',
  //内部部门调转
  deptnameentgetList = '/erp/cert/record/getList',
  //内部部门调转创建
  deptcheckOutCreate = '/erp/cert/ia/checkOutCreate',
  //内部部门调转审核
  deptsetStatus = '/erp/cert/ia/setStatus',

  //内部部门调转删除
  deptdelete = '/erp/cert/ia/delete',
  //人员信息
  certrecordexportStaff = '/erp/cert/record/exportStaff'
}

export const getCredentialList = (params?: Partial<ICreateCredentialList>) => defHttp.get({ url: Api.GetCredentialList, params })

export const createCredential = (params: ICreateCredentialList) =>
  defHttp.get({ url: Api.CreateCredential, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//批量插入凭证
export const batchImportERP = (params) =>
  defHttp.post({ url: Api.BatchImportERP, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//批量导入凭证
export const exportimport = (params) => defHttp.post({ url: Api.Exportimport, params })

//批量插入凭证
export const checkImportAmount = (params) => defHttp.post({ url: Api.checkImportAmount, params })

//批量删除凭证
export const batchDeleteERP = (params: { ids: number[] }) =>
  defHttp.get({ url: Api.BatchDeleteERP, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//批量修改状态(审核)
export const batchUpdateStatus = (params: { ids: number[]; status: number }) =>
  defHttp.post({ url: Api.BatchUpdateStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//数据导出
export const exportPackage = (istree, params) =>
  defHttp.get({ url: Api.ExportPackage, params, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })
//部门档案导出
export const exportDept = (istree) =>
  defHttp.get({ url: Api.exportDept, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })
//科目档案导出
export const exportAccount = (istree) =>
  defHttp.get({ url: Api.exportAccount, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })

export const postupdate = (params: {}) =>
  defHttp.post({ url: Api.postupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//创建财务结算
export const createEndDate = (params: {}) =>
  defHttp.post({ url: Api.createEndDate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//财务结算列表
export const getEndDateList = (params: {}) => defHttp.get({ url: Api.getEndDateList, params })
//试算平衡
export const trialBalancing = (params: {}) =>
  defHttp.get({ url: Api.trialBalancing, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const getdelEndDate = (params: {}) =>
  defHttp.get({ url: Api.getdelEndDate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const getOrderNumber = (params?: {}) => defHttp.get({ url: Api.getOrderNumber, params })
export const deptnameentgetList = (params?: {}) => defHttp.get({ url: Api.deptnameentgetList, params })
export const deptcheckOutCreate = (params?: {}) =>
  defHttp.get({ url: Api.deptcheckOutCreate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const deptsetStatus = (params?: {}) =>
  defHttp.get({ url: Api.deptsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const deptdelete = (params?: {}) =>
  defHttp.get({ url: Api.deptdelete, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//数据导出
export const certrecordexportStaff = (istree) =>
  defHttp.get({ url: Api.certrecordexportStaff, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })
