<template>
  <div>
    <BasicTable :data-cachekey="routePath" :ref="(el) => (tableRef = el)" @register="registerTable" @fetch-success="onPurchaseFetchSuccess">
      <template #form-advanceBefore>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="exporting">导出搜索结果</a-button>
        <Button type="primary" @click="handlepurhaseExportAll" :loading="exporting">查看文件下载 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
        <template v-if="column.key === 'account_name'">
          <Badge :count="record.notdf_count">
            <div style="padding: 10px">{{ record.account_name || '-' }}</div>
          </Badge>
        </template>
        <template v-if="column.key === 'files'">
          <div v-for="(newVal, index) in record.files" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
      </template>

      <template #form-amount="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber v-model:value="model.amount1" placeholder="应收金额最小值" style="height: 35px" :precision="4" />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber v-model:value="model.amount2" placeholder="应收金额最大值" style="height: 35px" :precision="4" />
          </div>
        </FormItemRest>
      </template>

      <template #footer>
        <div class="footer">
          <span> 应付金额合计： {{ formateerNotCurrency.format(totalAmount, 2) }} </span>
          <span>本次应付金额合计：{{ formateerNotCurrency.format(totalAmountCost, 2) }}</span>
          <span>本次已付金额合计：{{ formateerNotCurrency.format(totalAmountPaid, 2) }}</span>
        </div>
      </template>
    </BasicTable>
    <PaymentOrderDrawer @register="registerDrawer" @success="handleSuccess" />
    <DetailsDrawer @register="registerDetailsDrawer" @success="handleSuccess" />
    <PaymentOrderModal @register="registerPaymentModal" @success="handleSuccess" />
    <ExaminePaymentOrder @register="registerExamineDrawer" @success="handleSuccess" />
    <PreviewFile @register="registerModal" />
    <exportDrawer @register="registerExportDrawer" />
  </div>
</template>

<script setup lang="ts">
import {
  getPaymentOrderList,
  delPaymentOrder,
  financepcsetInChargeStatus,
  jobexportPaymentList
} from '/@/api/financialDocuments/paymentOrder'
import { columnsFn, searchFromSchemas, tableRef } from './datas/datas'
import PaymentOrderDrawer from './components/PaymentOrderDrawer.vue'
import DetailsDrawer from './components/DetailsDrawer.vue'
import ExaminePaymentOrder from './components/ExaminePaymentOrder.vue'
import PaymentOrderModal from './components/PaymentOrderModal.vue'

import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { message, InputNumber, Form, Badge, Button } from 'ant-design-vue'
import { ref } from 'vue'

import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useModal } from '/@/components/Modal/src/hooks/useModal'
import { useRoute } from 'vue-router'
import { add } from '/@/utils/math'
import { isNullAndUnDef } from '/@/utils/is'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import exportDrawer from './components/exportDrawer.vue'

const FormItemRest = Form.ItemRest
const route = useRoute()
const { path: routePath } = route
const pathname = window.location.pathname
const totals = ref(0)

const emit = defineEmits(['registerDrawer', 'registerDetailsDrawer', 'registerExamineDrawer', 'registerTable'])
console.log(emit)

let totalAmount = ref(0)
let totalAmountCost = ref(0)
let totalAmountPaid = ref(0)
//导出loding
const exporting = ref(false)

// const paymentInput: Recordable = reactive({
//   remarksInput: '',
//   dateInput: dayjs(Date.now())
// })

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()
const [registerExamineDrawer, { openDrawer: openExamineDrawer, setDrawerProps: setExamineDrawerProps }] = useDrawer()
const [registerPaymentModal, { openModal }] = useModal()
const [registerExportDrawer, { openDrawer: openExportDrawer }] = useDrawer()

/** 注册列表表格 */
const [registerTable, { reload, getForm, setLoading }] = useTable({
  title: '付款单列表',
  showIndexColumn: false,
  api: getPaymentOrderList,
  columns: columnsFn(),
  searchInfo: {
    is_finance: route.path === '/financialDocuments/paymentOrder' ? 0 : 1
  },
  beforeFetch: (params) => {
    const fieldsArr = ['collection_at_start', 'collection_at_end', 'check_at_start', 'check_at_end']
    for (const key of Object.keys(params)) {
      if (fieldsArr.includes(key) && isNaN(Date.parse(params[key]))) {
        params[key] = void 0
      }
    }
    return params
  },
  actionColumn: hasPermission([100, 101, 102, 103, 104, 614])
    ? {
        width: pathname == '/s/' ? 350 : 450,
        title: '操作',
        dataIndex: 'action'
      }
    : void 0,
  showTableSetting: true,
  afterFetch: (tableData) => {
    totalAmount.value = 0
    totalAmountCost.value = 0
    totalAmountPaid.value = 0
    tableData.forEach((item) => {
      totalAmount.value = add(totalAmount.value, Number(item.amount), 4)
      totalAmountCost.value = add(totalAmountCost.value, Number(item.amount_cost), 4)
      totalAmountPaid.value = add(totalAmountPaid.value, Number(item.amount_paid), 4)
    })
  },
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFromSchemas,
    fieldMapToTime: [
      ['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['collection_at', ['collection_at_start', 'collection_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

// onMounted(() => {
//   nextTick(() => {
//     setColumns(columnsFn(reload))
//   })
// })

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '主管审批',
      popConfirm: {
        title: '审批后订单进入执行中状态,且无法取消，是否确定执行？',
        placement: 'left',
        confirm: handleSetStatus.bind(null, record, 'start'),
        disabled: record.incharge_status !== 0
      },
      ifShow: hasPermission([614]) && pathname !== '/s/'
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '财务审批',
      onClick: handleExamine.bind(null, record, false),
      // disabled: record.is_check !== 0 || (record.clause === 2 && !isNullAndUnDef(record.is_show_check) && !record.is_show_check),
      disabled: record.is_check !== 0 || (record.incharge_status == 0 && pathname !== '/s/'),
      ifShow:
        hasPermission([103]) &&
        ((record.clause === 2 && !isNullAndUnDef(record.is_show_check) && record.is_show_check) || record.clause !== 2)
      // tooltip:
      //   record.clause === 2 && !isNullAndUnDef(record.is_show_check) && !record.is_show_check // is_show_check: 采购单已经通过主管审核为1
      //     ? {
      //         title: '采购单未通过主管审核！',
      //         placement: 'top'
      //       }
      //     : void 0
    },
    {
      icon: 'clarity:success-line',
      label: '已付款',
      // popConfirm: {
      //   okText: '确定',
      //   // title: '确定将状态设为已付款吗？',
      //   cancelText: '取消',
      //   placement: 'left',
      //   confirm: handleChangeStatus.bind(null, record),
      //   visibleChange: handleVisibleChange.bind(null),
      //   slots: returnVnode()
      // },
      onClick: handleChangeStatus.bind(null, record),
      disabled: (record.incharge_status == 0 && pathname !== '/s/') || !(record.is_check == 1 && record.status == 0),
      ifShow: hasPermission([229])
    },
    {
      icon: 'ant-design:plus-square-outlined',
      label: '关联流水',
      onClick: handleUpdate.bind(null, record),
      disabled: (record.incharge_status == 0 && pathname !== '/s/') || record.is_check == 2 || record.status !== 1,
      ifShow: hasPermission([102])
    }
  ]

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([100])
    },
    {
      icon: 'ant-design:eye-outlined',
      label: '编辑',
      onClick: handleedit.bind(null, record),
      disabled: (record.clause == 4 && record.is_check == 2) || record.is_check !== 2,
      ifShow: hasPermission([544])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.clause == 4 ? true : record.is_check === 1
      },
      ifShow: hasPermission([104])
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '出纳审批',
      onClick: handleExamine.bind(null, record, true),
      disabled: (record.incharge_status == 0 && pathname !== '/s/') || record.is_check == 2 || record.status == 2 || record.is_check !== 1,
      ifShow: hasPermission([230])
    },
    {
      label: '取消主管审批',
      popConfirm: {
        title: '取消审批后订单进入未审批状态,且无法取消，是否确定执行？',
        placement: 'left',
        confirm: handleSetStatus.bind(null, record, 'end'),
        disabled: !(record.incharge_status == 1 && record.is_check == 0)
      },
      ifShow: hasPermission([615]) && pathname !== '/s/'
    }
  ]
}

// /** 监听点击已付款时输入备注的值 */
// const handleRemarksInput = (e) => {
//   paymentInput.remarksInput = e
// }

// /** 监听点击已付款时输入付款日期的值 */
// const handleDateInput = (e) => {
//   if (!e) {
//     paymentInput.dateInput = undefined
//   } else {
//     paymentInput.dateInput = dayjs(e)
//   }
// }

// /** Popconfirm组件点击显示隐藏触发事件 */
// function handleVisibleChange() {
//   paymentInput.remarksInput = ''
//   paymentInput.dateInput = dayjs(Date.now())
// }

//主管审核
async function handleSetStatus(record, params) {
  const { news } = await financepcsetInChargeStatus({ incharge_status: params === 'start' ? 1 : 0, fdoc_id: record.id })
  news === 'success'
    ? createMessage.success(params === 'start' ? '审核成功' : '取消成功')
    : createMessage.error(params === 'start' ? '审核失败' : '取消失败')
  await reload()
}
/** 审批 */
function handleExamine(record, isCashier) {
  openExamineDrawer(true, {
    record,
    isCashier
  })
  setExamineDrawerProps({ title: '审批付款单' })
}

/** 关联流水 */
function handleUpdate(record, e) {
  e.stopPropagation()
  openDrawer(true, {
    record
  })
  setDrawerProps({ title: '付款单关联流水' })
}

/** 详情 */
function handleDetail(record) {
  // e.stopPropagation()
  setDetailsDrawerProps({ title: '付款单详情' })
  openDetailsDrawer(true, {
    id: record.id,
    showProjectList: true,
    type: 'detail'
  })
}
/** 编辑 */
function handleedit(record) {
  setDetailsDrawerProps({ title: '付款单编辑', showFooter: true })
  openDetailsDrawer(true, {
    id: record.id,
    showProjectList: true,
    type: 'edit'
  })
}

/** 删除 */
async function handleDelete(record) {
  try {
    await delPaymentOrder({ id: record.id })
    message.success('删除成功！')
    reload()
  } catch (error) {
    message.error('删除失败！')
    throw new Error(`${error}`)
  }
}

/** 点击已付款 */
async function handleChangeStatus(record) {
  // try {
  // let { dateInput, remarksInput } = paymentInput
  // await cashierChangeStatus({ id: record.id, remark: remarksInput, collection_at: dayjs(dateInput).format('YYYY-MM-DD') })
  //   message.success('操作成功！')
  //   reload()
  // } catch (error) {
  //   throw new Error(`${error}`)
  // }

  openModal(true, {
    record
  })
}

/** 返回vnode*/
// const returnVnode = (): VNode => {
//   let { dateInput, remarksInput } = paymentInput
//   return h('div', {}, [
//     h('span', null, '请输入付款备注：(按确定后状态将不可变！)'),
//     h(Textarea, { value: remarksInput, 'onUpdate:value': handleRemarksInput }),
//     h('span', null, '付款日期：'),
//     h(DatePicker, { 'onUpdate:value': handleDateInput, value: dateInput, style: { marginTop: '10px' } })
//   ])
// }

function handleSuccess() {
  reload()
}
//导出
async function handleBeforeExport() {
  setLoading(true)
  const fordata = await getForm().getFieldsValue()
  const fieldsArr = ['collection_at_start', 'collection_at_end', 'check_at_start', 'check_at_end']
  for (const key of Object.keys(fordata)) {
    if (fieldsArr.includes(key) && isNaN(Date.parse(fordata[key]))) {
      fordata[key] = void 0
    }
  }
  try {
    exporting.value = true
    await jobexportPaymentList({ ...fordata, is_finance: 0, pageSize: totals.value })
    message.success('导出任务已添加，请前往文件导出查看下载')
    exporting.value = false
  } catch (e) {
    message.error('下载失败')
  } finally {
    setLoading(false)
    exporting.value = false
  }
}
function handlepurhaseExportAll() {
  openExportDrawer(true)
}

function onPurchaseFetchSuccess({ items, total }) {
  console.log(items, total)
  totals.value = total
}

//预览
const { createMessage } = useMessage()
const [registerModal, { openModal: openMODAL }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openMODAL(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>

<style lang="less" scoped>
.footer {
  font-size: 15px;
  font-weight: bold;
  span:nth-of-type(2) {
    margin-left: 7%;
  }
  span:nth-of-type(3) {
    margin-left: 7%;
  }
}
</style>
