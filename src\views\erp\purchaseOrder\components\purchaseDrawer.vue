<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" :maskClosable="!propsData.news" show-footer>
    <template #footer v-if="['add', 'edit'].includes(propsData.type)">
      <Button @click="closeDrawer" v-if="!propsData.news">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </template>
    <ScrollContainer>
      <BasicForm @register="registerForm" @field-value-change="handleFieldChange">
        <template #totalSalesPriceSlot>
          <InputNumber v-model:value="allTotalPriceArr[1]" :precision="4" :disabled="true" />
        </template>
        <template #totalPriceSlot>
          <InputNumber v-model:value="allTotalPriceArr[2]" :precision="4" :disabled="true" />
        </template>
        <template #costSlot>
          <InputNumber v-model:value="allTotalPriceArr[0]" :precision="4" :disabled="true" />
        </template>
        <template #TaxAmountSlot>
          <InputNumber v-model:value="allTotalPriceArr[3]" :precision="4" :disabled="true" />
        </template>
        <template #addpoint>
          <InputNumber v-model:value="allTotalPriceArr[4]" :precision="4" :disabled="true" />
        </template>
        <template #currencyAmount>
          <InputNumber v-model:value="allTotalPriceArr[5]" :precision="4" :disabled="true" />
        </template>
        <template #Imgs>
          <Upload
            v-model:file-list="imgFileList"
            action="/api/oss/putImg2Stocking"
            list-type="picture-card"
            :custom-request="handleRequest"
            :disabled="!propsData.isUpdate"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #Files>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :disabled="!propsData.isUpdate"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
            @preview="uploadpreview"
          >
            <a-button type="primary">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
          <!-- <div v-for="(item, index) in filesList" :key="index" class="w-100">
            <div class="button-container flex items-center">
              <div>{{ item.name }}</div>
              <Button type="link" @click="handlePreview(item.url, $event)">预览</Button>
              <Button type="link" @click="handleDelFile(index)">删除</Button>
            </div>
            <Progress v-if="item.percent" :percent="item.percent" size="small" />
          </div> -->
        </template>
        <template #Goods="{ model }">
          <FormItemRest>
            <VxeBasicTable
              ref="tableRef"
              v-bind="gridOptions"
              show-footer
              :footer-span-method="footerSpanMethod"
              :footer-method="footerMethod"
              :expand-config="{
                visibleMethod({ row }) {
                  if (row.items_sub.length == 0) {
                    return false
                  }
                  return true
                },
                isFooter: true,
                accordion: true
              }"
              :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
              :edit-rules="validRules"
            >
              <template #Imgs="{ row, column, rowIndex }">
                <template v-if="column.field === 'imgs' && model.sales_goods[rowIndex]">
                  <div class="vxe-img">
                    <TableImg v-if="isArray(row.imgs) && row.imgs.length > 0" :size="60" :simpleShow="true" :imgList="row.imgs" />
                  </div>
                </template>
              </template>
              <template #SupplierId="{ column, row, rowIndex }">
                <template v-if="column.field === 'supplier_id' && model.sales_goods[rowIndex]">
                  <Select
                    v-model:value="row.supplier_id"
                    :showSearch="true"
                    :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate"
                    :options="supplierData"
                    :fieldNames="{ value: 'id', label: 'name' }"
                    optionFilterProp="name"
                    @change="handlesupplier"
                    @dropdown-visible-change="handleticket(row)"
                  />
                </template>
              </template>
              <template #PlatId="{ column, row, rowIndex }">
                <template v-if="column.field === 'plant_id' && model.sales_goods[rowIndex]">
                  <Select
                    v-model:value="row.plant_id"
                    :showSearch="true"
                    :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate"
                    :options="supplierData"
                    :fieldNames="{ value: 'id', label: 'name' }"
                    optionFilterProp="name"
                  />
                </template>
              </template>
              <template #QtyPurchased="{ column, row, rowIndex }">
                <template v-if="column.field === 'qty_purchased' && model.sales_goods[rowIndex]">
                  <InputNumber
                    v-model:value="row.qty_purchased"
                    :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate || row.items_sub.length > 0"
                    :min="0.01"
                    :max="row.qty_request_left"
                    :default-value="1"
                    :precision="2"
                  />
                </template>
              </template>
              <template #currencyUnitPirce="{ column, row, rowIndex }" v-if="!ratechange">
                <template v-if="column.field === 'foreign_currency_unit_pirce' && model.sales_goods[rowIndex]">
                  <InputNumber
                    v-model:value="row.foreign_currency_unit_pirce"
                    :disabled="ratechange || propsData.record?.is_check == 2 || !propsData.isUpdate || row.items_sub.length > 0"
                    :min="0"
                    :precision="4"
                    @change="handleUnitPrice($event, row, 'foreign_currency_unit_pirce')"
                  />
                </template>
              </template>
              <template #UnitPrice="{ column, row, rowIndex }">
                <template v-if="column.field === 'unit_price' && model.sales_goods[rowIndex]">
                  <InputNumber
                    v-model:value="row.unit_price"
                    :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate || row.items_sub.length > 0 || !ratechange"
                    :min="0"
                    :precision="4"
                    @change="handleUnitPrice($event, row, 'unit_price')"
                  />
                </template>
              </template>
              <template #UnitPriceTax="{ column, row, rowIndex }">
                <template v-if="column.field === 'unit_price_tax' && model.sales_goods[rowIndex]">
                  <InputNumber
                    v-model:value="row.unit_price_tax"
                    :disabled="true"
                    :min="0"
                    :precision="4"
                    @change="handleUnitPrice($event, row, 'unit_price_tax')"
                  />
                </template>
              </template>
              <template #CostPrice="{ column, row, rowIndex }">
                <template v-if="column.field === 'cost_price' && model.sales_goods[rowIndex]">
                  <InputNumber
                    v-model:value="row.cost_price"
                    :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate || row.items_sub.length > 0 || !ratechange"
                    :min="0"
                    :precision="4"
                    @change="handleUnitPrice($event, row, 'cost_price')"
                  />
                </template>
              </template>
              <template #Unit="{ column, row, rowIndex }">
                <template v-if="column.field === 'unit' && model.sales_goods[rowIndex]">
                  <Input v-model:value="row.unit" :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate" />
                </template>
              </template>
              <template #TotalCost="{ column, row, rowIndex }">
                <template v-if="column.field === 'total_cost' && model.sales_goods[rowIndex]">
                  <!-- <span>{{ mul(row.qty_purchased || 0, row.unit_price || 0, 2) || 0 }}</span> -->
                  <span>{{ formatAmount(Number(row.cost_price || 0) * Number(row.qty_purchased || 0), 4) }}</span>
                </template>
              </template>
              <template #TotalCostTax="{ column, row, rowIndex }">
                <template v-if="column.field === 'total_cost_tax' && model.sales_goods[rowIndex]">
                  {{ formatAmount(Number(row.unit_price || 0) * Number(row.qty_purchased || 0), 4) }}
                </template>
              </template>
              <template #TotalSales="{ column, row, rowIndex }">
                <template v-if="column.field === 'total_sales' && model.sales_goods[rowIndex]">
                  <span>{{ mul(row.qty_purchased || 0, row.unit_sales || 0, 2) || 0 }}</span>
                </template>
              </template>
              <template #LossReason="{ column, row, rowIndex }">
                <template v-if="column.field === 'lose_remark' && model.sales_goods[rowIndex]">
                  <Select
                    v-model:value="row.lose_remark"
                    :options="lossReason"
                    :allowClear="true"
                    @change="handleLossReasonChange($event, row)"
                    :disabled="!propsData.isUpdate"
                  />
                </template>
              </template>
              <template #LossRemark="{ column, row, rowIndex }">
                <template v-if="column.field === 'loss_reason' && model.sales_goods[rowIndex]">
                  <Input v-model:value="row.loss_reason" :disabled="!(row.lose_remark == '备注其他原因' && propsData.isUpdate)" />
                </template>
              </template>
              <template #Action="{ row }">
                <TableAction :actions="createActions(row)" />
              </template>
              <template #ActionHeader="{ column }">
                <div class="text-center">{{ column.title }}</div>
                <div class="text-center" v-if="propsData.type == 'add'">
                  <Button type="primary" size="small" @click="handleBatchDelete">批量删除</Button>
                </div>
              </template>
              <template #SupplierHeader="{ column }">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的供应商"
                    @visible-change="handleHoverChange($event, 'supplier')"
                    :visible="visiblesupper"
                  >
                    <template #content>
                      <Select
                        style="width: 188px"
                        :options="supplierData"
                        v-model:value="supplierValue"
                        :fieldNames="{ value: 'id', label: 'name' }"
                        optionFilterProp="name"
                        show-search
                        placeholder="请选择批量设置的供应商"
                        @change="handlesupplierbatch"
                      />
                      <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit(supplierValue, 'supplier_id')">确定</Button>
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #TaxPoint="{ column, row, rowIndex }">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <template v-if="column.field === 'tax_point' && model.sales_goods[rowIndex]">
                  <InputNumber v-model:value="row.tax_point" :disabled="true" min="0" />
                </template>
              </template>
              <template #AddPoint="{ column, row, rowIndex }">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <template v-if="column.field === 'add_point' && model.sales_goods[rowIndex]">
                  <InputNumber v-model:value="row.add_point" min="0" max="100" @change="handleeditaddpoint($event, row)" />
                </template>
              </template>
              <template #PlatIdHeader="{ column }">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的加工商"
                    @visible-change="handleHoverChange($event, 'processor')"
                    :visible="visibleprocessor"
                  >
                    <template #content>
                      <Select
                        style="width: 188px"
                        :options="supplierData"
                        v-model:value="plant_value"
                        :fieldNames="{ value: 'id', label: 'name' }"
                        optionFilterProp="name"
                        show-search
                        placeholder="请选择批量设置的加工商"
                      />
                      <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit(plant_value, 'plant_id')">确定</Button>
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #UnitPriceHeader="{ column }">
                <div class="text-center">
                  <Popover
                    trigger="click"
                    @visible-change="handleHoverChange($event, 'total')"
                    :visible="visible"
                    title="批量关联已勾选产品的采购单价"
                  >
                    <template #content>
                      <div class="mb-5">
                        <div>当前成本总价: {{ new_total_price }} ￥</div>
                        新成本总价:
                        <InputNumber v-model:value="newtotal_price" :min="0" :precision="2" />
                      </div>
                      <Button style="margin-right: 5px; width: 100%" type="primary" @click="handleprice(newtotal_price)">确定</Button>
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #MaterialHeader="{ column }" v-if="['/s/', '/tests/'].includes(pathname)">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的材质"
                    @visible-change="handleHoverChange($event, 'Material')"
                    :visible="visiblematerial"
                  >
                    <template #content>
                      <div class="mb-5">
                        <Input v-model:value="Material" />
                      </div>
                      <Button style="margin-left: 5px; width: 100%" type="primary" @click="handleBatchEdit(supplierValue, 'Material')"
                        >确定</Button
                      >
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #CodelHeader="{ column }" v-if="['/s/', '/tests/'].includes(pathname)">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的海关码"
                    @visible-change="handleHoverChange($event, 'Code')"
                    :visible="visiblecode"
                  >
                    <template #content>
                      <div class="mb-5">
                        <Input v-model:value="Code" />
                      </div>
                      <Button style="margin-left: 5px; width: 100%" type="primary" @click="handleBatchEdit(supplierValue, 'Code')"
                        >确定</Button
                      >
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #BrandHeader="{ column }" v-if="['/s/', '/tests/'].includes(pathname)">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的品牌"
                    @visible-change="handleHoverChange($event, 'Brand')"
                    :visible="visiblebrand"
                  >
                    <template #content>
                      <div class="mb-5"> <Input v-model:value="brand" /> </div>
                      <Button style="margin-left: 5px; width: 100%" type="primary" @click="handleBatchEdit(supplierValue, 'Brand')"
                        >确定</Button
                      >
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #InvoiceHeader="{ column }" v-if="['/s/', '/tests/'].includes(pathname)">
                <!--                <div class="text-center">{{ column.title }}</div>-->
                <div class="text-center">
                  <Popover
                    trigger="click"
                    title="批量关联已勾选产品的开票品名"
                    @visible-change="handleHoverChange($event, 'Invoicename')"
                    :visible="visibleinvoice"
                  >
                    <template #content>
                      <div class="mb-5">
                        <Input v-model:value="invoicename" />
                      </div>
                      <Button style="margin-left: 5px; width: 100%" type="primary" @click="handleBatchEdit(supplierValue, 'Invoicename')"
                        >确定</Button
                      >
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.title }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </div>
              </template>
              <template #expandContent="{ row: rowsplit }">
                <VxeBasicTable
                  v-bind="gridOptionssplit"
                  ref="tablesplitRef"
                  :columns="tablecolum(propsData.type).filter((item) => isUndefined(item.ifShow) || item.ifShow)"
                  :data="rowsplit.items_sub"
                >
                  <template #Imgs="{ row, column }">
                    <template v-if="column.field === 'imgs'">
                      <div class="vxe-img">
                        <TableImg v-if="isArray(row.imgs) && row.imgs.length > 0" :size="60" :simpleShow="true" :imgList="row.imgs" />
                      </div>
                    </template>
                  </template>
                  <template #Files="{ row, column }">
                    <template v-if="column.field === 'files'">
                      <div class="vxe-img">
                        <div v-for="(newVal, index) in row.files" :key="index">
                          <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                        >
                      </div>
                    </template>
                    <template v-if="column.key === 'files'"> </template>
                  </template>
                  <template #Action="{ row }">
                    <TableAction :actions="createsplitActions(row)" v-if="rowsplit.items_sub.length > 1" />
                  </template>
                  <template #ActionHeader="{ column }">
                    <div class="text-center">{{ column.title }}</div>
                  </template>
                  <template #Quantity="{ column, row }">
                    <template v-if="column.field === 'quantity'">
                      <InputNumber
                        v-model:value="row.quantity"
                        :min="0"
                        :precision="0"
                        :max="row.quantity_left"
                        :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate"
                        @change="handleExpandChange(rowsplit)"
                      />
                    </template>
                  </template>
                  <template #UnitPprice="{ column, row }">
                    <template v-if="column.field === 'unit_price'">
                      <InputNumber
                        v-model:value="row.unit_price"
                        :min="0"
                        :precision="2"
                        default-value="0"
                        :disabled="propsData.record?.is_check == 2 || !propsData.isUpdate"
                        @change="handleExpandChange(rowsplit)"
                      />
                    </template>
                  </template>
                  <!-- <template #proportion_org="{ column, row }">
                    <template v-if="column.field === 'proportion_org'">
                      <div>{{ row.proportion_org }}%</div>
                    </template>
                  </template>
                  <template #Proportion="{ column, row }">
                    <template v-if="column.field === 'proportion'">
                      <div>{{ row.proportion }}</div>
                    </template>
                  </template> -->
                </VxeBasicTable>
              </template>
              <template #expandHeader> 查看子产品 </template>
            </VxeBasicTable>
          </FormItemRest>
        </template>
      </BasicForm>
    </ScrollContainer>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script lang="ts" setup name="purchaseDrawer">
// import { storeToRefs } from 'pinia'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, DrawerInstance, useDrawerInner } from '/@/components/Drawer'
import { TableAction, TableImg, ActionItem } from '/@/components/Table'
import { getDrawerTableColumns, getSchemasList, tablecolum, orderType, lossReason, getSalesWork, validRules } from '../datas/drawerDatas'
import { BasicForm, useForm } from '/@/components/Form'
import { commonImgUpload, commonFileUpload } from '/@/api/commonUtils/upload'
import { computed, nextTick, reactive, ref, watch, watchEffect } from 'vue'
import { Upload, UploadFile, Form, InputNumber, Popover, Select, Button, message, Input } from 'ant-design-vue'
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import type { IBasicForm, ItemRequestResponse, PropsType } from '/@/views/erp/purchaseOrder/datas/types'
import { getItemRequest, getWorkList } from '/@/api/commonUtils'
import { createPurchase, editPurchase, getPurchaseDetail } from '/@/api/erp/purchaseOrder'
// import ApiSelect from '/@/components/Form/src/components/ApiSelect.vue'
import { add, mul } from '/@/utils/math'
// import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useMessage } from '/@/hooks/web/useMessage'
import type { CreateOrEditPurchase } from '/@/api/erp/modle/types'
import { EditOutlined } from '@ant-design/icons-vue'
import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { SupplierItem } from '/@/types/store'
import { cloneDeep, isArray, isUndefined } from 'lodash-es'
import { isNull } from '/@/utils/is'
import defaultUser from '/@/utils/erp/defaultUser'
import { BasicTableProps, VxeBasicTable, VxeGridInstance, VxeTablePropTypes } from '/@/components/VxeTable'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview/index'
import { useModal } from '/@/components/Modal'
import Decimal from 'decimal.js'

const [registerModal, { openModal }] = useModal()

// 标准四舍五入函数，解决第三位为5时的精度问题
function formatAmount(num: number, decimals = 2): string {
  if (isNaN(num)) return '0.00'
  // 使用 Math.round 确保标准四舍五入
  const rounded = Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
  return rounded.toFixed(decimals)
}

// 数值四舍五入函数
function roundNumber(num: number, decimals = 2): number {
  if (isNaN(num)) return 0
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

const FormItemRest = Form.ItemRest
//退货新建采购单 retreat标识
const propsData = ref<PropsType>({ type: '', isUpdate: false, page: 'purchase' })
const imgFileList = ref<UploadFile[]>([])
const filesList = ref<UploadFile[]>([])
const emit = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()
const { createMessage } = useMessage()
const plant_value = ref()
const supplierValue = ref<number>()
const supplierData = ref<SupplierItem[]>([]) //加工商
const basicForm = ref<IBasicForm>()
const gridDataSource = ref([])
const loading = ref<boolean>(false)
const tableRef = ref<VxeGridInstance>()
const tablesplitRef = ref<VxeGridInstance>()
const DataSource = ref([])
//供应商数组
const supplierArray = ref<any>([])
//尾部展示
const footergettable = ref<any>([])
const footercheecktable = ref<any>([])
const ratechange = ref(true)
const pathname = window.location.pathname
//批量
const visiblematerial = ref(false)
const visiblecode = ref(false)
const visiblebrand = ref(false)
const visibleinvoice = ref(false)
const Material = ref()
const Code = ref()
const brand = ref()
const invoicename = ref()

const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  // keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  rowConfig: {
    keyField: 'id'
  },
  columns: [],
  height: '500px',
  data: gridDataSource,
  toolbarConfig: {
    enabled: false
  },
  tableStyle: { padding: '0!important' },
  proxyConfig: null,
  headerAlign: 'center',
  'scroll-y': { enabled: true, gt: 20 }
})
//子产品表格
const gridOptionssplit = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  // keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  rowConfig: {
    keyField: 'id'
  },
  // data: gridDataSource,
  toolbarConfig: {
    enabled: false
  },
  tableStyle: { padding: '0!important' },
  proxyConfig: null,
  headerAlign: 'center',
  'scroll-y': { enabled: true, gt: 20 }
})

const [registerDrawer, { changeLoading, setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  setDrawerProps({ confirmLoading: false })
  changeLoading(true)
  propsData.value = data
  const mapStore = useMapStoreWithOut()
  supplierData.value = mapStore.supplierList
  try {
    await resetFields()
    // 如果是驳回状态
    await updateSchema(
      getSchemasList(propsData.value.isUpdate, propsData.value.type, propsData.value.page, { validateFields, handleSalesOrderChange })
    )

    // setTableData([])
    // setColumns(getDrawerTableColumns(propsData.value.type))
    gridDataSource.value = []
    // basicForm.value = getFieldsValue()
    if (propsData.value.record) {
      // 供应商回显
      if (data.record?.supplier_name && data.record?.supplier_id) {
        updateSchema({
          field: 'supplier_id',
          componentProps: { defaultOptions: [{ name: data.record?.supplier_name, id: data.record?.supplier_id }], disabled: true }
        })
      }
      // imgFileList.value = propsData.value.record!.imgs.map((item) => ({ url: item, name: item, uid: item }))
      const { auditor, client_id, client_name, cost, currency, inCharge, name, total_price, basic_work_id } = propsData.value.record.work
      if (basic_work_id) {
        const {
          items: [salesWork]
        } = await getWorkList({ id: basic_work_id })
        data.record.work = salesWork
        updateSchema({
          field: 'client',
          show: salesWork.type === 3 ? true : false
        })
      }
      filesList.value = propsData.value.record?.files?.map((item) => ({ url: item, name: item, uid: item }))
      await setFieldsValue({
        ...propsData.value.record,
        sales_goods: [],
        processor: +propsData.value.record.processor,
        auditor,
        cost,
        currency,
        inCharge,
        name,
        total_price,
        rate: propsData.value.record.exchange_rate,
        client: { value: client_id, label: client_name }
      })
      console.log(currency)

      ratechange.value = ['人民币', 'CNY'].includes(currency) ? true : false
    } else {
      // imgFileList.value = []
      filesList.value = []
      if (data.page === 'saleOrder') {
        getSalesWork.value = data.saleRecord.id
        setFieldsValue({ work_id: data.saleRecord.source_uniqid })
      }
    }

    if (data.type === 'add') {
      await setFieldsValue({
        inCharge: defaultUser!.userId,
        processor: defaultUser!.userId
      })
    }

    if (data.type === 'add' && data.news) {
      const res = await getWorkList({ id: data.news.id })
      await updateSchema([
        {
          field: 'work_id',
          componentProps: {
            defaultOptions: [{ source_uniqid: res.items[0].source_uniqid, id: res.items[0].id }],
            disabled: true
          }
        }
      ])
      await setFieldsValue({
        work_id: res.items[0].id
      })
      await handleSalesOrderChange(res.items[0].id, {
        client_id: res.items[0].client_id,
        client_name: res.items[0].client_name,
        dept_id: res.items[0].dept_id
      })
    }

    basicForm.value = getFieldsValue() as IBasicForm
    gridOptions.columns = getDrawerTableColumns(data.type, basicForm.value.currency).filter(
      (item) => isUndefined(item.ifShow) || item.ifShow
    )
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { setFieldsValue, resetFields, updateSchema, validate, validateFields, getFieldsValue }] = useForm({
  schemas: getSchemasList(true, propsData.value.type, propsData.value.page),
  actionColOptions: { span: 24 },
  labelWidth: 200,
  // baseColProps: { span: 24 },
  showActionButtonGroup: false
})

async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const curFile = filesList.value.find((item) => item.uid === file.uid)
  const result = await commonImgUpload(file, 'purchase', curFile)
  onSuccess!(result.path)
  if (!result.path) {
    message.error('上传失败')
    filesList.value = filesList.value!.filter((item) => item.url)
    changeOkLoading(false)
    return
  }
  imgFileList.value = imgFileList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ imgs: imgFileList.value.map((item) => item.url) })
  await nextTick(async () => {
    try {
      await validateFields(['imgs'])
    } catch (e) {
      throw new Error(`${e}`)
    }
  })
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({ files: filesList.value.map((item) => item.url) })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

async function handleSubmit() {
  // await changeOkLoading(true)
  try {
    let valid
    try {
      valid = await validate()
    } catch (validationError: any) {
      // 捕获验证错误并显示 message 提示
      if (typeof validationError === 'string') {
        message.error(validationError)
      } else if (validationError?.errorFields) {
        // 处理表单验证错误
        const errorMessages = validationError.errorFields.map((field: any) => field.errors.join(', ')).join('\n')
        message.error(errorMessages)
      } else {
        message.error('表单验证失败，请检查输入信息')
      }
      return
    }
    for (let item of valid.sales_goods) {
      if (item.items_sub?.length > 0) {
        // 主产品总价：先计算，再四舍五入
        const retoprice = roundNumber(Number(item.qty_purchased) * Number(item.unit_price), 4)
        // 子产品总价：先累加所有子产品金额，再四舍五入
        const subprice = roundNumber(
          item.items_sub.reduce((acc, cur) => {
            return acc + Number(cur.quantity) * Number(cur.unit_price)
          }, 0),
          4
        )

        if (subprice == 0) {
          message.error(`${item.name}的子产品采购金额未填写`)
          throw new Error('子产品采购金额未填写')
        }
        if (retoprice !== subprice) {
          message.error(`${item.name}的采购金额与子产品采购金额不一致`)
          throw new Error('子产品金额与主产品金额不一致')
        }
      }
    }
    console.log(valid.sales_goods)

    // debugger
    const {
      supplier_id,
      work_id,
      dept_id,
      files,
      client,
      auditor,
      inCharge,
      name,
      currency,
      cost,
      processor,
      work,
      arrival_at,
      is_gbuilder,
      gbuilder_percentage,
      contracting_party,
      invoice_type,
      add_point,
      is_engineering,
      enterprise_name,
      rate,
      foreign_currency_amount
    } = valid
    const result_auth = ref(false)
    valid.sales_goods.forEach((item) => {
      if (item.unit_price == 0) {
        result_auth.value = true
        return
      }
    })
    const params: CreateOrEditPurchase = {
      doc: {
        // work_id,
        arrival_at,
        dept_id,
        files: (files ?? []).map((item: UploadFile) => item.url).filter((item) => !isNull(item)),
        // imgs: imgs.map((item: UploadFile) => item.url),
        processor: processor,
        id: propsData.value.type === 'edit' && propsData.value.record?.id ? propsData.value.record.id : undefined,
        // supplier_id: supplier_id
        total_price: allTotalPriceArr.value[0],
        sales_price: allTotalPriceArr.value[1],
        cost_price: allTotalPriceArr.value[0],
        is_gbuilder,
        gbuilder_percentage,
        contracting_party,
        invoice_type,
        is_engineering,
        add_point: propsData.value.type === 'edit' ? add_point : null,
        enterprise_name,
        exchange_rate: rate,
        foreign_currency_amount,
        currency
      },
      items: [
        ...valid.sales_goods.map((item: ItemRequestResponse) => ({
          // ...item,
          name: item.name,
          request_id: ['edit', 'detail'].includes(propsData.value.type) ? item.request_id : item.id,
          imgs: item.imgs ? (Object.prototype.toString.call(item.imgs) === '[object Array]' ? item.imgs : JSON.parse(item.imgs)) : [],
          puid: item.puid ? item.puid : '',
          remark: item.remark,
          desc: item.desc,
          // name: item.name,
          plant_id: item.plant_id,
          qty_purchased: Number(item.qty_purchased) ?? 1,
          supplier_id: item.supplier_id,
          unit: item.unit,
          unit_price: Number(item.unit_price),
          id: item.id,
          sales_price: item.unit_sales,
          width: item.width ?? void 0,
          height: item.height ?? void 0,
          length: item.length ?? void 0,
          tax_point: item.tax_point,
          add_point: propsData.value.type === 'edit' ? null : item.add_point,
          cost_price: item.cost_price,
          unit_price_tax: Number(item.unit_price_tax), // 含税单价
          foreign_currency_unit_pirce: Number(item.foreign_currency_unit_pirce), // 外币单价
          material: pathname == '/s/' ? item.material : undefined,
          code: pathname == '/s/' ? item.code : undefined,
          brand: pathname == '/s/' ? item.brand : undefined,
          invoice_name: pathname == '/s/' ? item.invoice_name : undefined,
          lose_remark:
            item.lose_remark === null || item.lose_remark === undefined || item.lose_remark === ''
              ? null
              : item.lose_remark !== '备注其他原因'
              ? item.lose_remark
              : !item.loss_reason || item.loss_reason.trim() === ''
              ? (() => {
                  message.error(`商品 "${item.name}" 选择了备注其他原因，但未填写具体原因`)
                  throw new Error('备注其他原因的具体内容不能为空')
                })()
              : `备注其他原因:${item.loss_reason}`,
          // supplier_id: supplier_id,
          items_sub:
            item.items_sub?.map((val) => ({
              request_id: val.request_id,
              sale_work_id: val.work_id,
              name: val.name,
              quantity: val.quantity,
              proportion: val.proportion,
              remark: val.remark,
              desc: val.desc,
              imgs: val.imgs,
              files: val.files,
              unit: val.unit,
              unit_price: Number(val.unit_price),
              request_sub_id: propsData.value.type === 'edit' ? val.request_sub_id : propsData.value.news ? val.request_sub_id : val.id,
              item_purchase_id: propsData.value.type === 'edit' ? val.item_purchase_id : undefined,
              work_id: propsData.value.type === 'edit' ? val.work_id : undefined,
              doc_id: propsData.value.type === 'edit' ? val.doc_id : undefined,
              id: propsData.value.type === 'edit' && val.id ? val.id : undefined,
              width: val.width ?? void 0,
              height: val.height ?? void 0,
              length: val.length ?? void 0
            })) ?? []
        }))
      ],
      work: {
        name,
        client_id: orderType.value === 3 ? client.value : undefined,
        client_name: orderType.value === 3 ? client.label : undefined,
        dept_id,
        auditor,
        inCharge,
        currency,
        total_price: allTotalPriceArr.value[0],
        cost,
        parent_id:
          propsData.value?.page === 'saleOrder'
            ? propsData.value.saleRecord!.id
            : propsData.value.type === 'add'
            ? work_id
            : work.parent_id
            ? work.parent_id
            : void 0,
        // 神奇，这里的work跟数据库中的work竟然不一直
        // 数据库的work.basic_work_id一直是空，但是采购单列表的work，竟然没审核都有work.basic_work_id
        // 估计是列表的work是后端查出来返回的，
        basic_work_id:
          propsData.value?.page === 'saleOrder'
            ? propsData.value.saleRecord!.id
            : propsData.value.type === 'add'
            ? work_id
            : work.basic_work_id
            ? work.basic_work_id
            : void 0
      }
    }
    for (let item of params.items) {
      if (propsData.value.type === 'edit') {
        delete item.supplier_id, delete item.total_cost
      }

      if (propsData.value.type === 'add') {
        delete item.id
        delete item.created_at
        delete item.updated_at
      }
      if (!item.unit) {
        message.error('采购商品单位不能为空')
        throw new Error('采购商品单位不能为空')
      }
    }
    if (propsData.value.type === 'edit') {
      params.doc = {
        ...params.doc,
        work_id,
        supplier_id,
        has_fund: propsData.value.record!.has_fund,
        status: propsData.value.record!.status
      }
    }
    console.log(params)

    // 退货新建采购单 createandRetreat
    if (allTotalPriceArr.value[2] > allTotalPriceArr.value[1] && params.items.some((item) => item.lose_remark === null)) {
      //   Modal.confirm({
      //     title: '该订单采购总价大于销售总价，是否继续？',
      //     okText: '确定',
      //     okType: 'danger',
      //     cancelText: '取消',
      //     async onOk() {
      //       const result = propsData.value.type === 'edit' ? await editPurchase(params) : await createPurchase(params)
      //       emit('success')
      //       createMessage.success(result.msg)
      //       await closeDrawer()
      //     }
      //   })
      message.error('该订单采购总价大于销售总价,故所有商品的亏本原因不能为空')
      throw new Error('该订单采购总价大于销售总价,故所有商品的亏本原因不能为空')
    } else {
      const result = propsData.value.type === 'edit' ? await editPurchase(params) : await createPurchase(params)
      emit('success')
      createMessage.success(result.msg)
      await closeDrawer()
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}

const childrenItem = ref()
async function handleSalesOrderChange(val: number, shall: any) {
  tabledata.value = []
  loading.value = true
  tableRef.value?.clearSelected()

  gridDataSource.value = []
  try {
    let page = propsData.value.page === 'saleOrder' ? 'saleOrder' : undefined
    let saleRecord = page === 'saleOrder' ? propsData.value.saleRecord : undefined
    let { items, doc } =
      propsData.value.type === 'add'
        ? await getItemRequest({ work_id: page === 'saleOrder' ? saleRecord?.id : val, pageSize: 500, is_finish_split: 1 })
        : await getPurchaseDetail({ doc_id: propsData.value.record!.id as number, pageSize: 500 })

    items.map((val) => {
      if (val.lose_remark && val.lose_remark !== null) {
        const parts = val.lose_remark.split(':', 2)
        if (parts.length === 2) {
          val.lose_remark = parts[0].trim()
          val.loss_reason = parts[1].trim()
        } else {
          val.loss_reason = ''
        }
      } else {
        val.loss_reason = ''
        val.lose_remark = ''
      }
    })

    items.forEach((item) => {
      item.add_point = doc?.add_point || 0
    })
    childrenItem.value = cloneDeep(items)
    if (propsData.value.news) {
      items = items
        .filter((item) => propsData.value.news.clonedate.some((val) => item.id === val.request_id))
        .map((item) => {
          const matchedVal = propsData.value.news.clonedate.find((val) => item.puid === val.puid)
          return { ...item, qty_purchased: matchedVal.qty_purchased, items_sub: matchedVal.items_sub }
        })
    }
    await setFieldsValue({
      sales_goods: (propsData.value.type === 'add'
        ? items
            .filter((item) => Number(item.qty_request_left) > 0 && item.items_sub?.filter((val) => Number(val.quantity_left) !== 0))
            .map((shall) => {
              return {
                ...shall,
                unit_price: Number(shall.unit_price),
                foreign_currency_unit_pirce: 0,
                items_sub: shall.items_sub?.map((shallval) => {
                  return {
                    ...shallval,
                    quantity:
                      Number(shallval.quantity_left) < Number(shallval.quantity)
                        ? Number(shallval.quantity_left)
                        : Number(shallval.quantity)
                  }
                })
              }
            })
        : items
      ).map((item) => ({
        ...item,
        supplier_id: '',
        // qty_request_left: ['edit', 'detail'].includes(propsData.value.type) ? item.qty_purchased : item.qty_request_left,
        qty_purchased: ['edit', 'detail'].includes(propsData.value.type) ? item.qty_purchased : item.qty_request_left
      }))
    })
    if (propsData.value.type === 'add') {
      await setFieldsValue({
        client: {
          value: page === 'saleOrder' ? saleRecord?.client_id : shall.client_id,
          label: page === 'saleOrder' ? saleRecord?.client_name : shall.client_name,
          option: {
            name: page === 'saleOrder' ? saleRecord?.client_name : shall.client_name,
            id: page === 'saleOrder' ? saleRecord?.client_id : shall.client_id
          }
        },
        dept_id: page === 'saleOrder' ? saleRecord?.dept_id : shall.dept_id
      })
      // mapTableToForm(items)
    }
    gridDataSource.value = getFieldsValue().sales_goods
    DataSource.value = cloneDeep(getFieldsValue().sales_goods)
    // setTableData(propsData.value.type === 'add' ? items.filter((item) => item.qty_request_left > 0) : items)
    basicForm.value = getFieldsValue() as IBasicForm
    // setTableData(items)
    // nextTick(() => calcTotalAndPay())
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    loading.value = false
    // setLoading(false)
  }
}

const computePriceFn = () => {
  const formData = tableRef.value?.getTableData()
  if (formData?.fullData) {
    // 应付 - 先累加所有金额，最后统一四舍五入
    const cost = formData.fullData.reduce((erp, item) => {
      return erp + Number(item.unit_price) * Number(item.qty_purchased)
    }, 0)

    // 销售总价 - 先累加所有金额，最后统一四舍五入
    const totalSalesPrice = formData.fullData.reduce((erp, item) => {
      return erp + Number(item.unit_sales) * Number(item.qty_purchased)
    }, 0)

    // 税金tax_point - 先累加所有金额，最后统一四舍五入
    const taxamount = formData.fullData.reduce((erp, item) => {
      return (
        erp +
        (Number(item.unit_price || 0) / (1 + (item.tax_point || 0) / 100)) * ((item.tax_point || 0) / 100) * Number(item.qty_purchased || 0)
      )
    }, 0)
    //开票税点加价金额
    const addpoint = (cost / (1 + (formData.fullData[0]?.add_point || 0) / 100)) * ((formData.fullData[0]?.add_point || 0) / 100)

    // 成本总价
    const totalPrice = formData.fullData.reduce((erp, item) => {
      return add(erp, mul(item.cost_price, item.qty_purchased, 4), 4)
    }, 0)
    // 外币金额 - 先累加所有金额，最后统一四舍五入
    const currencyAmount = formData.fullData.reduce((erp, item) => {
      return erp + Number(item.foreign_currency_unit_pirce) * Number(item.qty_purchased)
    }, 0)

    //是否进行价格统调
    if (price_auth.value) {
      let sales_goods = formData.fullData
      const cost_auth = sales_goods.reduce((erp, item) => {
        return erp + Number(item.unit_price) * Number(item.qty_purchased)
      }, 0)
      setFieldsValue({
        total_price: roundNumber(cost_auth, 2),
        cost: roundNumber(cost_auth, 2),
        sales_price: roundNumber(totalSalesPrice, 2),
        tax_amount: roundNumber(taxamount, 2),
        foreign_currency_amount: roundNumber(currencyAmount, 2)
      })
    } else {
      setFieldsValue({
        total_price: roundNumber(totalPrice, 2),
        cost: roundNumber(cost, 2),
        sales_price: roundNumber(totalSalesPrice, 2),
        tax_amount: roundNumber(taxamount, 2),
        addpoint: roundNumber(addpoint, 2),
        foreign_currency_amount: roundNumber(currencyAmount, 2)
      })
    }

    return [
      roundNumber(cost, 2),
      roundNumber(totalSalesPrice, 2),
      roundNumber(totalPrice, 2),
      roundNumber(taxamount, 2),
      roundNumber(addpoint, 2),
      roundNumber(currencyAmount, 2)
    ]
  } else {
    setFieldsValue({ total_price: 0, cost: 0, sales_price: 0, tax_amount: 0, addpoint: 0, foreign_currency_amount: 0 })
    return [0, 0, 0, 0, 0, 0]
  }
}

const allTotalPriceArr = computed(computePriceFn)

watch(
  () => imgFileList.value,
  async (val) => {
    await setFieldsValue({ imgs: val })
  }
)
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)
async function handleBatchEdit(val: number | undefined, type: string) {
  // const sales_goods = getFieldsValue().sales_goods
  const data = tableRef.value?.getTableData().fullData || []
  const rowKeys = tableRef.value?.getCheckboxRecords(true).map((item) => item.id) || []
  for (const item of data) {
    if (rowKeys.includes(item.id)) {
      item[type] = val
    }
    if (type == 'supplier_id') {
      item.tax_point = batchsupplierticketpoint.value
      item.add_point = batchsupplierticketaddpoint.value
      item.unit_price_tax = formatAmount(item.unit_price / (1 + (item.tax_point || 0) / 100), 4)
      item.cost_price = formatAmount(item.unit_price / (1 + (item.add_point || 0) / 100), 4)
    } else if (type == 'Material') {
      item.material = Material.value
    } else if (type == 'Code') {
      item.code = Code.value
    } else if (type == 'Brand') {
      item.brand = brand.value
    } else if (type == 'Invoicename') {
      item.invoice_name = invoicename.value
    }
  }

  if (type == 'supplier_id') {
    const show = ref(false)
    data.some((item) => {
      const supplier = supplierArray.value.find((val) => item.supplier_id === val.id)
      if (supplier && supplier.is_gbuilder === 1) {
        show.value = true
        return true // 一旦找到满足条件的供应商，就停止遍历
      }
      return false
    })
    await setFieldsValue({ is_gbuilder: show.value == true ? 1 : 0 })

    await updateSchema({
      field: 'gbuilder_percentage',
      ifShow: show.value,
      show: show.value,
      defaultValue: 5,
      required: true
    })
  }
  await setFieldsValue({ sales_goods: data })
}

const currentrow = ref<any>({})
function handleticket(row) {
  currentrow.value = row
}

//单独供应商设置Gbuilder
async function handlesupplier(val, shall) {
  console.log(shall)

  const fromvalue = await getFieldsValue()
  if (fromvalue.invoice_type) {
    switch (fromvalue.invoice_type) {
      case 1:
        if (shall.is_open_tax !== 1) {
          message.error('该供应商不支持当前发票类型,如若使用请及时维护供应商数据')
          currentrow.value.tax_point = null
          currentrow.value.supplier_id = null
          return
        }
        currentrow.value.tax_point = shall.tax_point
        currentrow.value.add_point = shall.add_point
        break
      case 2:
        if (shall.is_open_ticket !== 1) {
          message.error('该供应商不支持当前发票类型,如若使用请及时维护供应商数据')
          currentrow.value.tax_point = null
          currentrow.value.supplier_id = null
          return
        }
        currentrow.value.tax_point = shall.ticket_point
        currentrow.value.add_point = shall.add_point
        break
      case 3:
        currentrow.value.tax_point = 0
        currentrow.value.add_point = 0
        break
    }
  } else if (!fromvalue.invoice_type) {
    message.error('请先选择发票类型')
    currentrow.value.tax_point = null
    currentrow.value.supplier_id = null
    return
  }

  const data = tableRef.value?.getTableData().fullData || []

  if (!supplierArray.value.find((supplier: any) => supplier.id === shall.id)) {
    supplierArray.value.push(shall)
  }
  const show = ref(false)
  data.some((item) => {
    const supplier = supplierArray.value.find((val) => item.supplier_id === val.id)
    if (supplier && supplier.is_gbuilder === 1) {
      show.value = true
      return true // 一旦找到满足条件的供应商，就停止遍历
    }
    return false
  })
  data.forEach((item) => {
    item.unit_price_tax = formatAmount(item.unit_price / (1 + (item.tax_point || 0) / 100), 4)
    item.cost_price = formatAmount(item.unit_price / (1 + (item.add_point || 0) / 100), 4)
  })
  await setFieldsValue({ is_gbuilder: show.value == true ? 1 : 0, sales_goods: data })
  await updateSchema({
    field: 'gbuilder_percentage',
    ifShow: show.value,
    show: show.value,
    defaultValue: 5,
    required: true
  })

  // return currentrow.value
}

const batchsupplierticketpoint = ref({})
const batchsupplierticketaddpoint = ref({})
async function handlesupplierbatch(val, shall) {
  console.log(shall)

  if (!supplierArray.value.find((supplier: any) => supplier.id === shall.id)) {
    supplierArray.value.push(shall)
  }

  const fromvalue = await getFieldsValue()
  if (fromvalue.invoice_type) {
    switch (fromvalue.invoice_type) {
      case 1:
        if (shall.is_open_tax !== 1) {
          message.error('该供应商不支持当前发票类型,如若使用请及时维护供应商数据')
          supplierValue.value = undefined
          return
        }
        batchsupplierticketpoint.value = shall.tax_point
        batchsupplierticketaddpoint.value = shall.add_point
        break
      case 2:
        if (shall.is_open_ticket !== 1) {
          message.error('该供应商不支持当前发票类型,如若使用请及时维护供应商数据')
          supplierValue.value = undefined
          return
        }
        batchsupplierticketpoint.value = shall.ticket_point
        batchsupplierticketaddpoint.value = shall.add_point
        break
      case 3:
        batchsupplierticketpoint.value = 0
        batchsupplierticketaddpoint.value = 0
        break
    }
  } else if (!fromvalue.invoice_type) {
    message.error('请先选择发票类型')
    supplierValue.value = undefined
    return
  }
}

//handleHoverChange
//当前商品总价
const new_total_price = ref<any>(0)
//折扣,采购单价修改
const newtotal_price = ref<any>(0)
//更改显示
const visible = ref(false)
const visiblesupper = ref(false)
const visibleprocessor = ref(false)
//商品数组
const tabledata = ref<any>([])
//展示
const price_auth = ref(false)
//点击
function handleHoverChange(e, type) {
  if (!e) {
    tableRef.value?.clearSelected()
    visible.value = false
    visiblesupper.value = false
    visibleprocessor.value = false
    tabledata.value = []
    price_auth.value = false
    newtotal_price.value = 0
    visiblematerial.value = false
    visiblecode.value = false
    visiblebrand.value = false
    visibleinvoice.value = false
    Material.value = ''
    Code.value = ''
    brand.value = ''
    invoicename.value = ''
    return
  }
  const isAllowedOperation = ['edit', 'add'].includes(propsData.value.type)
  const selectedIds = tableRef.value?.getCheckboxRecords(true).map((item) => item.id) || []

  // 根据不同 type 执行对应逻辑
  if (type == 'total') {
    if (isAllowedOperation && ratechange.value) {
      if (selectedIds.length > 0) {
        const salesGoods = tableRef.value?.getTableData()?.fullData || []
        tabledata.value = salesGoods.filter((item) => selectedIds.includes(item.id)).map((item) => cloneDeep(item))
        new_total_price.value = tabledata.value.reduce((acc, item) => acc + item.unit_price * item.qty_purchased, 0)
        price_auth.value = false
        visible.value = true
      } else {
        message.warning('请选择要编辑的商品')
        visible.value = false
      }
    }
  } else if (['supplier', 'processor', 'Material', 'Code', 'Brand', 'Invoicename'].includes(type)) {
    if (isAllowedOperation) {
      if (selectedIds.length > 0) {
        switch (type) {
          case 'supplier':
            visiblesupper.value = true
            break
          case 'processor':
            visibleprocessor.value = true
            break
          case 'Material':
            visiblematerial.value = true
            break
          case 'Code':
            visiblecode.value = true
            break
          case 'Brand':
            visiblebrand.value = true
            break
          case 'Invoicename':
            visibleinvoice.value = true
            break
        }
      } else {
        message.warning('请选择要编辑的商品')
      }
    }
  } else {
    console.warn(`Unsupported type: ${type}`)
  }
}

async function handleprice(newprice) {
  try {
    const selectKeys = tableRef.value?.getCheckboxRecords(true).map((item) => item.id)
    const formData = tableRef.value?.getTableData()
    let sales_goods = formData?.fullData
    const oldTotal = tabledata.value.reduce((acc, item) => acc + item.unit_price * item.qty_purchased, 0)
    const ratio = newprice / oldTotal
    // if (newprice > new_total_sales.value) {
    //   throw new Error('新的成本总价不能大于销售总价')
    // }
    console.log('oldTotal', oldTotal)
    console.log('newprice', newprice)
    console.log('ratio', ratio)

    if (oldTotal == '0') {
      const price = ref(0)
      tabledata.value.forEach((item) => {
        price.value += Number(item.qty_purchased)
      })
      for (const item of sales_goods) {
        if (selectKeys.includes(item.id)) {
          const ratio = newprice / price.value
          // 只有当ratio大于item.unit_sales时才需要特殊处理，否则直接赋值
          item.unit_price = formatAmount(ratio, 4)
          item.unit_price_tax = formatAmount(ratio / (1 + (item.tax_point || 0) / 100), 4)
          item.cost_price = formatAmount(item.unit_price / (1 + (item.add_point || 0) / 100), 4)
        }
      }
    } else {
      for (const item of sales_goods) {
        if (selectKeys.includes(item.id)) {
          const calculatedPrice = mul(item.unit_price, ratio, 2)
          item.unit_price = formatAmount(calculatedPrice, 4)
          item.unit_price_tax = formatAmount(calculatedPrice / (1 + (item.tax_point || 0) / 100), 4)
          item.cost_price = formatAmount(item.unit_price / (1 + (item.add_point || 0) / 100), 4)
        }
      }
    }
    newtotal_price.value = 0
    await setFieldsValue(sales_goods)
    // clearSelectedRowKeys()
    // setSelectedRowKeys(selectKeys)
    visible.value = false
    tabledata.value = []
    price_auth.value = true
  } catch (e) {
    throw new Error(`${e}`)
  }
}

// 操作按钮（权限控制）
const createActions = (record): ActionItem[] => [
  {
    label: '删除',
    color: 'error',
    ifShow: propsData.value.type == 'add',
    onClick: () => {
      tableRef.value?.remove([record])
      setFieldsValue({ sales_goods: tableRef.value?.getTableData().fullData })
    }
  }
]
// 操作按钮（权限控制）
const deletesplit = ref(false)
const createsplitActions = (record): ActionItem[] => [
  {
    label: '删除',
    color: 'error',
    ifShow: propsData.value.type == 'add',
    disabled: deletesplit.value,
    onClick: () => {
      tablesplitRef.value?.remove([record])
      const data = tableRef.value?.getTableData().fullData
      data?.forEach((item) => {
        if (item.id == record.request_id) {
          item.items_sub = item.items_sub?.filter((item) => item.id !== record.id) ?? []
        }
      })
      setFieldsValue({ sales_goods: data })
    }
  }
]

function handleBatchDelete() {
  const selectedRow = tableRef.value?.getCheckboxRecords()
  if (selectedRow && selectedRow?.length === 0) return createMessage.error('请勾选对应商品')
  tableRef.value?.removeCheckboxRow()
  setFieldsValue({ sales_goods: tableRef.value?.getTableData().fullData })
}

// 预览
async function handlePreview(val: string, e?: Event) {
  e && e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

// async function handleDelFile(idx) {
//   filesList.value.splice(idx, 1)
//   await setFieldsValue({ files: filesList.value.map((item) => item.url) })
// }

// //主产品更改带动子产品数量
// async function handleChange(row) {
//   const quantitys = ref()
//   const quantityboolon = ref(false)
//   const Data = childrenItem.value.filter((item) => item.id == row.id)[0]

//   quantitys.value = row.items_sub.reduce((acc, cur) => {
//     return add(acc, mul(cur.quantity, cur.proportion, 2))
//   }, 0)

//   if (Data.items_sub.length == row.items_sub.length) {
//     let allMatch = row.items_sub.every((item) => {
//       return item.quantity == item.quantity_left
//     })
//     quantityboolon.value = allMatch
//   }
//   row.qty_purchased = quantityboolon.value == false ? quantitys.value : Data.qty_purchased
//   return row
// }

//展开更改
function handleExpandChange(row) {
  const quantitys = ref()
  const quantityboolon = ref(false)
  //获取商品原始数据
  const Data = childrenItem.value.filter((item) => item.id == row.id)[0]

  quantitys.value = row.items_sub.reduce((acc, cur) => {
    return add(acc, mul(cur.quantity, cur.proportion, 2))
  }, 0)
  if (quantitys.value < 0.01) {
    message.error('主产品数量不能小于0.01')
    return
  }

  // const Data = DataSource.value.filter((item: any) => item.id == row.id)[0]
  if (Data.items_sub.length == row.items_sub.length) {
    let allMatch = row.items_sub.every((item) => {
      return item.quantity == item.quantity_left
    })
    quantityboolon.value = allMatch
  }
  const price = row.items_sub.reduce((acc, cur) => {
    return add(acc, mul(cur.quantity, cur.unit_price, 2))
  }, 0)
  // qty_purchased_actual
  row.qty_purchased =
    quantityboolon.value == false ? quantitys.value : propsData.value.type == 'add' ? Data.total_quantity : Data.qty_purchased
  row.unit_price = formatAmount(Number(price) / Number(row.qty_purchased), 4)
  row.unit_price_tax = formatAmount(row.unit_price / (1 + (row.tax_point || 0) / 100), 4)
  row.cost_price = formatAmount(row.unit_price / (1 + (row.add_point || 0) / 100), 4)

  return row
}

const footerSpanMethod: VxeTablePropTypes.FooterSpanMethod = ({ $rowIndex, _columnIndex }) => {
  if ($rowIndex === 0) {
    if (_columnIndex === 1) {
      return {
        rowspan: 1,
        colspan: 30
      }
    }
  } else if ($rowIndex === 1) {
    if (_columnIndex === 1) {
      return {
        rowspan: 1,
        colspan: 30
      }
    }
  }
}
watchEffect(() => {
  footergettable.value = tableRef.value?.getTableData().fullData?.length
  footercheecktable.value = tableRef.value?.getCheckboxRecords(true).map((item) => item.id)?.length
  tableRef.value?.updateFooter()
})

const footerMethod: VxeTablePropTypes.FooterMethod = ({ columns }) => {
  const footerData = [
    columns.map((column, columnIndex) => {
      if (columnIndex === 1) {
        return `显示商品种类数量: ${footergettable.value}`
      }
      return null
    }),
    columns.map((column, columnIndex) => {
      if (columnIndex === 1) {
        return `勾选商品数量: ${footercheecktable.value}`
      }
      return null
    })
  ]
  return footerData
}

//增收税点计算更改
function handleeditaddpoint(value, row) {
  const data = tableRef.value?.getTableData().fullData || []
  for (const item of data) {
    if (item.supplier_id == row.supplier_id) {
      item.add_point = value
    }
  }
  row.unit_price = formatAmount(row.cost_price * (1 + (row.add_point || 0) / 100), 4)
  row.unit_price_tax = formatAmount((row.cost_price * (1 + (row.add_point || 0) / 100)) / (1 + (row.tax_point || 0) / 100), 4)
}
function handleUnitPrice(value, row, type) {
  console.log(value, row, type)
  const { rate } = getFieldsValue()
  if (type == 'unit_price') {
    row.unit_price_tax = formatAmount(row.unit_price / (1 + (row.tax_point || 0) / 100), 4)
    row.cost_price = formatAmount(row.unit_price / (1 + (row.add_point || 0) / 100), 4)
  } else if (type == 'unit_price_tax') {
    row.unit_price = formatAmount(row.unit_price_tax * (1 + (row.tax_point || 0) / 100), 4)
    row.cost_price = formatAmount((row.unit_price_tax / (1 + (row.add_point || 0) / 100)) * (1 + (row.tax_point || 0) / 100), 4)
  } else if (type == 'cost_price') {
    row.unit_price = formatAmount(row.cost_price * (1 + (row.add_point || 0) / 100), 4)
    row.unit_price_tax = formatAmount((row.cost_price * (1 + (row.add_point || 0) / 100)) / (1 + (row.tax_point || 0) / 100), 4)
  } else if (type == 'foreign_currency_unit_pirce') {
    row.unit_price_tax = new Decimal(row.foreign_currency_unit_pirce).mul(rate).toDecimalPlaces(4).toNumber()
    row.cost_price = new Decimal(row.foreign_currency_unit_pirce).mul(rate).toDecimalPlaces(4).toNumber()
    row.unit_price = new Decimal(row.foreign_currency_unit_pirce).mul(rate).toDecimalPlaces(4).toNumber()
  }

  return row
}
function uploadpreview(file) {
  handlePreview(file.url)
}

//亏本原因
function handleLossReasonChange(value, row) {
  if (value !== '备注其他原因') {
    row.loss_reason = ''
  } else {
    // 如果选择了“备注其他原因”，提示用户填写具体原因
    message.warning('请填写备注其他原因的具体内容')
  }
}

function handleFieldChange(key, value) {
  console.log('12')

  const { currency } = getFieldsValue()
  if (key == 'exchange_rate' || key == 'rate') {
    ratechange.value = ['人民币', 'CNY'].includes(currency) ? true : false
    const data = getFieldsValue().sales_goods
    if (!['人民币', 'CNY'].includes(currency)) {
      data.forEach((item) => {
        item.unit_price_tax = new Decimal(item.foreign_currency_unit_pirce).mul(value).toDecimalPlaces(4).toNumber()
        item.cost_price = new Decimal(item.foreign_currency_unit_pirce).mul(value).toDecimalPlaces(4).toNumber()
        item.unit_price = new Decimal(item.foreign_currency_unit_pirce).mul(value).toDecimalPlaces(4).toNumber()
      })
    }
    gridDataSource.value = cloneDeep(data)
    setFieldsValue({ sales_goods: data })
    gridOptions.columns = getDrawerTableColumns(propsData.value.type, currency).filter((item) => isUndefined(item.ifShow) || item.ifShow)
  }
}
</script>

<style lang="less" scoped>
.vxe-img {
  ::v-deep(.ant-image-img) {
    height: 60px;
    object-fit: contain;
  }
}
</style>
