<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleadd">新增</Button>
        <Button type="primary" @click="handlestatus">更改菜品状态</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editModal @register="registereditModal" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { mfgetMenuCost } from '/@/api/restaurantmanagement/sellingcost'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import editModal from './components/editModal.vue'
import { useModal } from '/@/components/Modal'
import { Button } from 'ant-design-vue'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()
const [registereditModal, { openModal, setModalProps }] = useModal()

const [registerTable, { reload, getSelectRows }] = useTable({
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  api: mfgetMenuCost,
  columns,
  rowKey: 'id',
  useSearchForm: true,
  formConfig: {
    schemas,
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['checkout_at', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  rowSelection: {
    // type: 'checkbox',
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '编辑',
      onClick: handledetail.bind(null, record)
    }
  ]

  return editButtonList
}

function handleadd(record: any) {
  // openDrawer(true, record)
  openModal(true, { record, type: 'add' })
  setModalProps({ title: '新增' })
}
function handledetail(record: any) {
  // openDrawer(true, record)
  openModal(true, { record, type: 'edit' })
  setModalProps({ title: '编辑' })
}
async function handlestatus() {
  const formdata = await getSelectRows()

  // 检查是否选中了数据
  if (!formdata || formdata.length === 0) {
    createMessage.warning('请选择要修改状态的菜品')
    return
  }

  // 检查所有选中行的status是否相同
  const firstStatus = formdata[0].status
  const allSameStatus = formdata.every((item) => item.status === firstStatus)

  if (!allSameStatus) {
    createMessage.warning('请选择状态相同的菜品进行批量修改')
    return
  }

  openModal(true, { formdata, type: 'status' })
  setModalProps({ title: '更改菜品状态' })
}
</script>
