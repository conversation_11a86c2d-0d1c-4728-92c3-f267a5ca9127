<template>
  <BasicModal @register="register" @ok="handleOk" width="600px" :min-height="400">
    <BasicForm @register="registerFrom">
      <template #names>
        <Tag v-for="item in boxnames" :key="item">{{ item }}</Tag>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { mfupdateMenuCost, mfsetStatusMenuCost } from '/@/api/restaurantmanagement/sellingcost'
import { addschemas, statusschemas } from '../datas/modal.data'
import { ref } from 'vue'
import { Tag } from 'ant-design-vue'
const emit = defineEmits(['success'])

const boxnames = ref()
const boxids = ref()
const types = ref()
const [register, { changeOkLoading, closeModal }] = useModalInner((data) => {
  resetFields()
  types.value = data.type
  if (data.type !== 'status') {
    resetSchema(addschemas)
    setFieldsValue(data.record)
  } else {
    resetSchema(statusschemas)
    boxnames.value = data.formdata.map((item) => item.name)
    boxids.value = data.formdata.map((item) => item.id)
  }
})

const [registerFrom, { setFieldsValue, validate, resetFields, resetSchema }] = useForm({
  showActionButtonGroup: false,
  labelCol: { style: 'width: 100px' }
  //   schemas
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    types.value !== 'status'
      ? await mfupdateMenuCost({ ...formdata })
      : await mfsetStatusMenuCost({ ids: boxids.value, status: formdata.status })
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    changeOkLoading(false)
  }
}
</script>
