import type { FormSchema } from '/@/components/Table'

export const mapType = {
  0: {
    label: '全部'
  },
  1: {
    label: '销售'
  },
  2: {
    label: '采购'
  },
  3: {
    label: '入库'
  },
  4: {
    label: '库存转换'
  },
  5: {
    label: '盘点'
  },
  6: {
    label: '出库'
  },
  7: {
    label: '退货'
  },
  8: {
    label: '其他收入单'
  },
  9: {
    label: '其他支出单'
  },
  10: {
    label: '收款单'
  },
  11: {
    label: '付款单'
  },
  12: {
    label: '流水单'
  },
  13: {
    label: '退款单'
  },
  14: {
    label: '冲销单'
  },
  15: {
    label: '收款单流水'
  },
  16: {
    label: '付款单流水'
  }
}
export const mapSrc = {
  1: {
    label: '已审核'
  },
  2: {
    label: '可备货(销售单使用)'
  },
  3: {
    label: '结算(销售采购)'
  },
  4: {
    label: '清点完成'
  }
}

export const schemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: () => {
      const options = Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
      return {
        options
      }
    }
  },
  {
    field: 'src',
    label: '来源状态',
    component: 'Select',
    componentProps: () => {
      const options = Object.keys(mapSrc).map((item) => ({ label: mapSrc[item].label, value: item }))
      return {
        options
      }
    }
  }
]
