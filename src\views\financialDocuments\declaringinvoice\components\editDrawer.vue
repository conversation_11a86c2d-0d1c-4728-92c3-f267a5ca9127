<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
  <fromodal @register="registerFormmodal" @addok="handleAddok" />
</template>
<script setup lang="ts">
import { columns } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import fromodal from './fromodal.vue'
import { useModal } from '/@/components/Modal'
import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'
import { Invoicecuscreate, Invoicecusupdat } from '/@/api/financialDocuments/declaringinvoice'

//保存点击,其他禁用
const currentEditKeyRef = ref('')

const [registerFormmodal, { openModal }] = useModal()
const emit = defineEmits(['success', 'register'])
const params_type = ref()

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  params_type.value = data.type
  if (data.type !== 'add') {
    setFieldsValue({ id: data.record.id, date: data.record.date })
    setTableData(data.record.itemscus)
  }
})

const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      field: 'id',
      component: 'Input',
      label: 'id',
      colProps: {
        span: 24
      },
      show: false
    },
    {
      field: 'date',
      component: 'DatePicker',
      label: '登记日期',
      colProps: {
        span: 24
      },
      required: true
    }
  ],
  showActionButtonGroup: false
})

const [registerTable, { setTableData, deleteTableDataRecord, updateTableDataRecord, getColumns, getDataSource }] = useTable({
  showIndexColumn: false,
  columns,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})

//action
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

function handleAdd() {
  openModal(true)
}

function handleAddok(data) {
  console.log(data)
  setTableData(data)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

// 存储编辑前的record
const beforeRecord = ref()

async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
async function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      const data = formatObject(record)
      function hasEmptyValue(obj: Record<string, any>): boolean {
        return Object.values(obj).some((value) => value === null || value === '' || value === undefined)
      }
      if (hasEmptyValue(data)) {
        message.error({ content: '请填写正确的数据', key: 'saving' })
        return
      }
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const fromdata = await validate()
    const data = formatSubmit()
    params_type.value == 'add' ? Invoicecuscreate({ doc: fromdata, items: data }) : Invoicecusupdat({ doc: fromdata, items: data })
    changeOkLoading(false)
    emit('success')
    closeDrawer()
  } catch (error) {
    message.error({ content: '请填写正确的数据', key: 'saving' })
    changeOkLoading(false)
  }
}
</script>
