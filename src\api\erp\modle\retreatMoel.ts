export interface IAddRetreat {
  doc: {
    applicant: string
    inCharge: number
    dept_id: number
    total_price: number
    remark: string
  }
  items: {
    name: string
    work_id: number
    request_id?: number
    purchase_id?: number
    warehouse_id?: number
    stocking_id?: number
    quantity: number
    remark: string
    desc: string
    unit: string
    unit_price: number
    imgs: string[]
  }[]
  work_id: number
}

export interface IGetRetreatList {
  dept_id: number
  supplier_id: number
  strid: string
  status: number
  type: number
}

export interface IRetreatUpdate {
  doc: {
    id: number
    applicant: number
    inCharge: number
    dept_id: number
    supplier_id?: number
    total_price: number
    type: number
    remark: string
  }
  items: {
    id: number
    name: string
    work_id: number
    request_id?: number
    purchase_id?: number
    warehouse_id?: number
    stocking_id?: number
    quantity: number
    remark: string
    desc: string
    unit: string
    unit_price: number
    imgs: string[]
    action: 1 | 2 | 3
  }[]
}
