import { defHttp } from '/@/utils/http/axios'

enum Api {
  clientgetList = '/erp/report/client/getList',
  clientgetPurList = '/erp/report/client/getSaleList'
}

//获取项目列表
export const clientgetList = (params: Recordable) => {
  return defHttp.get({
    url: Api.clientgetList,
    params
  })
}
export const clientgetPurList = (params: Recordable) => {
  return defHttp.get({
    url: Api.clientgetPurList,
    params
  })
}
