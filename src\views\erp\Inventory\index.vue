<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission(268)" type="primary" @click="handleAddQc" :loading="exporting">生成库存质检单</Button>
        <Button type="primary" @click="handleOpenModal" v-if="hasPermission([134])" :loading="exporting">成本增加</Button>
        <!-- <Button v-if="hasPermission(269)" type="primary" @click="handleOpenStockUpModal">设置备货</Button> -->
        <Button type="default" @click="() => clearSelectedRowKeys()" :loading="exporting">清空所有勾选</Button>
        <Button v-if="hasPermission(340)" type="primary" @click="handleExport" :loading="exporting"> 导出搜索结果 </Button>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'qty_residue'">
          <Tooltip>
            <template #title>已经填写出库单的商品数量（可转备货商品数量）</template>
            <span class="mr-1">{{ column.customTitle }}</span>
            <span><InfoCircleTwoTone /></span>
          </Tooltip>
        </template>
        <template v-else-if="column.dataIndex === 'qty_reserve'">
          <Tooltip>
            <template #title>已转成备货的商品数量</template>
            <span class="mr-1">{{ column.customTitle }}</span>
            <span><InfoCircleTwoTone /></span>
          </Tooltip>
        </template>
        <template v-else>{{ column.customTitle }}</template>
      </template>
      <template #bodyCell="{ column, record }">
        <!-- <template v-if="column.key === 'guige'">
          <Tag color="blue">
            {{ record.guige }}
          </Tag>
        </template> -->
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
        <template v-if="column.key === 'warehouse_id'">
          <div v-for="(item, index) in unref(statusdata).items" :key="index">
            <div v-if="item.id == record.warehouse_id">
              {{ item.name }}
            </div>
          </div>
        </template>
        <template v-if="column.key === 'src'"> {{ options[record.src].label }}</template>
        <template v-if="column.key === 'action'"> <TableAction :actions="createActions(record)" /> </template>
      </template>
    </BasicTable>
    <ModiyDrawer @register="registermodiy" />
    <CostSort @register="registerModal" @success="handleSuccess" />
    <EditDrawer @register="registerEditDrawer" @success="handleSuccess" />
    <!-- <StockUp @register="registerStockUpModal" @submit="handleConfirm" /> -->
    <QcDrawer @register="registerQcDrawer" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts" name="/erp/inventory">
import { ActionItem, BasicTable, TableAction, TableImg, useTable } from '/@/components/Table'
import { columns, formConfig, options } from './datas/data'
import { Button, Tooltip } from 'ant-design-vue'
import { InfoCircleTwoTone } from '@ant-design/icons-vue'
import { useDrawer } from '/@/components/Drawer'
import ModiyDrawer from './components/ModiyDrawer.vue'
import CostSort from './components/CostSort.vue'
import { getstockList, exportFile } from '/@/api/erp/inventory'
import { ref, unref } from 'vue'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { IRecord } from './datas/types'
import { useModal } from '/@/components/Modal'
import EditDrawer from '/@/views/erp/Inventory/components/EditDrawer.vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { useMessage } from '/@/hooks/web/useMessage'
// import StockUp from './components/StockUp.vue'
import { cloneDeep } from 'lodash-es'
import QcDrawer from '/@/views/erp/newqc/components/editDrawer.vue'
import { useRoute } from 'vue-router'
const [registerQcDrawer, { openDrawer: openQcDrawer, setDrawerProps: setQcDrawerProps }] = useDrawer()

const { path: routePath } = useRoute()
const exporting = ref<boolean>(false)
const { createMessage } = useMessage()
const { hasPermission } = usePermission()

const [registerTable, { getSelectRows, reload, clearSelectedRowKeys, getForm, setLoading }] = useTable({
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: 'work_id'
      }
    }
    // return sortInfo
  },
  title: '库存货物',
  showIndexColumn: false,
  useSearchForm: true,
  formConfig,
  columns,
  showTableSetting: true,
  api: getstockList,
  afterFetch: statusname,
  rowSelection: {
    fixed: 'left',
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record) => {
      if (record.status === 0) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  },
  rowKey: 'id',
  pagination: { pageSize: 10 },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleView.bind(null, record),
      ifShow: hasPermission([135])
    },
    {
      icon: 'ant-design:form-outlined',
      label: '编辑',
      disabled: record.qty_stocking === 0,
      onClick: handleEdit.bind(null, record),
      ifShow: hasPermission([136])
    }
  ]
}

// //盘点单
const [registerModal, { openModal }] = useModal()

// const [registerStockUpModal, { openModal: openStockUpModal, closeModal: closeStockUpModal, setModalProps }] = useModal()
// // 创建
function handleOpenModal() {
  const selected = cloneDeep(getSelectRows())
  if (selected.length === 0) return createMessage.error('请先勾选数据！')
  const isAllow = selected.every((item) => {
    return item.status !== 0
  })
  if (!isAllow) return createMessage.error('请不要选择未入库的商品！')
  openModal(true, { selectedRow: cloneDeep(getSelectRows()) })
}

//修改
const [registermodiy, { openDrawer: openDrawer2, setDrawerProps }] = useDrawer()

const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer()

function handleView(record: IRecord, e) {
  e.stopPropagation()
  setDrawerProps({ title: '库存详情' })
  openDrawer2(true, { record })
}

function handleEdit(record: IRecord) {
  openEditDrawer(true, { record })
}

//库存
const statusdata = ref()

async function statusname() {
  const a = await getWarehouse()
  statusdata.value = unref(a)
}

function handleSuccess() {
  reload()
  clearSelectedRowKeys()
}

// function handleOpenStockUpModal() {
//   // openStockUpModal(true, { data: cloneDeep(compPreparation.value) })
//   const selected = cloneDeep(getSelectRows())
//   if (selected.length === 0) return createMessage.error('请先勾选数据！')
//   const isAllow = selected.every((item) => {
//     return item.status !== 0 && +item.qty_stocking > 0
//   })
//   if (!isAllow) return createMessage.error('请选择库存数量大于0的商品进行操作！')
//   openStockUpModal(true, { data: cloneDeep(getSelectRows()) })
// }

// async function handleConfirm(data) {
//   try {
//     console.log(data)
//     const { msg } = await setReserve({ stockLists: data.map((item) => ({ id: item.id, qty_reserve: item.qty_reserve })) })
//     if (msg === 'success') {
//       createMessage.success('设置成功')
//       await reload()
//       clearSelectedRowKeys()
//       closeStockUpModal()
//     }
//   } catch (e) {
//     throw new Error(`${e}`)
//   } finally {
//     setModalProps({ confirmLoading: false })
//   }
// }

function handleAddQc() {
  const selected = cloneDeep(getSelectRows())
  if (selected.length === 0) return createMessage.error('请先勾选数据！')
  const sameOneSales = [...new Set(selected.map((item) => item.work_id))]

  if (sameOneSales.length > 1) return createMessage.error('请选择同一张销售订单的库存！')

  const isAllow = selected.every((item) => {
    return item.status !== 0 && item.qc_status !== 1
  })
  if (!isAllow) return createMessage.error('请不要选择未入库或已经质检的商品！')

  const ispurchase_id = selected.every((item) => {
    return item.purchase_id
  })

  if (ispurchase_id) return createMessage.error('库存没有可质检数量')
  const hasStocking = selected.every((item) => {
    return item.qr_num_left > 0
  })
  if (!hasStocking) return createMessage.error('请选择未质检数量大于0的商品')
  setQcDrawerProps({ title: '新增库存质检', showFooter: true })
  openQcDrawer(true, { type: 'add', qcType: 2, stockingRecord: selected })
}

async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await exportFile(params)
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `库存-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}
</script>
