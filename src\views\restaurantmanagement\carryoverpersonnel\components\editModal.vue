<template>
  <BasicModal @register="registerModal" title="资金资料绑定" width="500px" @ok="handleOk" :min-height="300">
    <BasicForm @register="registerFrom" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { mfsetCheckCashierCate } from '/@/api/restaurantmanagement/carryoverpersonnel'
const emit = defineEmits(['success'])

const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  resetFields()
  setFieldsValue(data)
})

const [registerFrom, { setFieldsValue, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  labelCol: { style: 'width: 100px' },
  schemas: [
    {
      field: 'number',
      label: 'number',
      component: 'Input',
      show: false
    },
    {
      field: 'category_id',
      label: '绑定科目',
      required: true,
      component: 'Select',
      componentProps: ({ formModel }) => {
        return {
          options: [
            {
              label: '营业费用-客户招待费',
              value: '营业费用-客户招待费'
            },
            {
              label: '实收资本',
              value: 150
            },
            {
              label: '应收账款',
              value: 211
            }
          ],
          onChange(_, shall) {
            formModel.category = shall.label
          }
        }
      },
      colProps: {
        span: 24
      }
    },
    {
      field: 'category',
      label: 'category',
      component: 'Input',
      show: false
    }
  ]
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    await mfsetCheckCashierCate(formdata)
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    changeOkLoading(false)
  }
}
</script>
