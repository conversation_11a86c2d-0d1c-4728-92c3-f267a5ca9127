//客户信息
import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '../model/baseModel'
import { BasicPageParams } from '/@/api/model/baseModel'
import { WarehouseParams } from './model/warehouseModel'
enum Api {
  GetWarehouse = '/erp/wm/get',
  UpdateWarehouse = '/erp/wm/update',
  CreateWarehouse = '/erp/wm/add',
  RemoveWarehouse = '/erp/wm/remove',
  GetWlist = '/erp/si/getWlist',
  GetSourceSelect = '/erp/si/getSourceSelect',
  UpdateWMI = '/erp/wmi/update',
  GetWMI = '/erp/wmi/getList',
  GetPackageList = '/erp/package/getList',
  wmiimpor = '/erp/wmi/import'
}

export const getWarehouse = (params?: BasicPageParams) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetWarehouse, params })

export const updateWarehouse = (params?: WarehouseParams) =>
  defHttp.post({ url: Api.UpdateWarehouse, params }, { successMessageMode: 'message' })

export const createWarehouse = (params?: WarehouseParams) =>
  defHttp.post({ url: Api.CreateWarehouse, params }, { successMessageMode: 'message' })

export const removeWarehouse = (params: { id: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.RemoveWarehouse, params }, { successMessageMode: 'message' })

export const getWlist = (params: { name?: string }) =>
  defHttp.get<BasicFetchResult<{ id: number; name: string }>>({ url: Api.GetWlist, params })

export const getSourceSelect = (params: {}) => defHttp.get({ url: Api.GetSourceSelect, params })

export const updateWMI = (params: Recordable) =>
  defHttp.post({ url: Api.UpdateWMI, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const getWMI = (params: { warehousemanage_id: number }) => defHttp.get({ url: Api.GetWMI, params })

export const getPackageList = (params: Recordable) => defHttp.post({ url: Api.GetPackageList, params })
export const wmiimpor = (params: Recordable) =>
  defHttp.post({ url: Api.wmiimpor, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
