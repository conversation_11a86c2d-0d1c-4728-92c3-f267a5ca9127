import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { message, Tag } from 'ant-design-vue'
import dayjs from 'dayjs'

const statusmap = {
  0: { text: '未支付', color: 'red' },
  1: { text: '已支付', color: 'green' },
  2: { text: '已撤销', color: 'gray' },
  3: { text: '支付中', color: '#ffc107' },
  4: { text: '退款中', color: 'purple' },
  5: { text: '支付失败', color: 'blue' },
  6: { text: '退款失败', color: 'red' }
}

export const columns: BasicColumn[] = [
  {
    title: '订单编号',
    dataIndex: 'order_number',
    width: 200,
    resizable: true
  },
  {
    title: '交易流水号',
    dataIndex: 'trade_no',
    width: 200,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: statusmap[text].color }, statusmap[text].text)
    }
  },
  {
    title: '支付类型',
    dataIndex: 'type',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? (text == 1 ? '进账' : '出账') : '-'
    }
  },
  {
    title: '支付金额(元)',
    dataIndex: 'pay_amount',
    width: 200
  },
  {
    title: '外币金额',
    dataIndex: 'foreign_amount',
    width: 200
  },
  {
    title: '关联支付流水标识',
    dataIndex: 'related_pay_no',
    width: 200
  },
  {
    title: '结账方式名称',
    dataIndex: 'pay_type_name',
    width: 200
  },
  {
    title: '支付时间',
    dataIndex: 'created_date',
    width: 200
  },
  {
    title: '最后修改时间',
    dataIndex: 'modify_date',
    width: 200
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'checkout_at',
    label: '结账时间',
    defaultValue: [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    component: 'SingleRangeDate',
    componentProps: ({ formModel }) => {
      return {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        },
        endPickerProps: {
          // 点击结束日期选择器时检查开始日期是否已选择
          onFocus: () => {
            // 如果开始日期为空
            if (!formModel.checkout_at || !formModel.checkout_at[0]) {
              // 提示用户需要先选择开始日期
              message.warning('请先选择开始日期')

              // 将焦点设置到开始日期选择器
              setTimeout(() => {
                const startDateInput = document.querySelector('input[placeholder="开始日期"]') as HTMLInputElement
                if (startDateInput) {
                  startDateInput.focus()
                }
              }, 100)

              return false
            }
            return true
          },
          disabledDate: (current: any) => {
            if (!current) return false

            // 结束日期不能超过今天
            const isAfterToday = current > dayjs().endOf('day')

            // 获取开始日期的值
            const startDate = formModel.checkout_at && formModel.checkout_at[0] ? dayjs(formModel.checkout_at[0]) : null

            // 结束日期不能早于开始日期
            const isBeforeStart = startDate && current < startDate

            return isAfterToday || isBeforeStart
          }
        },
        startPickerProps: {
          disabledDate: (current: any) => {
            if (!current) return false

            // 开始日期不能超过今天
            const isAfterToday = current > dayjs().endOf('day')

            return isAfterToday
          }
        }
      }
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'status',
    label: '订单状态',
    component: 'Select',
    componentProps: {
      options: Object.keys(statusmap).map((key) => {
        return {
          label: statusmap[key].text,
          value: key
        }
      })
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'type',
    label: '支付类型',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '进账',
          value: 1
        },
        {
          label: '出账',
          value: 2
        }
      ]
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'order_number',
    label: '订单编号',
    component: 'Input',
    colProps: {
      span: 6
    }
  }
]
