import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
const otherExpendList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      date: '@date',
      parent_id: '@guid',
      relate_sale: '@cname',
      'curreny|1': ['CNY', 'USD'],
      'cost|10-100000': 1000,
      title: '@ctitle',
      remark: '@cparagraph',
      client_id: '@guid',
      dept_id: '@guid',
      auditor: '@cname',
      inCharge: '@cname',
      order_no: '@guid',
      'client|1': ['档案1', '档案2', '档案3'],
      dept: '数据中心[EN:Date Center]',
      fund_return: '@guid',
      amount: '@natural(1, 1000)',
      salesorder_no: '@guid',
      amount_collected: '@natural(1, 1000)',
      'fund_id|1': ['0', '1', '2'],
      category: '@ctitle',
      revenue_details: [
        { no: 1, name: '@ctitle' },
        { no: 2, name: '@ctitle' },
        { no: 3, name: '@ctitle' }
      ]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/otherExpend',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(otherExpendList)
    }
  }
] as MockMethod[]
