// import { MockMethod } from 'vite-plugin-mock'
// import { resultError, resultPageSuccess, resultSuccess } from '../_util'

// const accountList = (() => {
//   const result: any[] = []
//   for (let index = 0; index < 20; index++) {
//     result.push({
//       id: `${index}`,
//       account: '@first',
//       email: '@email',
//       nickname: '@cname()',
//       role: '@first',
//       createTime: '@datetime',
//       remark: '@cword(10,20)',
//       'status|1': ['0', '1']
//     })
//   }
//   return result
// })()

// export default [
//   {
//     url: '/api/system/getAccountList',
//     timeout: 100,
//     method: 'get',
//     response: ({ query }) => {
//       const { page = 1, pageSize = 20 } = query
//       return resultPageSuccess(page, pageSize, accountList)
//     }
//   },
//   {
//     url: '/api/system/accountExist',
//     timeout: 500,
//     method: 'post',
//     response: ({ body }) => {
//       const { account } = body || {}
//       if (account && account.indexOf('admin') !== -1) {
//         return resultError('该字段不能包含admin')
//       } else {
//         return resultSuccess(`${account} can use`)
//       }
//     }
//   }
// ] as MockMethod[]
