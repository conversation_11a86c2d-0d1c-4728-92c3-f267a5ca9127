<template>
  <BasicModal @register="register" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import { projectbksetCheck } from '/@/api/erp/letterofguarantee'

const emit = defineEmits(['success', 'register'])
const type = ref()
const doc_id = ref()

const [register, { changeOkLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  resetFields()
  doc_id.value = data.record.id
  type.value = data.type
})

const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 120,
  showActionButtonGroup: false,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'is_pass',
      label: '是否通过',
      component: 'RadioButtonGroup',
      required: true,
      componentProps: {
        options: [
          { label: '通过', value: 1 },
          { label: '不通过', value: 2 }
        ]
      }
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      required: true,
      componentProps: { rows: 4 }
    }
  ]
})

async function handleSubmit() {
  try {
    const formData = await validate()
    changeOkLoading(true)
    // 根据type类型将is_pass替换为对应的字段名
    const submitData: any = {
      doc_id: doc_id.value,
      remark: formData.remark
    }

    // 根据审核类型设置对应的字段
    if (type.value === 'finance') {
      submitData.is_finance = formData.is_pass === 1 ? 2 : 1 // 1=通过转为2, 2=不通过转为1
    } else if (type.value === 'pm') {
      submitData.is_pm = formData.is_pass === 1 ? 2 : 1
    } else if (type.value === 'pm_charger') {
      submitData.is_pm_charger = formData.is_pass === 1 ? 2 : 1
    }

    console.log('提交数据:', submitData)

    // TODO: 调用对应的API接口提交审核结果
    await projectbksetCheck(submitData)
    changeOkLoading(false)
    closeModal()
    emit('success')
  } catch (error: any) {
    message.error('提交失败:', error)
  } finally {
    changeOkLoading(false)
  }
}
</script>
