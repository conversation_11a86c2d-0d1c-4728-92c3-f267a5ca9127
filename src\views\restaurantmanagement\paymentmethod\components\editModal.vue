<template>
  <BasicModal @register="registerModal" title="资金资料绑定" width="500px" @ok="handleOk" :min-height="300">
    <BasicForm @register="registerFrom" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { mfupdateCheckCap } from '/@/api/restaurantmanagement/paymentmethod'
const emit = defineEmits(['success'])

const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  resetFields()
  setFieldsValue(data)
})

const [registerFrom, { setFieldsValue, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  labelCol: { style: 'width: 100px' },
  schemas: [
    {
      field: 'payment_id',
      label: 'code',
      component: 'Input',
      show: false
    },
    {
      field: 'capital_id',
      label: '资金资料',
      required: true,
      component: 'ApiSelect',
      componentProps: {
        api: getFinancialInformation,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          placeholder: '请选择',
          fieldNames: { value: 'a_id', label: 'name' },
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },

      colProps: {
        span: 24
      }
    }
  ]
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    await mfupdateCheckCap(formdata)
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    changeOkLoading(false)
  }
}
</script>
