import { AccountItem } from './model/accountModel'
import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetAccountPageList = '/account/getPageList',
  GetAccountSelectList = '/account/getSelectList',
  isAccountExist = '/account/checkExist',
  CreateAccountItem = '/account/create',
  UpdateAccountItem = '/account/update',
  GetAccountItemDetail = '/account/detail',
  DeleteAccountItem = '/account/delete'
}

export const getAccountPageList = (params?: {} & BasicPageParams) =>
  defHttp.get<BasicFetchResult<AccountItem>>({ url: Api.GetAccountPageList, params })

export const getAccountSelectList = (params?: {} & BasicPageParams) => defHttp.get<AccountItem[]>({ url: Api.GetAccountSelectList, params })

export const isAccountExist = (username: string) =>
  defHttp.post({ url: Api.isAccountExist, params: { username } }, { errorMessageMode: 'none' })

export const createAccountItem = (params: AccountItem) =>
  defHttp.post({ url: Api.CreateAccountItem, params }, { successMessageMode: 'message' })

export const updateAccountItem = (params: AccountItem) =>
  defHttp.post({ url: Api.UpdateAccountItem, params }, { successMessageMode: 'message' })

export const setAccountItemStatus = (id: number, status: number) =>
  defHttp.post({ url: Api.UpdateAccountItem, params: { id, status } }, { successMessageMode: 'message' })

export const GetAccountDetail = (params: { id: number }) => defHttp.get<AccountItem>({ url: Api.GetAccountItemDetail, params })

export const deleteAccountItem = (params?: { id: number }) =>
  defHttp.get<{ id: number }>({ url: Api.DeleteAccountItem, params }, { successMessageMode: 'message' })
