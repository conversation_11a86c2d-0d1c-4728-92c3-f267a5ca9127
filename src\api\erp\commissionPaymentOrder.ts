import { defHttp } from '/@/utils/http/axios'
import type { BasicFetchResult } from '/@/api/model/baseModel'
import { commissionPaymentOrder } from './modle/types'

enum Api {
  GetcommissionPaymentOrder = '/erp/commissionPaymentOrderList'
}

export const getcommissionPaymentOrder = (params?: {}) =>
  defHttp.get<BasicFetchResult<commissionPaymentOrder>>({ url: Api.GetcommissionPaymentOrder, params })
