import { defHttp } from '/@/utils/http/axios'
import type { IGetInWarehouseDetail, IGetInWarehouseList, IGetInWarehouseListDoc, IGetInWarehouseListItems } from './modle/inWarehouseModel'
import { BasicPageParams } from '../model/baseModel'

enum Api {
  GetInWarehouseList = '/erp/stock/ew/getListByDate',
  GetInWarehouseDetail = '/erp/stock/ew/getItem',
  EditUpdateInWarehouse = '/erp/stock/ew/update',
  SetInWarehouseStatus = '/erp/stock/ew/setStatus',
  GetSaleNum = '/erp/stock/sin/getInNum',
  EditSaleNum = '/erp/stock/sin/update',
  GetReceivedNum = '/erp/stock/ew/getPkgReceived',
  EditReceivedNum = '/erp/stock/ew/setPkgReceived',
  ConfirmApprove = '/erp/stock/ew/confirmDoc',
  DelInWarehouse = '/erp/stock/ew/deleteDoc',
  SetPkgNum = '/erp/stock/ew/updatePkgNumber',
  GetRelatePurchaseList = '/tools/getPurchaseSelect',
  GetInWarehouseTableList = '/erp/stock/ew/getDoc',
  EditUpdateInWarehouseNew = '/erp/stock/ew/updateNew'
}

//更新/编辑入库单
export const editUpdateInWarehouse = (data: { doc: IGetInWarehouseListDoc; items: Partial<IGetInWarehouseListItems> }) =>
  defHttp.post<{ code: number; msg: string }>({ url: Api.EditUpdateInWarehouse, data })

export const editUpdateInWarehouseNew = (data) => defHttp.post({ url: Api.EditUpdateInWarehouseNew, data })

//获取入库单
export const getInWarehouseList = (params?: Partial<IGetInWarehouseList> & { created_at_start: string; created_at_end: string }) =>
  defHttp.get({ url: Api.GetInWarehouseList, params })

//获取入库单详情
export const getInWarehouseDetail = (params?: Partial<IGetInWarehouseDetail> & Partial<BasicPageParams>) =>
  defHttp.get({ url: Api.GetInWarehouseDetail, params })

export const setInWarehouseStatus = (data?: { doc_id: number; status: number; items: Array<{ id: number; status: number }> }) =>
  defHttp.post({ url: Api.SetInWarehouseStatus, data })

//获取销售总数量
export const getSaleNum = (params?: { work_id?: number }) => defHttp.get({ url: Api.GetSaleNum, params })

//编辑销售总数量
export const editSaleNum = (params?: { doc_id: number; work_id: number; quantity: number }) =>
  defHttp.get({ url: Api.EditSaleNum, params }, { successMessageMode: 'message' })

//获取已收到包裹数
export const getReceivedNum = (params?: { doc_id: number }) => defHttp.get({ url: Api.GetReceivedNum, params })

//编辑已收到包裹数
export const editReceivedNum = (params?: { doc_id: number; pkg_received: number }) =>
  defHttp.get({ url: Api.EditReceivedNum, params }, { successMessageMode: 'message' })

// 通过审核
export const confirmApprove = (doc_id: number) => defHttp.post({ url: Api.ConfirmApprove, data: { doc_id } })

// 删除
export const delInWarehouse = (id: number) => defHttp.get({ url: Api.DelInWarehouse, params: { id } })

// 更新pkg_num
export const setPkgNum = (id: number, pkg_num: number) => defHttp.post<{ msg: string }>({ url: Api.SetPkgNum, data: { id, pkg_num } })

//获取关联采购单下拉列表
export const getRelatePurchaseList = (params?: { work_id?: number; id?: number; is_wait?: number }) =>
  defHttp.get({ url: Api.GetRelatePurchaseList, params })

export const getInWarehouseTableList = (params: {}) => defHttp.get({ url: Api.GetInWarehouseTableList, params })
