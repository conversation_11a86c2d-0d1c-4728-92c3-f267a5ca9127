import { defHttp } from '/@/utils/http/axios'
import { CreateRefundParams, FundManageItems, FundManageListParams } from '/@/api/financialDocuments/modle/refund'
import { BasicFetchResult } from '/@/api/model/baseModel'

enum Api {
  GetFundManage = '/erp/refund/getList',
  SetFundStatus = '/erp/refund/setStatus',
  DelFund = '/erp/refund/delete',
  GetPayment = '/erp/finance/wrpc/getList',
  CreateRefund = '/erp/refund/create',
  RefundDetail = '/erp/refund/details',
  EditRefund = '/erp/refund/update'
}

export const getFundManage = (params?: FundManageListParams) =>
  defHttp.get<BasicFetchResult<FundManageItems>>({ url: Api.GetFundManage, params })

export const setFundStatus = (id: number) => defHttp.get({ url: Api.SetFundStatus, params: { id, status: 15 } })

export const delFund = (id: number) => defHttp.get({ url: Api.DelFund, params: { id } })

export const getPaymentList = (params) => defHttp.get({ url: Api.GetPayment, params })

export const createRefund = (data: CreateRefundParams) => defHttp.post<{ msg: string }>({ url: Api.CreateRefund, data })

export const getRefundDetail = (params: { id: Number }) => defHttp.get({ url: Api.RefundDetail, params })

export const editRefund = (data: CreateRefundParams & { id: number }) => defHttp.post({ url: Api.EditRefund, data })
