import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '一级名称',
    dataIndex: 'f_name',
    width: 100,
    resizable: true
  },
  {
    title: '二级编码',
    dataIndex: 's_name',
    width: 100,
    resizable: true
  },
  {
    title: '支付方式名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '支付方式编码',
    dataIndex: 'type_no',
    width: 100,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'green' : 'red' }, text == 1 ? '启用' : '禁用')
    }
  },
  {
    title: '来源',
    dataIndex: 'custom',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'green' : 'red' }, text == 1 ? '自定义' : '系统默认')
    }
  },
  {
    title: '资金资料名称',
    dataIndex: 'capital_name',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'f_name',
    label: '一级名称',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 's_name',
    label: '二级名称',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'name',
    label: '支付方式名称',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    colProps: {
      span: 8
    },
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '禁用',
          value: 0
        }
      ]
    }
  },
  {
    field: 'custom',
    label: '来源',
    component: 'Select',
    colProps: {
      span: 8
    },
    componentProps: {
      options: [
        {
          label: '自定义',
          value: 1
        },
        {
          label: '系统默认',
          value: 0
        }
      ]
    }
  }
]
