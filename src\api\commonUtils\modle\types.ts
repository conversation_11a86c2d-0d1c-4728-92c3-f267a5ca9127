export interface GetWorkListParams {
  basic_work_id?: number
  id?: number
  name?: string
  page?: number
  pageSize?: number
  parent_id?: number
  source_uniqid?: string
  status?: number[]
  strid?: string
  type?: number
  [property: string]: any
}

export interface WorkItem {
  auditor: number
  basic_work_id: number
  client_id: number
  client_name: string
  cost: string
  created_at: string
  creator: number
  currency: string
  dept_id: number
  est_finished_at: null | string
  exchange_rate: string
  id: number
  inCharge: number
  name: string
  paid: string
  paid_actual: string
  parent_id: number
  project_id: null | string
  project_number: null
  receivable: string
  received: string
  received_actual: number
  remark: string
  source: null | string
  source_uniqid: null | string
  start_at: null | string
  status: number
  strid: string
  submited_at: null | string
  total_price: string
  type: number
  updated_at: string
  [property: string]: any
}

export interface GetItemRequestParams {
  id?: number
  name?: string
  page?: number
  pageSize?: number
  work_id?: number
  [property: string]: any
}

export interface ItemRequestResponse {
  batch_code: number
  created_at: string
  desc: string
  id: number
  imgs: string
  name: string
  parent_uniqid: null
  puid: null
  qty_request: number
  qty_request_left: number
  remark: string
  total_amount: string
  type: number
  uniqid: string
  unit: string
  unit_price: string
  updated_at: string
  work_id: number
  [property: string]: any
}

export interface GetErpSupplier {
  id?: string
  name?: string
  page?: string
  pageSize?: number
  [property: string]: any
}

export interface ErpSupplierResponse {
  contact: string
  created_at: string
  creator: number | null
  id: number
  is_disabled: number
  name: string
  inCharge: number
  remark: string
  type: null
  updated_at: string
  [property: string]: any
}

export interface ItemStockingItem {
  created_at?: string
  desc?: string
  doc_id?: number
  doc_in_id?: number
  id?: number
  imgs?: string
  name?: string
  origin_stocking_id?: null
  pkg_num?: number
  pkg_received?: number
  puid?: string
  purchase_id?: number
  qty_defective?: number
  qty_received?: number
  qty_stocking?: number
  qty_total?: number
  remark?: string
  request_id?: number
  src: number
  status?: number
  unit?: string
  unit_price?: string
  updated_at?: string
  warehouse_id?: number
  work_id?: number
  [property: string]: any
}
