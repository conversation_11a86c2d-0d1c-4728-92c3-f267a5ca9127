import { defHttp } from '/@/utils/http/axios'
import type { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel'
import type {
  SalesTopListItem,
  SalesSubListItem,
  SalesListParams,
  SalesDetailParams,
  SalesRelateListParams,
  IEditSaleOrder
} from './modle/types'

enum Api {
  GetSalesOrder = '/erp/project/wk/getSalesOrderList',
  GetSalesOrderDetail = '/erp/project/wk/getSalesOrderDetail',
  GetSalesOrderListReq = '/erp/project/wk/getSalesOrderListReq',
  SetSalesOrderStatus = '/erp/project/wk/setSalesOrderStatus',
  GetSalesOrderChildren = '/erp/project/wk/getSalesOrderChild',
  //补货单
  GetaddSalesOrder = '/erp/project/wq/addReplenishOrder',
  //汇率
  GetRmbquot = '/tools/getRmbquot',
  //补货产品
  GetReplenish = '/erp/project/wq/getReplenish',
  //销售订单编辑
  EditSalesOrder = '/erp/project/wk/updateSalesOrder',
  //任务订单收付款结账审核
  VerifyOrder = '/erp/finance/ws/updateWorksAudit',
  SaleRetreat = '/erp/project/wk/saleRetreat',
  //结算审核
  updateWorksAudit = '/erp/finance/ws/updateWorksAudit',
  //流水调拨
  postfundAllot = '/erp/project/wk/fundAllot',
  //生成付款单驳回查询
  rejectList = '/erp/finance/rc/rejectList',
  //售后单
  addAfterSale = '/erp/project/wk/addAfterSale',
  // 需求产品列表
  GetRequestList = '/erp/project/wk/getRequestList',
  //财务特批
  FinanceApproved = '/erp/project/wk/setFinanceApproved',
  //设置反结算
  SetIsAudit = '/erp/project/wk/setIsAudit',
  //设置取消可备货
  setCencleReady = '/erp/project/wk/setCencleReady',
  // 设置是否完成拆单
  SetIsFinishSplit = '/erp/project/wk/setIsFinishSplit',
  // 收款结算导出
  ReceiptExport = '/erp/project/wk/receiptExport',
  //单独设置采购日期
  setPurchaseEstFinishAt = '/erp/project/wk/setPurchaseEstFinishAt',
  //附件上传
  updateFiles = '/erp/project/wk/updateFiles',
  //子产品上传
  importSub = '/erp/project/wk/importSub',
  //主产品导出
  exportitemRequest = '/erp/project/wk/exportItemRequest',
  // 销售单时效统计
  GetLimitationList = '/erp/project/wk/getOrderCountList',
  // 批量获取销售订单详情（产品和子产品）
  GetSalesOrderAndDetail = '/open/wkr/getSalesOrder',
  //设计师下单
  projectwksetDesignerAt = '/erp/project/wk/setDesignerAt',
  //导出商品pi单
  projectwqexportPi = '/erp/project/wq/exportPi',
  //费用订单更改设计师
  projectwksetDesigner = '/erp/project/wk/setDesigner',
  //收支明细
  projectwkgetBillDetails = '/erp/project/wk/getBillDetails',
  //装箱单附件上传
  projectpackinguploadFiles = '/erp/project/packing/uploadFiles',
  //箱单附件列表
  projectwkgetSaleFilesList = '/erp/project/wk/getSaleFilesList'
}

// 导出一个函数，用于获取销售订单列表
export const getSalesOrderList = (params?: BasicPageParams & SalesListParams) =>
  // 使用defHttp.get方法，传入url和params参数，返回BasicFetchResult<SalesTopListItem[]>类型的数据
  defHttp.get<BasicFetchResult<SalesTopListItem[]>>({ url: Api.GetSalesOrder, params })

export const getSalesOrderDetail = (params: SalesDetailParams) =>
  defHttp.get<BasicFetchResult<SalesSubListItem[]>>({ url: Api.GetSalesOrderDetail, params })

export const setSalesStatus = (params: { id: number; status: number; delivery_at?: string }) =>
  defHttp.post({ url: Api.SetSalesOrderStatus, params }, { successMessageMode: 'message' })

export const getSalesOrderListReq = (params: SalesRelateListParams) =>
  defHttp.get<BasicFetchResult<SalesSubListItem[]>>({ url: Api.GetSalesOrderListReq, params })

export const getSalesOrderChildren = (id: string | number, basic_work_id: string | number) =>
  defHttp.get<BasicFetchResult<SalesSubListItem[]>>({ url: Api.GetSalesOrderChildren, params: { id, basic_work_id } })

export const getaddSalesOrder = (params?: {}) => defHttp.post<BasicFetchResult<SalesSubListItem[]>>({ url: Api.GetaddSalesOrder, params })
export const getRmbquot = (params?: {}) =>
  defHttp.get({ url: Api.GetRmbquot, params }).then(({ items }) => {
    return items.reverse().map((item) => ({ ...item, name: `${item.name}-${item.fBuyPri}` }))
  })
export const getRmbquotcapit = (params?: {}) => defHttp.get({ url: Api.GetRmbquot, params })

export const getReplenish = (params?: {}) =>
  defHttp.get<BasicFetchResult<SalesSubListItem[]>>({ url: Api.GetReplenish, params }, { isTransformResponse: false })

export const editSalesOrder = (params: IEditSaleOrder) =>
  defHttp.post({ url: Api.EditSalesOrder, params }, { successMessageMode: 'message' })

export const verifyOrder = (params: any) => defHttp.post({ url: Api.VerifyOrder, params }, { successMessageMode: 'message' })
export const updateWorksAudit = (params: any) => defHttp.post({ url: Api.updateWorksAudit, params }, { successMessageMode: 'message' })

export const saleRetreat = (id: number) => defHttp.get({ url: Api.SaleRetreat, params: { id } })
export const postfundAllot = (params: any) =>
  defHttp.post({ url: Api.postfundAllot, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const rejectList = (params: any) => defHttp.post({ url: Api.rejectList, params })
export const addAfterSale = (params: any) => defHttp.post({ url: Api.addAfterSale, params }, { successMessageMode: 'message' })

export const getRequestList = (params) => defHttp.get({ url: Api.GetRequestList, params })

export const financeApproved = (params: { id: number; is_finance_approved: 0 | 1 }) =>
  defHttp.get({ url: Api.FinanceApproved, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

/**设置反结算
 * 只有待结算的才可以设置为0
 */
export const setIsAudit = (params: { id: number; is_audit: 0 }) =>
  defHttp.get({ url: Api.SetIsAudit, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//取消可备货
export const setCencleReady = (params: { id: number; status: 1 }) =>
  defHttp.get({ url: Api.setCencleReady, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const setIsFinishSplit = (params: { id: number; is_finish_split: 0 | 1 }) =>
  defHttp.get({
    url: Api.SetIsFinishSplit,
    params
  })

// 收款结算导出
export const receiptExport = (params) =>
  defHttp.get(
    {
      url: Api.ReceiptExport,
      params,
      responseType: 'blob'
    },
    { isTransformResponse: false }
  )

export const setPurchaseEstFinishAt = (params) =>
  defHttp.get({ url: Api.setPurchaseEstFinishAt, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const updateFiles = (params) =>
  defHttp.post({ url: Api.updateFiles, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const importSub = (params) =>
  defHttp.post({ url: Api.importSub, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const exportitemRequest = (params) =>
  defHttp.get({ url: Api.exportitemRequest, params, responseType: 'blob' }, { isTransformResponse: false })

export const getLimitationList = (params, isExcel = false) =>
  defHttp.get({ url: Api.GetLimitationList, params, responseType: !isExcel ? 'json' : 'blob' }, { isTransformResponse: !isExcel })
export const projectwqexportPi = (params) =>
  defHttp.get({ url: Api.projectwqexportPi, params, responseType: 'blob' }, { isTransformResponse: false })

export const getSalesOrderAndDetail = (params: { source_uniqid?: string; source_uniqids?: string; work_ids: number[] }) =>
  defHttp.get({ url: Api.GetSalesOrderAndDetail, params })
export const projectwksetDesignerAt = (params) =>
  defHttp.post({ url: Api.projectwksetDesignerAt, params }, { successMessageMode: 'message' })
export const projectwksetDesigner = (params) =>
  defHttp.post({ url: Api.projectwksetDesigner, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectwkgetBillDetails = (params) => defHttp.get({ url: Api.projectwkgetBillDetails, params })
export const projectpackinguploadFiles = (params) =>
  defHttp.post(
    {
      url: Api.projectpackinguploadFiles,
      params
    },
    {
      successMessageMode: 'message',
      errorMessageMode: 'message'
    }
  )
export const projectwkgetSaleFilesList = (params) =>
  defHttp.get({
    url: Api.projectwkgetSaleFilesList,
    params
  })
