import { DeptItem } from './model/deptModel'

import { defHttp } from '/@/utils/http/axios'
import { mapChildren } from '/@/utils/tree'

enum Api {
  GetDeptTree = '/department/getSelectTree',
  GetDeptSelectTree = '/department/getSelectTree',
  CreateDeptItem = '/department/create',
  UpdateDeptItem = '/department/update',
  GetDeptItemDetail = '/department/detail',
  DeleteDeptItem = '/department/delete'
}

function mapChildrenOdds(data, formatDisabled: boolean) {
  return {
    ...data,
    disabled: formatDisabled ? data.status === 0 : false
    // name: data.deptName
  }
}

export const getDeptTree = (params?: { status?: number; isHidden?: number; id?: number }) =>
  defHttp.get<DeptItem[]>({
    url: Api.GetDeptTree,
    params,
    transformResponse: (res) => {
      const data = JSON.parse(res)
      const newData = mapChildren(data.result, (item) => mapChildrenOdds(item, false))
      return { ...data, result: newData }
    }
  })

export const getDeptSelectTree = (params?: {}) =>
  defHttp.get<DeptItem[]>({
    url: Api.GetDeptSelectTree,
    params,
    transformResponse: (res) => {
      const data = JSON.parse(res)
      const newData = mapChildren(data.result, (item) => mapChildrenOdds(item, true))
      return { ...data, result: newData }
    }
  })

export const createDeptItem = (params?: DeptItem) =>
  defHttp.post<{ id: number }>({ url: Api.CreateDeptItem, params }, { successMessageMode: 'message' })

export const updateDeptItem = (params?: DeptItem) =>
  defHttp.post<{ id: number }>({ url: Api.UpdateDeptItem, params }, { successMessageMode: 'message' })

export const getDeptItemDetail = (params?: { id: number; type?: number }) => defHttp.get<DeptItem>({ url: Api.GetDeptItemDetail, params })

export const deleteDeptItem = (params?: { id: number }) =>
  defHttp.get<{ id: number }>({ url: Api.DeleteDeptItem, params }, { successMessageMode: 'message' })
