import { defHttp } from '/@/utils/http/axios'

enum Api {
  certpacreateOrUpdateDeptMap = '/erp/dm/createOrUpdate',
  certpagetDeptMapList = '/erp/dm/getList'
}

export const certpacreateOrUpdateDeptMap = (params?: {}) =>
  defHttp.post({ url: Api.certpacreateOrUpdateDeptMap, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const certpagetDeptMapList = (params?: {}) => defHttp.get({ url: Api.certpagetDeptMapList, params })
