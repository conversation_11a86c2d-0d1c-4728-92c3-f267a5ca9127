//部门科目余额
import { defHttp } from '/@/utils/http/axios'
import { SubjectParams } from '/@/api/baseData/model/subjectManage'
enum Api {
  GetLevel = '/erp/am/get',
  GetVoucherByName = '/erp/am/recordlist',
  UpdateStatus = '/erp/am/setStatus',
  GetSubjectList = '/erp/am/getList',
  UpdateSubject = '/erp/am/update'
}

export const getLevel = (params?: { noCache?: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetLevel, params })

export const getVoucherByName = (params?: { account_name?: string }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetVoucherByName, params })

export const getSubjectList = (params?: SubjectParams) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetSubjectList, params })

/**
 *
 * status 0是禁用,1是启用
 */
export const updateStatus = (params: { id: number; status: 0 | 1 }) =>
  defHttp.get({ url: Api.UpdateStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const updateSubject = (params) => defHttp.post({ url: Api.UpdateSubject, params })
