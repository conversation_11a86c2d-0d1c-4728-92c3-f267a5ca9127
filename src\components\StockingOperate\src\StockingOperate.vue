<template>
  <div>
    <!--    <slot name="operateBtn" />-->
    <BasicDrawer v-bind="$attrs" @register="registerModal" title="库存出库" :draggable="false" show-footer width="90%">
      <div style="width: 80%; margin: 0 auto; padding: 30px 0">
        <Steps :current="stepValue">
          <Step v-for="item in mapStep[props.type]" :key="item.value" :title="item.title" />
        </Steps>
      </div>
      <template v-if="stepValue === 0">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'imgs'">
              <TableImg
                :imgList="typeof record.imgs === 'string' ? [record.imgs] : record.imgs"
                :simpleShow="true"
                @click="(e: Event) => e.stopPropagation()"
              />
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="mapStep[props.type][stepValue]">
        <component
          ref="finallyComp"
          :is="mapStep[props.type][stepValue].component"
          :stocking-list="selectStockingList"
          v-bind="$attrs"
          @success="handleSuccess"
        />
      </template>
      <template #footer>
        <a-button v-if="stepValue !== 0" type="default" @click="handlePrev">上一步</a-button>
        <a-button v-if="mapStep[props.type].length - 1 !== stepValue" type="primary" @click="handleNext" :disabled="selectList.length === 0"
          >下一步</a-button
        >
        <a-button v-if="mapStep[props.type].length - 1 === stepValue" type="primary" @click="debounceHandleSubmit">完成</a-button>
      </template>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts" name="StockingOperate">
// import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Step, Steps, message } from 'ant-design-vue'
import { mapStep, columns, searchFormSchemas, getMapStoreList } from './datas/datas'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { nextTick, ref } from 'vue'
import { StockingItem } from '/@/components/StockingOperate/src/types'
import { DrawerInstance } from '/@/components/Drawer'
import { getItemStocking } from '/@/api/commonUtils'
import { debounce } from 'lodash-es'

const selectStockingList = ref<StockingItem[]>([])
const stepValue = ref<number>(0)
const finallyComp = ref<HTMLElement | null>(null)
const selectList = ref<number[] | string[]>([])

const props = withDefaults(
  defineProps<{
    type: 'addOutWarehouse' | 'addTransform'
  }>(),
  {
    type: 'addOutWarehouse'
  }
)

getMapStoreList()

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()

const [registerModal, { closeDrawer, changeOkLoading }] = useDrawerInner(async () => {
  selectList.value = []
  selectStockingList.value = []
  stepValue.value = 0
})

const [registerTable, { getSelectRows, clearSelectedRowKeys }] = useTable({
  api: getItemStocking,
  showIndexColumn: false,
  columns,
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    onChange: (selectedRowKeys) => {
      selectList.value = selectedRowKeys
    }
  },
  resizeHeightOffset: 80,
  // scroll: {
  //   y: 500
  // },
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchemas,
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  searchInfo: {
    is_residue: props.type === 'addTransform' ? 1 : null,
    is_reserve: props.type === 'addTransform' ? null : 0
  }
})

function handleNext() {
  console.log(props.type)
  // selectStockingList.value = getSelectRows() || []
  if (props.type === 'addTransform') {
    const ifshow: Array<number> = []
    getSelectRows().forEach((item: any) => {
      return ifshow.push(item.work_id)
    })
    if (new Set(ifshow).size == 1) {
      selectStockingList.value = getSelectRows() || []
      stepValue.value++
      console.log(selectStockingList.value)
    } else {
      message.error('只能选择同一销售订单的商品！')
    }
  } else {
    selectStockingList.value = getSelectRows() || []
    stepValue.value++
  }
}

function handlePrev() {
  stepValue.value--
  nextTick(() => clearSelectedRowKeys())
}
const debounceHandleSubmit = debounce(handleSubmit, 500)
function handleSubmit() {
  finallyComp.value?.submit({ changeOkLoading, closeDrawer })
}

function handleSuccess() {
  selectStockingList.value = []
  stepValue.value = 0
  selectList.value = []
  emits('success')
}
</script>

<style scoped lang="less"></style>
