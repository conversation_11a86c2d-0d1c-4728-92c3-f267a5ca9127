import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import {
  ErpSupplierResponse,
  GetErpSupplier,
  GetItemRequestParams,
  GetWorkListParams,
  ItemRequestResponse,
  ItemStockingItem,
  WorkItem
} from '/@/api/commonUtils/modle/types'
import { isArray } from 'lodash-es'

enum Api {
  GetWorkList = '/tools/getWork',
  GetItemRequest = '/tools/getItemRequest',
  GetErpSupplier = '/tools/getErpSupplier',
  // GetSupplierList = '/erp/sm/getList',
  GetDeptList = '/department/getSelectTree',
  GetAccountList = '/erp/ad/get',
  GetItemStocking = '/tools/getItemStocking',
  GetStoreList = '/erp/si/getWlist',
  GetClient = '/erp/si/getCustomer',
  GetUserFieldList = '/user/getFieldList',
  UpdateUserFieldList = '/user/updateField'
}

// 获取销售订单下拉列表
export const getWorkList = (params: GetWorkListParams = { pageSize: 99999 }) =>
  defHttp.get<BasicFetchResult<WorkItem>>({ url: Api.GetWorkList, params })

// 获取销售订单的商品
export const getItemRequest = (params: GetItemRequestParams = { pageSize: 99999 }) =>
  defHttp.get<BasicFetchResult<ItemRequestResponse>>({ url: Api.GetItemRequest, params })

// 获取供应商所有信息
export const getErpSupplier = (params: GetErpSupplier = { pageSize: 99999 }) =>
  defHttp.get<BasicFetchResult<ErpSupplierResponse>>({ url: Api.GetErpSupplier, params })

// 获取供应商下拉列表
// export const getSupplierList = () => defHttp.get<BasicFetchResult<{ id: number; name: string }>>({ url: Api.GetSupplierList })

// 一维部门列表（非树结构）
export const getDeptList = () =>
  defHttp.get<BasicFetchResult<{ id: number; name: string }>>({
    url: Api.GetDeptList,
    transformResponse: (data) => {
      const newData = JSON.parse(data)
      if (newData?.result && isArray(newData.result)) {
        return {
          ...newData,
          result: newData.result.map((item) => ({ ...item, disabled: item.status === 0 }))
        }
      }
      return newData
    }
  })

// 获取用户列表
export const getAccountList = (params) =>
  defHttp.get<BasicFetchResult<{ id: number; name: string }>>({ url: Api.GetAccountList, params: { pageSize: 999999, ...params } })

// 查询库存信息
export const getItemStocking = (
  params: { name?: string; id?: string; pageSize?: number; work_id?: number; status?: number; is_residue?: number; is_reserve?: number } = {
    pageSize: 99999
  }
) => defHttp.get<BasicFetchResult<ItemStockingItem>>({ url: Api.GetItemStocking, params })

// 获取仓库信息
export const getStoreList = () => defHttp.get<BasicFetchResult<{ id: number; name: string }>>({ url: Api.GetStoreList })

// 客户下拉列表
export const getClientList = (params) => defHttp.get<BasicFetchResult<{ id: number; name: string }>>({ url: Api.GetClient, params })

export const getFieldList = (type: string) => defHttp.get({ url: Api.GetUserFieldList, params: { type } })

export const updateUserFieldList = (data) => defHttp.post({ url: Api.UpdateUserFieldList, data })
