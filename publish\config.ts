/**
 * username: 请自行设置git的user.sftp属性进行修改提交的用户名
 * 设置：git config --global user.sftp "Your sftp username"
 * 清空：git config --global --unset user.sftp
 *
 * privateKey: 默认获取~/.ssh/id_rsa地址
 *  如果有其他路径，请自行修改git.user.sshpath
 *  设置：git config --global user.sshpath "Your sshpath"
 */

const Utils = require('./utils.ts')

const basePath = '/www/wwwroot/erp.gbuilderchina.com'

const baseConfig = {
  // host: '************',
  host: '**************',
  port: 22
  // username: 'sftpuser',
  // privateKey: Utils.getSSHPrivateKey()
}

module.exports = function (mode) {
  const shell = require('shelljs')
  const execSSHPath = shell.exec('git config --global user.sshpath')
  const SSHPath = execSSHPath.stdout.replace(/\n/, '')
  const privateKey = Utils.getSSHPrivateKey(SSHPath || '~/.ssh/id_rsa')
  const mapRemote = {
    tests: `${basePath}/tests`,
    car: `${basePath}/car`,
    prod: `${basePath}/s`,
    old: `${basePath}/old`,
    sp: `${basePath}/sp`,
    sptests: `${basePath}/sptests`,
    pytests: `${basePath}/py`
  }
  const mapBuild = {
    tests: 'pnpm run build:test',
    car: 'pnpm run build:car',
    prod: 'pnpm run build',
    old: 'pnpm run build:old',
    sp: 'pnpm run build:sp',
    sptests: 'pnpm run build:sptests',
    pytests: 'pnpm run build:pytests'
  }
  return {
    ...baseConfig,
    privateKey,
    remoteFile: mapRemote[mode],
    buildCommand: mapBuild[mode]
  }
}
