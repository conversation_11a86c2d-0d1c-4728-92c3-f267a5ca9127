<template>
  <BasicModal @register="registerModal" :title="t('routes.importExcelModal.modalTitle')" width="80%" @cancel="handleRedo" :footer="null">
    <Row :gutter="[16, 16]" justify="center">
      <Col :lg="18" :md="22">
        <Steps :current="currentStep">
          <Step :title="t('routes.importExcelModal.step1Title')" />
          <Step :title="t('routes.importExcelModal.step2Title')" />
          <Step :title="t('routes.importExcelModal.step3Title')" />
        </Steps>
      </Col>

      <template v-if="currentStep === 0">
        <Col :lg="12" :md="22">
          <Alert :message="t('routes.importExcelModal.alertMessage')" show-icon />
          <input ref="inputRef" type="file" v-show="false" accept=".xlsx, .xls" @change="handleFileInputChange" />
          <Button class="m-3" type="primary" @click="handleChooseFile"> {{ t('routes.importExcelModal.selectFileBtn') }}</Button>
        </Col>
      </template>
      <template v-if="currentStep === 1">
        <Col :lg="7" :md="12">
          <Space direction="vertical" size="small" class="action-wrapper">
            <Alert :message="t('routes.importExcelModal.alertMessage2')" show-icon />
            <span>{{ t('routes.importExcelModal.span1') }}</span>
            <Select class="w-full" v-model:value="sheetParams.sheetName" :options="sheetList" showSearch @change="handleParamChange" />
            <span>{{ t('routes.importExcelModal.span2') }}</span>
            <InputNumber v-model:value="sheetParams.headerRow" allowClear @change="handleParamChange" />
            <span
              >{{ t('routes.importExcelModal.span3') }}
              <span style="color: gray">{{ t('routes.importExcelModal.example') }} A2:ZZ100</span>
            </span>
            <div class="excel-range">
              <Input v-model:value="sheetParams.startCell" allowClear :bordered="false" @change="handleParamChange" />
              <span style="padding: 4px 0">:</span>
              <Input v-model:value="sheetParams.endCell" allowClear :bordered="false" @change="handleParamChange" />
            </div>
            <div stlye="display:inline-block;">
              <Button class="mr-5" @click="handleRedo"> {{ t('routes.importExcelModal.reUploadBtn') }} </Button>
              <!-- <Button type="primary"> 解析数据 </Button> -->
            </div>
          </Space>
          <!-- <BasicForm @register="registerForm">

          </BasicForm> -->
        </Col>
        <Col :lg="14" :md="20">
          <BasicTable
            v-if="!isNull(tableRef)"
            :title="tableRef.title"
            :titleHelpMessage="t('routes.importExcelModal.tableHelpMsg')"
            :columns="tableRef.columns"
            :dataSource="tableRef.dataSource"
            size="small"
            :showIndexColumn="false"
            :maxHeight="500"
            :pagination="{
              size: 'small',
              pageSize: 10,
              pageSizeOptions: ['10', '20', '100']
            }"
          >
            <template #toolbar>
              <Button class="mt-3" type="primary" @click="handleDataConfirm" :loading="isUploading">
                {{ t('routes.importExcelModal.uploadBtn') }}
              </Button>
            </template>
          </BasicTable>
        </Col>
      </template>
      <template v-if="currentStep === 2">
        <Col :lg="8" :md="22">
          <Result :status="status ? 'success' : 'error'" :title="status ? t('routes.importExcelModal.rusultTitle') : '导入失败'">
            <template #subTitle>
              <div class="w-[100%]" style="white-space: pre-line">
                {{ status ? t('routes.importExcelModal.resultSubTitle') : formattedErrorMessage || '数据导入过程中发生错误' }}
              </div>
            </template>
            <template #extra>
              <Button @click="handleStepPrev"> {{ t('routes.importExcelModal.continueBtn') }}</Button>
              <Button type="primary" @click="handleRedo" class="mt-4px"> {{ t('routes.importExcelModal.uploadAgainBtn') }} </Button>
            </template>
          </Result>
        </Col>
      </template>
    </Row>
  </BasicModal>
</template>
<script lang="ts" setup name="ImpExcelModal">
import { ref, unref, watch, computed } from 'vue'
import dayjs from 'dayjs'
import { Row, Col, Space, Steps, Button, Input, InputNumber, Select, Alert, Result, message } from 'ant-design-vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, BasicColumn } from '/@/components/Table'
import { useI18n } from '/@/hooks/web/useI18n'
import { useDebounceFn } from '@vueuse/core'
// import { isNull, isString } from 'lodash-es'

import { useExcelData } from '../../hook/useExcelData'
import { isNull, isString, isNumber, isEmpty } from '/@/utils/is'

const Step = Steps.Step

const { t } = useI18n()

const emit = defineEmits(['register', 'success', 'error', 'cancel'])

const inputRef = ref<HTMLInputElement | null>(null)
const isUploading = ref(false)
const cancelRef = ref<Boolean>(true)
const currentStep = ref(0)
const sheetList = ref<{ label: string; value: string }[]>([])

const sheetParams = ref<Recordable>({})

const tableRef = ref<
  Nullable<{
    title: string
    columns: any[]
    dataSource: any[]
  }>
>(null)

const props = defineProps({
  dataCallBackFn: {
    type: Function as PropType<(data: any[]) => Promise<string>>
  }
})

const { readData, getSheetNames, hasSheet, getSheet, getRowData, getSheetData, xlsxUtil } = useExcelData()
const [registerModal, { changeLoading }] = useModalInner(async (data) => {
  sheetParams.value = {
    ...data
  }
})

function handleStepNext() {
  currentStep.value++
}

function handleStepPrev() {
  currentStep.value--
}

//监听currentStep,如果currentStep为1就调用handleParamChange
watch(
  () => currentStep.value,
  (val) => {
    console.log(val)
    if (val === 1) {
      handleParamChange()
    }
  }
)

function handleRedo() {
  const inputRefDom = unref(inputRef)
  if (inputRefDom) inputRefDom.value = ''

  sheetList.value = []
  tableRef.value = null

  currentStep.value = 0
}
async function handleFileInputChange(e: Event) {
  const target = e && (e.target as HTMLInputElement)
  const files = target?.files
  const rawFile = files && files[0] // only setting files[0]
  sessionStorage.setItem('filename', files[0].name)
  target.value = ''
  if (!rawFile) return

  cancelRef.value = false

  await readData(rawFile)

  const sheetNames: string[] = getSheetNames()
  sheetNames.forEach((name) => {
    sheetList.value.push({
      label: name,
      value: name
    })
  })

  handleStepNext()
}

function handleFocusChange() {
  const timeId = setInterval(() => {
    if (cancelRef.value === true) {
      emit('cancel')
    }
    clearInterval(timeId)
    window.removeEventListener('focus', handleFocusChange)
  }, 1000)
}

function handleChooseFile() {
  const inputRefDom = unref(inputRef)
  if (inputRefDom) {
    cancelRef.value = true
    inputRefDom.click()
    window.addEventListener('focus', handleFocusChange)
  }
}

function isParamsReady(): boolean {
  const p = unref(sheetParams)
  if (!isNumber(p.headerRow) || isEmpty(p.sheetName) || !isString(p.startCell) || !isString(p.endCell)) {
    return false
  }

  //检查两个cell的格式
  const testReg = /^[A-Z]+[0-9]+$/
  if (!testReg.test(p.startCell) || !testReg.test(p.endCell)) {
    return false
  }

  return true
}

function cookSheetData() {
  const p = unref(sheetParams)

  if (!hasSheet(p.sheetName)) return
  const workSheet = getSheet(p.sheetName)

  const endColumn = p.endCell.replace(/[0-9]+$/, '')
  const startColumn = p.startCell.replace(/[0-9]+$/, '')
  const header = getRowData(workSheet, p.headerRow, endColumn, startColumn)
  const dataExcelRange = xlsxUtil.decode_range(p.startCell + ':' + p.endCell)
  let data = getSheetData(workSheet, {
    header,
    range: dataExcelRange
  })
  const columns: BasicColumn[] = []
  for (const title of header) {
    columns.push({ title, dataIndex: title })
  }
  data = data.map((obj) => {
    for (const key in obj) {
      if (obj[key] instanceof Date) {
        obj[key] = dayjs(obj[key]).add(8, 'hour').format('YYYY-MM-DD')
      }
    }
    return obj
  })
  tableRef.value = {
    title: p.sheetName,
    columns,
    dataSource: data
  }
}

const handleParamChange = useDebounceFn(() => {
  if (isParamsReady()) {
    cookSheetData()
  }
}, 300)

/**上传处理结果状态 */
const status = ref<boolean>(true)
const errorMessage = ref<string>('')

// 格式化错误消息，将分号替换为换行符
const formattedErrorMessage = computed(() => {
  return errorMessage.value.replace(/;/g, '\n')
})

function handleDataConfirm() {
  isUploading.value = true
  // emit('success', unref(tableRef)?.dataSource)
  const data = unref(tableRef)?.dataSource
  if (props.dataCallBackFn && data) {
    //直接在这里做拦截不行吗(等下面的方法有结果才next)
    props
      .dataCallBackFn(data)
      .then((res) => {
        console.log('数据处理和请求上传接口完成,进行下一步(成功之后的)', res)
        status.value = true
        errorMessage.value = ''
        message.success('导入成功')
        emit('success')
        isUploading.value = false
        handleStepNext()
      })
      .catch((err) => {
        console.log('失败', err)
        status.value = false
        // 提取错误信息并显示
        let errMsg = '导入失败'
        if (err && typeof err === 'object') {
          if (err.message) {
            errMsg = err.message
          } else if (err.response && err.response.data && err.response.data.message) {
            errMsg = err.response.data.message
          } else if (err.data && err.data.message) {
            errMsg = err.data.message
          } else if (typeof err === 'string') {
            errMsg = err
          }
        } else if (typeof err === 'string') {
          errMsg = err
        }
        errorMessage.value = errMsg
        // 显示错误提示
        message.error(errMsg)
        isUploading.value = false
        handleStepNext()
      })
  } else {
    isUploading.value = false
    handleStepNext()
  }
}

defineExpose({ changeLoading })
</script>
<style lang="less" scoped>
.action-wrapper {
  padding-right: 10px;
  border-right: 1px solid #d9d9d9;
  height: 600px;

  .excel-range {
    display: inline-flex;
    border: 1px solid #d9d9d9;

    &:hover {
      border-color: #2a7dc9;
      // box-shadow: 0 0 0 2px #0960bd33;
      outline: 0;
      border-radius: 2px;
    }
  }
}
</style>
