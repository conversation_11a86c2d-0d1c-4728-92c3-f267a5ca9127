import { Mock<PERSON>ethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

// const saleOrderList = (function () {
//   const result: any[] = []
//   for (let i = 0; i < 5; i++) {
//     result.push({
//       id: '@id',
//       parent_id: 0,
//       basic_work_id: 0,
//       project_id: '0',
//       strid: '@guid',
//       type: 1,
//       source: '运营中心',
//       source_uniqid: '01',
//       client_id: 1,
//       client_name: '张三',
//       status: 0,
//       dept_id: 22,
//       creator: 4,
//       auditor: 4,
//       inCharge: 4,
//       currency: 'CNY',
//       receivable: 0,
//       received: 0,
//       exchange_rate: 1,
//       received_actual: 0,
//       cost: 0,
//       paid: 0,
//       remark: '测试',
//       submited_at: '2023-09-12 17:12:55',
//       start_at: '2023-09-12 17:12:55',
//       est_finished_at: '2023-09-12 17:12:55',
//       created_at: '2023-09-12 09:12:55',
//       updated_at: '2023-09-12 09:12:55',
//       department: '数据中心[EN:Date Center]',
//       creator_name: '超级开发人员2',
//       auditor_name: '超级开发人员2',
//       inCharge_name: '超级开发人员2'
//     })
//   }
//   return { items: result, total: 50 }
// })()

// const salesOrderDetail = {
//   'noOutWarehouse|100-1000': 0,
//   'yesOutWarehouse|100-1000': 0,
//   'yesWorks|100-1000': 0,
//   'noReserveSum|100-1000': 0,
//   'produceNowSum|100-1000': 0,
//   'overdueSum|100-1000': 0,
//   'stockUpMon|100-1000': 0,
//   // 'produceNowSum|100-1000': 0,
//   'age|100-1000': 0,
//   'reserveTimeSum|100-1000': 0,
//   'nowWaitFollow|100-1000': 0,
//   'nowWaitGroup|100-1000': 0,
//   'monWaitFollow|100-1000': 0,
//   'monGroup|100-1000': 0,
//   'noReceipt|100-1000': 0,
//   'waitOtherDisburse|100-1000': 0,
//   'waitCheckFund|100-1000': 0,
//   'waitPayFund|100-1000': 0,
//   'nowNoFund|100-1000': 0,
//   'nowOutWarehouse|100-1000': 0,
//   'monOutWarehouse|100-1000': 0,
//   'monOrderOutWarehouse|100-1000': 0,
//   'monInWarehouse|100-1000': 0,
//   'monOutPiece|100-1000': 0,
//   'monInPiece|100-1000': 0,
//   'monReturn|100-1000': 0,
//   'waitQuality|100-1000': 0,
//   'finshNoSendQuality|100-1000': 0,
//   'qaReport|100-1000': 0,
//   'factoryQaReport|100-1000': 0,
//   'stockQaReport|100-1000': 0,
//   'onTimeRat|100-1000': 0,
//   'deliveryRat|100-1000': 0,
//   'topOnTimeRat|10': [{ name: '@cname', value: '@integer(1000, 9999)' }],
//   'topDeliveryRat|10': [{ name: '@cname', value: '@integer(1000, 9999)' }],
//   assess: {
//     oneStarSum: '@integer(10, 100)',
//     twoStarSum: '@integer(10, 100)',
//     threeStarSum: '@integer(10, 100)',
//     fourStarSum: '@integer(10, 100)',
//     fiveStarSum: '@integer(10, 100)',
//     ageStar: '@integer(10, 100)'
//   },
//   'tableAssess|10': [
//     {
//       name: '@cname',
//       oneStarSum: '@integer(0, 5)',
//       twoStarSum: '@integer(0, 5)',
//       threeStarSum: '@integer(0, 5)',
//       fourStarSum: '@integer(0, 5)',
//       fiveStarSum: '@integer(0, 5)'
//     }
//   ]
// }

const setOrderStatus = {
  code: 0,
  message: 'ok',
  type: 'success'
}

export default [
  // {
  //   url: '/api/erp/project/wk/getSalesOrderList',
  //   timeout: 1000,
  //   method: 'get',
  //   response: () => {
  //     return resultSuccess(saleOrderList)
  //   }
  // },
  // {
  //   url: '/api/account/getList',
  //   timeout: 1000,
  //   method: 'get',
  //   response: (req) => {
  //     return { ...resultSuccess({ item: { ...salesOrderDetail, roleId: req.query.roleId } }) }
  //   }
  // },
  {
    url: '/api/erp/setOrderStatus',
    timeout: 1000,
    method: 'post',
    response: () => {
      return resultSuccess(setOrderStatus)
    }
  }
] as MockMethod[]
