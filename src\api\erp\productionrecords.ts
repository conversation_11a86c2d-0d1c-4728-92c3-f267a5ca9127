import { defHttp } from '/@/utils/http/axios'

enum Api {
  productionrecordgetList = '/erp/production/record/getList',
  productionrecordupdate = '/erp/production/record/update',
  productionrecordsetIsCancel = '/erp/production/record/setIsCancel',
  productionrecordgetPurchaseItem = '/erp/production/record/getPurchaseItem',
  productionrecordgetItemList = '/erp/production/record/getItemList'
}

export const productionrecordgetList = (params?: {}) => defHttp.get({ url: Api.productionrecordgetList, params })
export const productionrecordupdate = (params?: {}) =>
  defHttp.post({ url: Api.productionrecordupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const productionrecordsetIsCancel = (params?: {}) =>
  defHttp.post({ url: Api.productionrecordsetIsCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const productionrecordgetPurchaseItem = (params?: {}) => defHttp.get({ url: Api.productionrecordgetPurchaseItem, params })
export const productionrecordgetItemList = (params?: {}) => defHttp.get({ url: Api.productionrecordgetItemList, params })
