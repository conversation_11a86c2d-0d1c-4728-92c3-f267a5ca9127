import { defHttp } from '/@/utils/http/axios'
// 发票登记
enum Api {
  getInvoiceList = '/taxbook/Invoice/getList',
  createInvoice = '/taxbook/Invoice/create',
  updateInvoice = '/taxbook/Invoice/update',
  detailsInvoice = '/taxbook/Invoice/details',
  setStatusInvoice = '/taxbook/Invoice/setStatus',
  deleteInvoice = '/taxbook/Invoice/delete',
  Invoiceexport = '/taxbook/Invoice/export'
}

// 获取
export const getInvoiceList = (params?: {}) => defHttp.get({ url: Api.getInvoiceList, params }, { errorMessageMode: 'message' })

// 创建
export const createInvoice = (params: Recordable) =>
  defHttp.post({ url: Api.createInvoice, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

// 更新
export const updateInvoice = (params: Recordable) =>
  defHttp.post({ url: Api.updateInvoice, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

// 获取详情
export const detailsInvoice = (params: { doc_id: number }) => defHttp.get({ url: Api.detailsInvoice, params }, { errorMessageMode: 'none' })

export const setStatusInvoice = (params: { doc_ids: string[] }) =>
  defHttp.get({ url: Api.setStatusInvoice, params }, { successMessageMode: 'none', errorMessageMode: 'none' })

// 删除
export const deleteInvoice = (params: { doc_ids: string[] }) =>
  defHttp.get({ url: Api.deleteInvoice, params }, { successMessageMode: 'none', errorMessageMode: 'none' })
//导出
export const Invoiceexport = (params: {}) =>
  defHttp.get(
    { url: Api.Invoiceexport, params, responseType: 'blob' },
    { isTransformResponse: false, errorMessageMode: 'message', successMessageMode: 'message' }
  )
