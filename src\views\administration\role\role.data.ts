import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Table'
import { h } from 'vue'
import { Switch } from 'ant-design-vue'
import { setRoleItemStatus, getAllRoleList, GetRoleDetail } from '../../../api/admin/role'
import { useMessage } from '/@/hooks/web/useMessage'
import { useI18n } from '/@/hooks/web/useI18n'

const { t } = useI18n()
const optionsdata = [
  { label: '开发人员', value: 'developer', key: '1' },
  { label: '超级管理员	', value: 'super_admin', key: '2' },
  { label: '无权限角色', value: 'no_perm', key: '3' },
  { label: '大客户部负责人', value: 'client_responsible', key: '4' },
  { label: '客服', value: 'customer_service', key: '5' },
  { label: '采购', value: 'procure', key: '6' },
  { label: '质检', value: 'inspection', key: '7' },
  { label: '仓库员工', value: 'warehouse', key: '8' },
  { label: '财务主管', value: 'treasurer', key: '9' },
  { label: '财务审单员', value: 'financia_review', key: '10' },
  { label: '出纳', value: 'cashier', key: '11' },
  { label: '部门主管', value: 'director', key: '12' },
  { label: '销售', value: 'sale', key: '13' },
  { label: '综合后勤', value: 'logistics', key: '14' },
  { label: '财务', value: 'finance', key: '15' },
  { label: '项目经理', value: 'project_manager', key: '16' },
  { label: '方案经理', value: 'program_manager', key: '7' },
  { label: '交付经理', value: 'delivery_manager', key: '18' },
  { label: '交付组长', value: 'delivery_team_leader', key: '19' },
  { label: 'GBuilder采购', value: 'gbuilder_procure', key: '20' },
  { label: '拆单员', value: 'splitter', key: '21' },
  { label: '物流经理', value: 'logistics', key: '22' },
  { label: '设计师', value: 'designer', key: '23' },
  { label: '产品部跟单', value: 'saleprocure', key: '24' },
  { label: '老板助理', value: 'assistant', key: '25' },
  { label: '实施工程师', value: 'implementation', key: '26' }
]
export const columns: BasicColumn[] = [
  {
    title: '角色id',
    dataIndex: 'id',
    width: 200
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    width: 200
  },
  {
    title: '角色值',
    dataIndex: 'roleValue',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ record }) => {
      if (!Reflect.has(record, 'pendingStatus')) {
        record.pendingStatus = false
      }
      return h(Switch, {
        checked: record.status === 1,
        checkedChildren: '已启用',
        unCheckedChildren: '已禁用',
        loading: record.pendingStatus,
        onChange(checked: boolean) {
          record.pendingStatus = true
          const newStatus = checked ? 1 : 0
          const { createMessage } = useMessage()
          setRoleItemStatus(record.id, newStatus)
            .then(() => {
              record.status = newStatus
              createMessage.success(`已成功修改角色状态`)
            })
            .catch(() => {
              createMessage.error('修改角色状态失败')
            })
            .finally(() => {
              record.pendingStatus = false
            })
        }
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '备注',
    dataIndex: 'remark'
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'roleName',
    label: '角色名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 }
      ]
    },
    colProps: { span: 8 }
  }
]

const roleTreeSelectSchema: FormSchema = {
  field: 'parentId',
  label: '上层角色id',
  component: 'ApiSelect',
  componentProps: ({ formModel }) => {
    return {
      api: getAllRoleList,
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        searchPlaceholder: 'Please select'
      },
      onChange: async (e: number) => {
        const roleInfo = await GetRoleDetail({ id: e })
        formModel.permissions = roleInfo.permissions
      }
    }
  },
  required: true,
  itemProps: {
    validateTrigger: 'blur'
  }
}

export const createFormSchema: FormSchema[] = [
  roleTreeSelectSchema,
  {
    field: 'roleName',
    label: '角色名称',
    required: true,
    component: 'Input'
  },
  {
    field: 'roleValue',
    label: '角色值',
    required: true,
    component: 'Select',
    componentProps: {
      options: optionsdata,
      onChange: (_, shall) => {
        console.log(shall)
      }
    }
  },
  {
    field: 'status',
    label: t('admin.base.status'),
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: t('admin.base.statusV.enable'), value: 1 },
        { label: t('admin.base.statusV.disable'), value: 0 }
      ]
    }
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea'
  },
  {
    label: ' ',
    field: 'permissions',
    slot: 'menu',
    component: 'Input',
    defaultValue: []
  }
]

export const updateFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: '角色id',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'parentId',
    label: '上层角色id',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'roleName',
    label: '角色名称',
    required: true,
    component: 'Input'
  },
  {
    field: 'roleValue',
    label: '角色值',
    required: true,
    component: 'Select',
    componentProps: {
      options: optionsdata
    }
  },
  {
    field: 'status',
    label: t('admin.base.status'),
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: t('admin.base.statusV.enable'), value: 1 },
        { label: t('admin.base.statusV.disable'), value: 0 }
      ]
    }
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea'
  },
  {
    label: ' ',
    field: 'permissions',
    slot: 'menu',
    component: 'Input',
    defaultValue: [],
    componentProps: {
      disabled: true
    }
  }
]
