<template>
  <div ref="cellWrap" class="cell-wrap" :style="{ width: `${wrapRect.width}px`, height: `${wrapRect.height}px`, border: '1px solid #ccc' }">
    <div>
      <div class="isNewStyle">
        <div class="isNewStyle_box">
          <div class="isNewStyle_box_ma">
            <div>
              <QrCode :value="computedQrCode" :width="195" :logo="ScanImg" class="qrcode" />
            </div>
          </div>
          <div class="isNewStyle_box_info">
            <div class="info_text">
              <div class="info_text_tit">Product Department:</div>
              <span>{{ row.dept_name }}</span>
            </div>
            <div class="info_text">
              <div class="info_text_tit">Order number:</div>
              <span>{{ row.source_uniqid }}</span>
            </div>
            <div class="info_text">
              <div class="info_text_tit">Packing Number:</div>
              <span>{{ row.strid }}</span>
            </div>
          </div>
          <div class="isNewStyle_box_no">
            <!-- <div class="no_tit">CTN.NO</div>
            <span class="no_content">{{ row.num }}</span> -->
          </div>
        </div>
        <div class="isNewStyle_bottom">
          <div class="bottom_box">
            <div class="bottom_box_div" v-for="index in 4" :key="index">
              <template v-if="row.items[index - 1]">
                <div class="info-item">
                  <div class="title">Product name:</div>
                  <div class="content">{{ row.items[index - 1].name }}</div>
                </div>
                <div class="info-item">
                  <div class="title">Item number:</div>
                  <div class="content">{{ row.items[index - 1].qty_purchased_actual }}</div>
                </div>
                <div class="info-item">
                  <div class="title">Product quantity:</div>
                  <div class="content">{{ row.items[index - 1].qty_purchased }}</div>
                </div>
                <div class="product-img">
                  <img :src="row.items[index - 1].imgs?.[0] || ''" alt="" class="qrcodeImg" v-if="row.items[index - 1]?.imgs?.length > 0" />
                </div>
              </template>
              <template v-else>
                <div class="info-item">
                  <div class="title">Product name:</div>
                  <div class="content">-</div>
                </div>
                <div class="info-item">
                  <div class="title">Item number:</div>
                  <div class="content">-</div>
                </div>
                <div class="info-item">
                  <div class="title">Product quantity:</div>
                  <div class="content">-</div>
                </div>
                <div class="product-img"></div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { QrCode } from '/@/components/Qrcode'

import ScanImg from '/@/assets/images/scan.png'
import { domToDataUrl } from 'modern-screenshot'

const props = defineProps({
  row: {
    type: Object,
    default: () => ({})
  },
  paper: {
    type: Object,
    default: () => ({
      width: 0,
      height: 0
    })
  },
  wrapRect: {
    type: Object,
    default: () => ({
      width: 0,
      height: 0
    })
  }
})

const computedQrCode = computed(() => {
  // const propDataValue = propData.value
  return `${props.row.strid}`
  // return `JC/123;JC/123;123`
})

const cellWrap = ref<HTMLElement | null>(null)

const purchaseSize = ref(18)
const uniqueItems = ref()
const purchaseWrap = ref<HTMLElement | null>(null)

onMounted(async () => {
  console.log(props.row)
  uniqueItems.value = props.row.items.reduce((acc, item) => {
    if (!acc.some((obj) => obj.purchase_strid === item.purchase_strid)) {
      acc.push(item)
    }
    return acc
  }, [])
  adjustTextToFitDiv(purchaseWrap.value)
})

/**
 * 用于判断div中是否一行显示文字完整；
 * 生成一个div，并设置初始样式，appendChild到页面，如果这个append元素的长度比当前页面的元素长，则不断缩小字体大小适应div
 * 直到appendChild元素长度少于当前页面的元素
 * @param container 页面上的元素
 * @param idx 渲染的页面元素
 */
const adjustTextToFitDiv = (container) => {
  if (container) {
    const div = container
    const text = props.row.purchaseId
    const tempElement = document.createElement('div')
    tempElement.style.position = 'absolute'
    // tempElement.style.left = '50%';
    // tempElement.style.top = '50%';
    tempElement.style.fontWeight = '900'
    tempElement.style.visibility = 'hidden'
    tempElement.style.whiteSpace = 'nowrap'
    // tempElement.style.display = 'inline';
    tempElement.textContent = text
    document.body.appendChild(tempElement)

    let fontSize = parseInt(window.getComputedStyle(div).fontSize, 10)
    tempElement.style.fontSize = fontSize + 'px'

    const step = 0.5

    while (tempElement.offsetWidth > props.wrapRect.width - 3 && fontSize > 0) {
      fontSize -= step
      tempElement.style.fontSize = fontSize + 'px'
    }

    purchaseSize.value = fontSize
    document.body.removeChild(tempElement)
  }
}

async function genderImg() {
  return new Promise(async (resolve) => {
    const imageData = await domToDataUrl(cellWrap.value, {
      width: props.wrapRect.width,
      height: props.wrapRect.height,
      quality: 0.1,
      scale: 0.8,
      backgroundColor: '#ffffff'
    })
    resolve(imageData)
  })
}

defineExpose({
  genderImg
})
</script>

<style scoped lang="less">
.box {
  //   font-size: 12px;
  width: 845px;
  height: 775px;
  box-sizing: border-box;
  background-color: white;
  padding: 0 0 27px;
  border: 1px solid black;
  .content {
    font-size: 30px;
    font-weight: 700;
  }
  //   background-image: url(../../../assets/images/print-bg-img.jpg);
  //   background-size: cover;
  .top_bar {
    display: flex;
    width: 100%;
    padding: 0px 20px;

    .left {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 200px;
      .qrcode_tit {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 22px;
        font-weight: bold;
        .tits {
          margin-top: -20px;
        }

        &.en {
          border: 1px solid black;
          border-radius: 5px;
          font-size: 28px;
          word-break: break-all; /* 允许在单词内换行 */
          white-space: pre-wrap; /* 保留空格和换行符 */
          &br {
            content: '\A'; /* 使用换行符 */
            white-space: pre;
          }
        }
      }
    }
    .right {
      box-sizing: border-box;
      position: relative;
      padding-top: 45px;
      padding-left: 10px;
      font-size: 24px;
      font-weight: 600;
      flex: 1;
      .desc {
        width: 100%;
        .descton {
          width: 100%;
          border: 1px solid black;
          border-radius: 5px;
          height: 100px;
          font-size: 32px;
          overflow: hidden;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
      .pkg {
        height: 120px;
        margin-top: 90px;
      }
      .type {
        width: 420px;
        margin-top: 5px;
        // width: 400px;
        .tit {
          font-size: 22px;
        }
        .content {
          font-size: 24px;
        }
      }
      .break {
        width: 400px;
        word-break: break-all;
        line-height: 30px;
      }
      .logo {
        width: 350px;
        position: absolute;
        right: 0px;
        top: 10px;
        // margin-top: -40px;
        // margin-left: 210px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .spec {
        display: flex;
        height: 30px;
        // margin-top: 5px;
        .tit {
          width: 180px;
        }
        .spec_str {
          height: 30px;
          font-weight: 700;
          font-size: 24px;
        }
      }
      .product_name {
        padding-top: 0;
        margin-top: 5px;
        width: 420px;
        height: 200px;
        .product_name_str {
          height: 205px;
          width: 420px;
          font-size: 29px;
          border-radius: 5px;
          border: 1px solid #000;
          overflow: hidden;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
      .prdouct_space {
        .prdouct_space_str {
          width: 100%;
          border: 1px solid #000;
          border-radius: 5px;
          height: 80px;
          overflow: hidden;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
      .pkg_num {
        position: absolute;
        right: 10px;
        top: 85px;
        .tit {
          font-size: 22px;
        }

        .num {
          margin-top: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 150px;
          height: 110px;
          box-sizing: border-box;
          border: 1px solid #000;
          border-radius: 5px;
          font-size: 50px;
        }
      }
    }
  }
  .middle_bar {
    display: flex;
    border: 5px solid #000;
    margin: 10px 30px 15px;
    height: 300px;
    box-sizing: border-box;

    .left {
      display: flex;
      flex-direction: column;
      border-right: 3px solid #000;
      height: 100%;
      width: 505px;
      box-sizing: border-box;

      .strid {
        padding: 5px;
        width: 100%;
        height: 310px;
        border-bottom: 3px solid #000;
        font-size: 25px;
        font-weight: bold;
        .content {
          height: 180px;
          font-size: 40px;
          overflow: hidden;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 3; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
      .sprite {
        display: flex;
        align-items: center;
        flex: 1;
        img {
          width: 100%;
          height: 80%;
        }
      }
    }
    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .qrcode_tit {
        width: 268px;
        height: 290px;
        position: relative; /* 使容器成为相对定位的上下文 */
        overflow: hidden; /* 隐藏超出部分 */
        .qrcodeImg {
          max-width: 100%; /* 图片的最大宽度为容器宽度 */
          max-height: 100%; /* 图片的最大高度为容器高度 */
          height: auto; /* 高度自动调整 */
          width: auto; /* 宽度自动调整 */
          object-fit: contain; /* 保持图片原始宽高比 */
        }
      }
    }
  }
  .bottom_bar {
    font-size: 31px;
    font-weight: bold;
    width: 100%;
    padding: 0 13px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
    word-break: break-all;
  }
}
.no_right {
  padding-top: 22px;
  background-color: red;
}
.lightfixture {
  width: 845px;
  height: 775px;
  box-sizing: border-box;
  background-color: white;
  padding: 0 0 27px;
  border: 3px solid #000;
  &_box {
    display: flex;
    justify-content: flex-start;
    height: 360px;
    &_ma {
      border: 3px solid #000;
      border-top: 0;
      border-left: 0;
      .ma_box {
        text-align: center;
        border-top: 2px solid #000;
        &_tit {
          font-weight: bold;
          font-size: 23px;
        }
        &_content {
          width: 240px;
          font-size: 26px;
          overflow: hidden;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
    }
    &_info {
      border: 3px solid #000;
      width: 400px;
      border-left: 0;
      border-top: 0;
      .info_text {
        border-bottom: 3px solid #000;
        height: 180px;
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &_tit {
          font-weight: bold;
        }
      }
    }
    &_no {
      border: 3px solid #000;
      width: 200px;
      border-left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border-right: 0;
      border-top: 0;
      .no_tit {
        font-weight: bold;
        font-size: 40px;
      }
      .no_content {
        font-size: 60px;
        font-weight: 800;
      }
    }
  }
  &_bottom {
    .bottom_box {
      height: 320px;
      border-bottom: 3px solid #000;
      display: flex;
      justify-content: flex-start;
      .box_left {
        width: 253px;
        :nth-child(3) {
          border-bottom: 0;
        }
      }
      .box_right {
        width: 300px;
        .tit_size {
          font-size: 24px;
        }
        :nth-child(3) {
          border-bottom: 0;
        }
      }
      .box_tit {
        height: 106px;
        border: 3px solid #000;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 28px;
        font-weight: bold;
        border-top: 0;
        border-left: 0;

        &.tit_size {
          font-size: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box !important;
          -webkit-box-orient: vertical !important;
          -webkit-line-clamp: 3;
          word-break: break-all;
          line-height: 32px;
          height: 106px;
          max-height: 106px;
          padding: 0 5px;
        }
      }
    }
  }
}

.isNewStyle {
  width: 845px;
  height: 775px;
  box-sizing: border-box;
  background-color: white;
  padding: 0 0 27px;
  border: 3px solid #000;
  &_box {
    display: flex;
    justify-content: flex-start;
    height: 300px;
    &_ma {
      border: 3px solid #000;
      width: 25%;
      border-top: 0;
      border-left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        .qrcode {
          padding: 0 !important;
        }
      }
    }
    &_info {
      border: 3px solid #000;
      width: 50%;
      border-left: 0;
      border-top: 0;
      .info_text {
        border-bottom: 3px solid #000;
        height: 100px;
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &_tit {
          font-weight: bold;
          display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-line-clamp: 2; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
          -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
          word-break: break-all;
        }
      }
    }
    &_no {
      border: 3px solid #000;
      width: 25%;
      border-left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border-right: 0;
      border-top: 0;
      .no_tit {
        font-weight: bold;
        font-size: 40px;
      }
      .no_content {
        font-size: 60px;
        font-weight: 800;
      }
    }
  }
  &_bottom {
    .bottom_box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      height: 475px;
      border-collapse: collapse;

      .bottom_box_div {
        border-right: 3px solid #000;
        display: flex;
        flex-direction: column;
        height: 99%;
        overflow: hidden;
        box-sizing: border-box;

        &:last-child {
          border-right: none;
        }

        .info-item {
          height: 102px;
          min-height: 102px;
          border-bottom: 3px solid #000;
          padding: 8px;
          display: flex;
          flex-direction: column;
          box-sizing: border-box;

          .title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            flex-shrink: 0;
          }

          .content {
            flex: 1;
            font-size: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box !important;
            -webkit-box-orient: vertical !important;
            -webkit-line-clamp: 2;
            word-break: break-all;
            text-align: center;
            line-height: 24px;
            max-height: 55px;
            padding: 2px 5px;
            box-sizing: border-box;
          }
        }

        .product-img {
          flex: 1;
          min-height: 0;
          height: 190px;
          max-height: 190px;
          padding: 5px;
          border-bottom: none;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          box-sizing: border-box;
          position: relative;

          img {
            position: absolute;
            width: auto;
            height: auto;
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            display: block;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
}

.bottom_box {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  height: 475px;
  border-collapse: collapse;

  .bottom_box_div {
    border-right: 3px solid #000;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;

    &:last-child {
      border-right: none;
    }

    .info-item {
      height: 95px;
      min-height: 95px;
      border-bottom: 3px solid #000;
      padding: 8px;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;

      .title {
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 5px;
        flex-shrink: 0;
      }

      .content {
        flex: 1;
        font-size: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box !important;
        -webkit-box-orient: vertical !important;
        -webkit-line-clamp: 2;
        word-break: break-all;
        text-align: center;
        line-height: 24px;
        max-height: 48px;
        padding: 2px 5px;
        box-sizing: border-box;
      }
    }

    .product-img {
      flex: 1;
      min-height: 0;
      height: 190px;
      max-height: 190px;
      padding: 5px;
      border-bottom: none;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      position: relative;

      img {
        position: absolute;
        width: auto;
        height: auto;
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        display: block;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.product-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;

  img {
    position: absolute;
    width: auto;
    height: auto;
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    display: block;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.qrcodeImg {
  width: auto;
  height: auto;
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}
</style>
