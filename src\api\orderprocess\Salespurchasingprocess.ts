import { defHttp } from '/@/utils/http/axios'

enum Api {
  productiongetList = '/erp/production/getList',
  productionsaleProcessupdate = '/erp/production/saleProcess/update',
  productionsaleProcessgetList = '/erp/production/saleProcess/getList',
  productionsaleProcesssetIsCancel = '/erp/production/saleProcess/setIsCancel',
  productionsaleProcesssetStatus = '/erp/production/saleProcess/setStatus',
  productionsaleProcessreturnStatus = '/erp/production/saleProcess/returnStatus',
  productionsaleProcesssetProcessIsFinish = '/erp/production/saleProcess/setProcessIsFinish',
  productionsaleProcesssetDelay = '/erp/production/saleProcess/setDelay'
}

export const productiongetList = (params: any) => {
  return defHttp.get({ url: Api.productiongetList, params })
}
export const productionsaleProcessupdate = (params: any) => {
  return defHttp.post({ url: Api.productionsaleProcessupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}
export const productionsaleProcessgetList = (params: any) => {
  return defHttp.post({ url: Api.productionsaleProcessgetList, params })
}
export const productionsaleProcesssetIsCancel = (params: any) => {
  return defHttp.post({ url: Api.productionsaleProcesssetIsCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}
export const productionsaleProcesssetStatus = (params: any) => {
  return defHttp.post({ url: Api.productionsaleProcesssetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}
export const productionsaleProcessreturnStatus = (params: any) => {
  return defHttp.post(
    { url: Api.productionsaleProcessreturnStatus, params },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
export const productionsaleProcesssetProcessIsFinish = (params: any) => {
  return defHttp.post(
    { url: Api.productionsaleProcesssetProcessIsFinish, params },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
export const productionsaleProcesssetDelay = (params: any) => {
  return defHttp.post({ url: Api.productionsaleProcesssetDelay, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}
