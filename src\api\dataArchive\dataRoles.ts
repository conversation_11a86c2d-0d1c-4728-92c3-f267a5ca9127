import { defHttp } from '/@/utils/http/axios'
import { DataRolesList } from '/@/api/dataArchive/model/types'
import { BasicFetchResult } from '/@/api/model/baseModel'

enum Api {
  GetDataRolesList = '/erp/rd/get',
  SetDataRolesStatus = '/erp/rd/setStatus',
  SetRelateRoles = '/erp/rd/relevancy'
}

export const getDataRolesList = () => defHttp.get<BasicFetchResult<DataRolesList>>({ url: Api.GetDataRolesList })

export const setDataRolesStatus = (params: { roles_id: number; is_forbid: number }) =>
  defHttp.get<{ news: string }>({ url: Api.SetDataRolesStatus, params })

export const setRelateRoles = (data: { roles_id: number; deptahs_id: number[]; is_forbid: number }) =>
  defHttp.post({ url: Api.SetRelateRoles, data })
