<template>
  <div>
    <FormItemRest>
      <Row :gutter="10">
        <Col :span="11">
          <DatePicker
            class="w-full"
            v-model:value="compStartDate"
            v-bind="startPickerProps"
            :disabled-date="startPickerProps?.disabledDate ? startPickerProps.disabledDate : (current) => disabledDate(current, 'start')"
            @change="(val) => handleDatePickerChange(val, 0)"
          />
        </Col>
        <Col :span="2">
          <swap-right-outlined class="w-full text-base" style="vertical-align: sub !important" />
        </Col>
        <Col :span="11">
          <DatePicker
            class="w-full"
            v-model:value="compEndDate"
            v-bind="endPickerProps"
            :disabled-date="endPickerProps?.disabledDate ? endPickerProps.disabledDate : (current) => disabledDate(current, 'end')"
            @change="(val) => handleDatePickerChange(val, 1)"
          />
        </Col>
      </Row>
    </FormItemRest>
  </div>
</template>

<script setup lang="ts" inheritAttrs="false">
import dayjs, { Dayjs } from 'dayjs'
import { ref, computed, unref } from 'vue'
import { DatePicker, Form, Row, Col, DatePickerProps } from 'ant-design-vue'
import { useRuleFormItem } from '/@/hooks/component/useFormItem'
import { SwapRightOutlined } from '@ant-design/icons-vue'
import { isArray } from 'lodash-es'
import { propTypes } from '/@/utils/propTypes'

const FormItemRest = Form.ItemRest
const emits = defineEmits(['change'])
const emitData = ref<any[]>([null, null])
const props = defineProps({
  value: {
    type: Array<Dayjs>
  },
  // 格式化
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss'
  },
  // 开始日期选择器的配置
  startPickerProps: {
    type: Object as PropType<DatePickerProps>
  },
  // 结束日期选择器的配置
  endPickerProps: {
    type: Object as PropType<DatePickerProps>
  },
  relateMode: propTypes.bool.def(false) // 时间限制模式：开始时间禁选结束时间之后，结束时间禁选开始时间之前
})

const compStartDate = computed({
  get() {
    return isArray(state.value) && state.value[0] ? dayjs(state.value ? unref(state)[0] : null) : null
  },
  set(value) {
    setState([value ? dayjs(value).format(props.valueFormat) : null, state.value ? state.value[1] : null])
  }
})

const compEndDate = computed({
  get() {
    return isArray(state.value) && state.value[1] ? dayjs(state.value ? unref(state)[1] : null) : null
  },
  set(value) {
    setState([state.value ? state.value[0] : null, value ? dayjs(value).format(props.valueFormat) : null])
  }
})

const [state, setState] = useRuleFormItem(props, 'value', 'change', emitData)

function handleDatePickerChange(val: Dayjs | string, arrPosition: number) {
  const [startDate, endDate] = state.value
  emits('change', [
    arrPosition === 0 ? val : startDate ? dayjs(startDate).format(props.valueFormat) : null,
    arrPosition === 1 ? val : endDate ? dayjs(endDate).format(props.valueFormat) : null
  ])
}

function disabledDate(current, type: 'start' | 'end') {
  if (!state.value || !props.relateMode) return false
  const [startDate, endDate] = state.value
  if (type === 'start') {
    if (!endDate) return false
    return dayjs(endDate) < dayjs(current).startOf('day')
  }

  if (type === 'end') {
    if (!startDate) return false
    return dayjs(startDate) > dayjs(current).endOf('day')
  }
  return false
}
</script>
