import { defHttp } from '/@/utils/http/axios'
import { BasicFormDataResult } from '/@/api/model/baseModel'
import { RcFile } from 'ant-design-vue/lib/vc-upload/interface'

enum Api {
  CommonImgUpload = '/oss/putImg',
  CommonFileUpload = '/oss/putFile'
}

export const commonImgUpload = (file: string | Blob | RcFile, path?: string, curFile?: any) =>
  defHttp.post<BasicFormDataResult>({
    url: Api.CommonImgUpload,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file, path },
    onUploadProgress: (progressEvent) => {
      if (curFile) {
        curFile.percent = Math.round(progressEvent.progress * 100)
      }
    }
  })

export const commonFileUpload = (file: string | Blob | RcFile, path?: string, curFile?: any, name?: string) =>
  defHttp.post<BasicFormDataResult>({
    url: Api.CommonFileUpload,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file, path, name },
    onUploadProgress: (progressEvent) => {
      if (curFile) {
        curFile.percent = Math.round(progressEvent.progress * 100)
      }
    }
  })
