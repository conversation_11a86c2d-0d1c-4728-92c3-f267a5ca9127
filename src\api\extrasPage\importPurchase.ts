import { defHttp } from '/@/utils/http/axios'

enum Api {
  BatchImpPurchaseCheck = '/AGR/importPurchaseCheck',
  //检查后提交到草稿
  SubmitToDraft = '/AGR/toDraft',
  GetDraftList = '/AGR/getDraft',
  //提交草稿生成订单采购单
  SubmitToPurchaseOrder = '/AGR/submitDraft'
}

export const batchImpPurchaseCheck = (params?: Recordable) =>
  defHttp.post({ url: Api.BatchImpPurchaseCheck, params }, { errorMessageMode: 'message' })

export const submitToDraft = (params?: Recordable) => defHttp.post({ url: Api.SubmitToDraft, params }, { errorMessageMode: 'message' })

export const getDraftList = (params?: Recordable) => defHttp.get({ url: Api.GetDraftList, params }, { errorMessageMode: 'message' })

export const submitToPurchaseOrder = (params?: Recordable) =>
  defHttp.get({ url: Api.SubmitToPurchaseOrder, params }, { errorMessageMode: 'message', successMessageMode: 'message' })
