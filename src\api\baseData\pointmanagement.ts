//资金档案
import { defHttp } from '/@/utils/http/axios'
enum Api {
  pointsupdate = '/erp/points/update',
  pointsgetList = '/erp/points/getList',
  pointssetStatus = '/erp/points/setStatus',
  pointssetIsDisabled = '/erp/points/setIsDisabled'
}

export const pointsupdate = (params?: {}) =>
  defHttp.post({ url: Api.pointsupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const pointsgetList = (params?: {}) => defHttp.post({ url: Api.pointsgetList, params })
export const pointssetStatus = (params?: {}) =>
  defHttp.post({ url: Api.pointssetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const pointssetIsDisabled = (params?: {}) =>
  defHttp.post({ url: Api.pointssetIsDisabled, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
