<template>
  <BasicModal
    @register="register"
    title="附件盖章"
    width="80%"
    @cancel="handleCancel"
    @ok="handleConfirmStamp"
    :ok-text="'确认盖章'"
    defaultFullscreen
    :ok-button-props="{
      disabled: !clickPosition,
      loading: stampLoading
    }"
  >
    <div class="stamp-container">
      <!-- 文件列表选择 -->
      <div class="file-list" v-if="fileList.length > 0">
        <h4>选择需要盖章的附件：</h4>
        <div class="file-items">
          <div
            v-for="(file, index) in fileList"
            :key="index"
            class="file-item"
            :class="{ active: selectedFileIndex === index }"
            @click="selectFile(index)"
          >
            <Icon icon="ant-design:file-outlined" />
            <span>{{ `附件${index + 1}` }}</span>
            <span class="file-type">.{{ getFileExtension(file) }}</span>
          </div>
        </div>
      </div>

      <!-- 坐标信息显示 -->
      <div class="stamp-info" v-if="selectedFile && (fileType === 'pdf' || isImageType)">
        <div class="coordinate-display">
          <p v-if="clickPosition">
            <Icon icon="ant-design:aim-outlined" />
            盖章位置: X: {{ clickPosition.x }}, Y: {{ clickPosition.y }}
            <span v-if="fileType === 'pdf' && clickPosition.page"> (第{{ clickPosition.page }}页)</span>
          </p>
          <p v-else class="tip">
            <Icon icon="ant-design:info-circle-outlined" />
            请在文件上点击选择盖章位置
          </p>
        </div>
      </div>

      <!-- 文件预览区域 -->
      <div class="preview-area" v-if="selectedFile">
        <!-- PDF预览 -->
        <div v-if="fileType === 'pdf'" class="pdf-container">
          <div class="pdf-header">
            <div class="pdf-title">
              <Icon icon="ant-design:file-pdf-outlined" />
              PDF文件预览
            </div>
            <Button :type="stampMode ? 'primary' : 'default'" @click="toggleStampMode" size="small">
              <Icon icon="ant-design:aim-outlined" />
              {{ stampMode ? '退出盖章模式' : '进入盖章模式' }}
            </Button>
          </div>

          <div class="pdf-viewer">
            <VuePDF ref="pdfViewerRef" :pdf="pdf" :page="currentPage" @loaded="handlePdfLoaded" @click="handlePdfClick" />

            <!-- PDF导航 -->
            <div v-if="pdfLoaded && totalPages > 1" class="pdf-navigation">
              <a-button :disabled="currentPage <= 1" @click="previousPage" size="small"> 上一页 </a-button>
              <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
              <a-button :disabled="currentPage >= totalPages" @click="nextPage" size="small"> 下一页 </a-button>
            </div>

            <!-- 盖章覆盖层 -->
            <div v-if="stampMode" class="pdf-overlay" @click="handlePdfOverlayClick">
              <div v-if="clickPosition" class="stamp-marker" :style="getMarkerStyle()">
                <Icon icon="ant-design:check-circle-filled" class="marker-icon" />
                <div class="marker-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="isImageType" class="image-container">
          <div class="image-wrapper">
            <img ref="imageRef" :src="selectedFile" @click="handleImageClick" @load="handleImageLoad" class="stamp-image" />
            <div v-if="clickPosition" class="stamp-marker" :style="getImageMarkerStyle()">
              <Icon icon="ant-design:check-circle-filled" class="marker-icon" />
              <div class="marker-pulse"></div>
            </div>
          </div>
        </div>

        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported-file">
          <Icon icon="ant-design:file-exclamation-outlined" size="48" />
          <p>该文件类型不支持在线盖章</p>
          <p>支持的格式：PDF、PNG、JPG、JPEG、GIF</p>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { Button, message } from 'ant-design-vue'
import { Icon } from '/@/components/Icon'
import { projectbkcreateSealFile } from '/@/api/erp/letterofguarantee'
import { VuePDF, usePDF } from '@tato30/vue-pdf'

const emit = defineEmits(['success', 'register'])

// 响应式数据
const fileList = ref<string[]>([])
const selectedFileIndex = ref<number>(-1)
const selectedFile = ref<string>('')
const fileType = ref<string>('')
const clickPosition = ref<{ x: number; y: number; page?: number } | null>(null)
const stampLoading = ref(false)
const recordData = ref<any>(null)

// PDF相关
const pdfViewerRef = ref()
const pdfLoaded = ref(false)
const stampMode = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const pdfUrl = ref('')
const { pdf, pages } = usePDF(pdfUrl)

// 监听PDF加载状态
watch(
  pdf,
  (newPdf) => {
    if (newPdf) {
      handlePdfLoaded()
    }
  },
  { immediate: true }
)

// 图片相关
const imageRef = ref<HTMLImageElement>()
const imageLoaded = ref(false)

// 计算属性
const isImageType = computed(() => {
  return ['png', 'jpg', 'jpeg', 'gif'].includes(fileType.value.toLowerCase())
})

// Modal注册
const [register, { closeModal }] = useModalInner(async (data) => {
  recordData.value = data.record

  const allFiles = []
  if (data.record.file && Array.isArray(data.record.file)) {
    allFiles.push(...data.record.file)
  }

  fileList.value = allFiles
  selectedFileIndex.value = -1
  selectedFile.value = ''
  clickPosition.value = null

  if (fileList.value.length === 1) {
    selectFile(0)
  }
})

// 文件选择
async function selectFile(index: number) {
  selectedFileIndex.value = index
  selectedFile.value = fileList.value[index]
  clickPosition.value = null
  stampMode.value = false
  currentPage.value = 1
  totalPages.value = 1

  const extension = getFileExtension(selectedFile.value)
  fileType.value = extension.toLowerCase()

  await nextTick()

  if (fileType.value === 'pdf') {
    pdfLoaded.value = false
    pdfUrl.value = selectedFile.value
  }
}

// 获取文件扩展名
function getFileExtension(url: string): string {
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const match = url.match(reg)
  return match ? match[1] : ''
}

// PDF处理函数
function handlePdfLoaded() {
  pdfLoaded.value = true
  totalPages.value = pages.value?.length || 1
}

function handlePdfClick(_: any) {
  if (!stampMode.value) {
    message.warning('请先点击"进入盖章模式"按钮')
    return
  }
}

function toggleStampMode() {
  stampMode.value = !stampMode.value
  if (!stampMode.value) {
    clickPosition.value = null
  }
}

function handlePdfOverlayClick(event: MouseEvent) {
  if (!pdfLoaded.value || !stampMode.value) {
    message.warning('请先进入盖章模式')
    return
  }

  const overlay = event.currentTarget as HTMLElement
  const rect = overlay.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const relativeX = Math.round((x / rect.width) * 100)
  const relativeY = Math.round((y / rect.height) * 100)

  clickPosition.value = {
    x: relativeX,
    y: relativeY,
    page: currentPage.value
  }

  message.success(`已选择盖章位置: (${relativeX}%, ${relativeY}%)`)
}

function previousPage() {
  if (currentPage.value > 1) {
    currentPage.value--
    clickPosition.value = null
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    clickPosition.value = null
  }
}

// 图片处理函数
function handleImageClick(event: MouseEvent) {
  const img = imageRef.value
  if (!img || !imageLoaded.value) return

  const rect = img.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 统一使用百分比坐标系统
  const relativeX = Math.round((x / rect.width) * 100)
  const relativeY = Math.round((y / rect.height) * 100)

  clickPosition.value = {
    x: relativeX,
    y: relativeY
  }

  message.success(`已选择盖章位置: (${relativeX}%, ${relativeY}%)`)
}

function handleImageLoad() {
  imageLoaded.value = true
}

// 样式计算
function getMarkerStyle() {
  if (!clickPosition.value) return {}
  return {
    left: clickPosition.value.x + '%',
    top: clickPosition.value.y + '%'
  }
}

function getImageMarkerStyle() {
  if (!clickPosition.value) return {}
  return {
    left: clickPosition.value.x + '%',
    top: clickPosition.value.y + '%'
  }
}

// 确认盖章
async function handleConfirmStamp() {
  if (!clickPosition.value) {
    message.warning('请先点击选择盖章位置')
    return
  }

  if (!selectedFile.value) {
    message.warning('请先选择要盖章的文件')
    return
  }

  stampLoading.value = true

  try {
    // 将百分比坐标转换为0-1之间的小数，这是常见的后端坐标格式
    const normalizedX = clickPosition.value.x
    const normalizedY = clickPosition.value.y

    const stampParams = {
      doc_id: recordData.value.id,
      x_step: normalizedX,
      y_step: normalizedY,
      seal_page: clickPosition.value.page || 1,
      contracting_party: recordData.value.contracting_party
    }

    console.log('发送给后端的盖章参数:', stampParams)

    await projectbkcreateSealFile(stampParams)
    message.success('盖章成功')
    closeModal()
    emit('success')
  } catch (error) {
    console.error('盖章失败详情:', error)
    message.error('盖章失败: ' + error)
  } finally {
    stampLoading.value = false
  }
}

function handleCancel() {
  closeModal()
}
</script>

<style lang="less" scoped>
.stamp-container {
  .file-list {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 12px;
      color: #333;
    }

    .file-items {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #1890ff;
          background-color: #f0f8ff;
        }

        &.active {
          border-color: #1890ff;
          background-color: #e6f7ff;
          color: #1890ff;
        }

        .anticon {
          margin-right: 6px;
        }

        .file-type {
          margin-left: 4px;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  .stamp-info {
    margin-bottom: 16px;

    .coordinate-display {
      padding: 12px 16px;
      background-color: #f5f5f5;
      border-radius: 6px;

      p {
        margin: 0;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
        }

        &.tip {
          color: #666;
        }
      }
    }
  }

  .preview-area {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    min-height: 400px;

    .pdf-container {
      .pdf-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #fafafa;

        .pdf-title {
          display: flex;
          align-items: center;
          font-weight: 500;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .pdf-viewer {
        position: relative;
        height: 800px;
        margin: 20px auto;
        max-width: 800px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding: 20px 0;

        .pdf-navigation {
          position: absolute;
          bottom: 10px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          align-items: center;
          gap: 10px;
          background: rgba(0, 0, 0, 0.7);
          padding: 8px 16px;
          border-radius: 20px;
          z-index: 10;

          .page-info {
            color: white;
            font-size: 14px;
            min-width: 60px;
            text-align: center;
          }
        }

        .pdf-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          cursor: crosshair;
          background: rgba(0, 123, 255, 0.05);
          z-index: 10;
          border: 2px dashed #1890ff;

          &::after {
            content: '请在PDF文档区域点击选择盖章位置';
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(24, 144, 255, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            pointer-events: none;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .image-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;

      .image-wrapper {
        position: relative;
        display: inline-block;

        .stamp-image {
          cursor: crosshair;
          max-width: 100%;
          max-height: 500px;
          border-radius: 4px;
          display: block;
        }
      }
    }

    .unsupported-file {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 300px;
      color: #999;

      .anticon {
        margin-bottom: 16px;
        color: #d9d9d9;
      }

      p {
        margin: 4px 0;
      }
    }
  }

  .stamp-marker {
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 20;

    .marker-icon {
      font-size: 24px;
      color: #52c41a;
      background: white;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      animation: bounce 0.6s ease-out;
    }

    .marker-pulse {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      border: 2px solid #52c41a;
      border-radius: 50%;
      animation: pulse-ring 2s infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}
</style>
