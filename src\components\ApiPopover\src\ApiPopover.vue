<template>
  <Popover v-if="!props.disabled" v-bind="$attrs" @visible-change="handleVisibleChange">
    <template #content>
      <div ref="wrapEl"></div>
      <slot name="popoverContent" :data="fetchData"></slot>
    </template>
    <template #title>
      <slot name="popoverTitle"></slot>
    </template>
    <slot name="popoverDefault"></slot>
  </Popover>
  <slot v-else name="popoverDefault"></slot>
</template>

<script setup lang="ts">
import { Popover } from 'ant-design-vue'
import { propTypes } from '/@/utils/propTypes'
import { ref, h, nextTick } from 'vue'
import { isFunction } from '/@/utils/is'
import { useLoading } from '/@/components/Loading'
import { LoadingOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  // 是否显示popover
  disabled: propTypes.bool.def(false),
  // 请求的api，必填
  api: {
    type: Function as PropType<(arg?: Recordable) => Promise<Recordable[]>>,
    required: true
  },
  // api传递的固定参数，动态参数需要api.bind(null, xxx)进行传递
  params: {
    type: Object as PropType<Recordable>,
    default: () => ({})
  }
})

const indicator = h(LoadingOutlined, {})
const wrapEl = ref(null)
const [openWrapLoading, closeWrapLoading] = useLoading({
  target: wrapEl,
  props: {
    absolute: true,
    size: 'small',
    indicator
  }
})

const fetchData = ref(null)

async function handleFetch() {
  const api = props.api
  if (!api || !isFunction(api)) return

  try {
    openWrapLoading()
    fetchData.value = null

    fetchData.value = await api(props.params)
  } catch (e) {
    throw new Error(e)
  } finally {
    closeWrapLoading()
  }
}

function handleVisibleChange(visible) {
  if (visible) {
    nextTick(() => {
      handleFetch()
    })
  }
}
</script>
