import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'

export type AccountParams = BasicPageParams & {
  account?: string
  nickname?: string
}

export interface AccountListItem {
  id: string
  account: string
  email: string
  nickname: string
  role: number
  createTime: string
  remark: string
  status: number
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem>
