import { defineStore } from 'pinia'
import { ALLSTATUS } from '/@/const/status'

interface IOptions {
  label: string
  value: number
}

export const useSaleOrderStore = defineStore('saleOrder', {
  state: () => ({
    saleType: {
      1: '正常订单',
      2: '备货单',
      3: '销售订单',
      4: '采购订单',
      5: '入库订单 ',
      6: '质检',
      7: '其它收入单',
      8: '其它支出单',
      9: '退货单',
      // 10: '备货单',
      11: '退款单',
      12: '收款单',
      13: '库存转换单',
      14: '盘点单',
      15: '被库存转换单',
      16: '出库单',
      20: '佣金',
      21: '共摊',
      22: '手续费 ',
      23: '个人报销 ',
      24: '款项支出 ',
      25: '预约出库单',
      26: '预约质检单',
      27: '消费单',
      28: '付款单',
      29: '流水',
      30: '其他'
    },
    saleStatus: {
      0: '未执行',
      1: '执行中',
      2: '可备货',
      3: '备货中',
      4: '已入库',
      5: '出库中',
      15: '已结束',
      16: '取消'
    },
    statusColor: {
      0: '',
      1: 'green',
      2: 'green',
      3: 'green',
      4: 'green',
      15: 'red',
      16: ''
    },
    orderType: {
      1: '销售',
      2: '采购',
      3: '其他收入',
      4: '其他支出',
      5: '销售退款',
      6: '采购退款'
    },
    retreatSaleStatus: [1, 2, 3, 4, 5],
    // 仓库员工和质检的角色值
    warehouseQualityStaffRoleValues: ['inspection', 'warehouse']
  }),
  getters: {
    mapOrderTypeOptions() {
      const options: IOptions[] = []
      for (const key in this.saleType) {
        options.push({ label: this.saleType[key], value: Number(key) })
      }
      return options
    },
    mapOrderStatusOptions() {
      const options: IOptions[] = []
      for (const key in this.saleStatus) {
        options.push({ label: this.saleStatus[key], value: Number(key) })
      }
      return options
    },
    mapOrderStatusConcatAll() {
      return [ALLSTATUS].concat(this.mapOrderStatusOptions)
    },
    mapOrderStatusTabsSchemas() {
      const tabsSchemas: any[] = [
        {
          key: undefined,
          tab: '全部'
        }
      ]

      for (const key in this.saleStatus) {
        tabsSchemas.push({ tab: this.saleStatus[key], key: Number(key) })
      }
      return tabsSchemas
    }
  }
})
