import { defHttp } from '/@/utils/http/axios'

enum Api {
  mfgetCheckCashier = '/meituan/mf/getCheckCashier',
  mfsetCheckCashierCate = '/meituan/mf/setCheckCashierCate'
}

export const mfgetCheckCashier = (params?: {}) => {
  return defHttp.get({
    url: Api.mfgetCheckCashier,
    params
  })
}
export const mfsetCheckCashierCate = (params?: {}) => {
  return defHttp.post({
    url: Api.mfsetCheckCashierCate,
    params
  })
}
