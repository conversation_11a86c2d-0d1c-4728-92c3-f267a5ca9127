import type { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const projectList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    const list: any[] = []
    for (let j = 0; j < 5; j++) {
      const children: any[] = []
      for (let k = 0; k < 5; k++) {
        children.push({
          name: '@ctitle',
          'imgs|0-5': ['@image("200x200")'],
          puid: '@guid',
          'purchase_status|1': [0, 1, 15],
          'inwarehouse_status|1': [0, 1, 2, 3],
          'outwarehouse_status|1': [0, 1, 2],
          not_purchase_count: '@integer(0, 100)',
          not_inwarehouse_count: '@integer(0, 100)',
          not_outwarehouse_count: '@integer(0, 100)',
          unit_price: '@float(0, 100, 2, 2)',
          quantity: '@integer(0, 100)',
          total_price: '@float(0, 100, 2, 2)'
        })
      }
      list.push({
        created_at: '@datetime',
        id: '@id',
        name: '@ctitle',
        strid: '@guid',
        project_name: '@ctitle',
        'is_started|1': [1, 0],
        'purchase_status|1': [0, 1, 15],
        'inwarehouse_status|1': [0, 1, 2, 3],
        'outwarehouse_status|1': [0, 1, 2],
        quality_check_rate: '@integer(0, 100)',
        receive_count: '@integer(0, 100)',
        pay_count: '@integer(0, 100)',
        retreat_item_count: '@integer(0, 100)',
        retreat_count: '@integer(0, 100)',
        childrenList: children
      })
    }
    result.push({
      id: '@id',
      name: '@ctitle',
      client_name: '@cname',
      list,
      revisit: {
        count: '@integer(0, 100)',
        visit_date: '@datetime',
        'visit_way|1': ['1', '2'],
        group_name: '@name'
      }
    })
  }
  return { items: result, total: 5 }
})()

const relatePurchaseList = (function () {
  const items: any[] = []
  for (let i = 0; i < 5; i++) {
    items.push({
      id: '@id',
      strid: '@guid',
      'status|1': [0, 1, 15],
      'is_check|1': [1, 2],
      created_at: '@datetime',
      item_count: '@integer(0, 100)',
      cost: '@float(0, 100, 2, 2)',
      paid: '@float(0, 100, 2, 2)',
      dept_name: '@ctitle',
      supplier_name: '@ctitle'
    })
  }
  return { items, total: 5 }
})()

export default [
  {
    url: '/api/projectOverview/getList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(projectList)
    }
  },
  {
    url: '/api/projectOverview/getRelatePurchaseList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(relatePurchaseList)
    }
  }
] as MockMethod[]
