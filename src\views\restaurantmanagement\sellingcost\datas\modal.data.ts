import { FormSchema } from '/@/components/Form'
import { mfgetShopMenuList } from '/@/api/restaurantmanagement/restaurantdishes'

export const addschemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'code',
    label: 'code',
    component: 'Input',
    show: false
  },
  {
    field: 'name',
    label: '菜品名称',
    required: true,
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: mfgetShopMenuList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        params: {
          no_cache: 0
        },
        pagingSize: 20,
        returnParamsField: 'name',
        selectProps: {
          fieldNames: { key: 'id', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          onChange(_, shall) {
            if (!shall) return
            formModel.dept_id = shall.dept_id == 0 ? undefined : shall.dept_id
            formModel.code = shall.code
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '空中餐厅',
          value: 173
        },
        {
          label: 'SAWA餐厅',
          value: 174
        },
        {
          label: '麟料理餐厅',
          value: 175
        },
        {
          label: '江南餐厅',
          value: 176
        },
        {
          label: '法餐厅',
          value: 177
        },
        {
          label: '甜点酒水',
          value: 178
        },
        {
          label: '粤棠餐厅',
          value: 189
        }
      ]
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'cost',
    label: '成本',
    required: true,
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0
    },
    colProps: {
      span: 24
    }
  }
]

export const statusschemas: FormSchema[] = [
  {
    field: 'status',
    label: '状态',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '禁用',
          value: 0
        }
      ]
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'code',
    label: '勾选商品',
    component: 'Input',
    slot: 'names'
  }
]
