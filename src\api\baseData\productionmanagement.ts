//资金档案
import { defHttp } from '/@/utils/http/axios'
enum Api {
  productionupdate = '/erp/production/update',
  productiongetList = '/erp/production/getList',
  productionsetStatus = '/erp/production/setStatus',
  productionsetIsDisabled = '/erp/production/setIsDisabled'
}

export const productiongetList = (params?: {}) => defHttp.get({ url: Api.productiongetList, params })
export const productionupdate = (params?: {}) =>
  defHttp.post({ url: Api.productionupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const productionsetStatus = (params?: {}) =>
  defHttp.post({ url: Api.productionsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const productionsetIsDisabled = (params?: {}) =>
  defHttp.post({ url: Api.productionsetIsDisabled, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
