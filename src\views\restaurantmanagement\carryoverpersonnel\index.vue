<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editModal @register="registereditModal" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { columns } from './datas/datas'
import { mfgetCheckCashier } from '/@/api/restaurantmanagement/carryoverpersonnel'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import editModal from './components/editModal.vue'
import { useModal } from '/@/components/Modal'

const [registereditModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  columns,
  api: mfgetCheckCashier,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '绑定科目',
      onClick: handleEdit.bind(null, record)
    }
  ]

  return editButtonList
}

function handleEdit(record) {
  console.log(record)
  openModal(true, record)
}
</script>
