import type { BasicColumn } from '/@/components/Table'
import type { FormSchema } from '/@/components/Form'
import { formatter } from '/@/utils/erp/formatterPrice'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getDeptSelectTree } from '/@/api/admin/dept'

import { useRender } from '/@/components/Table/src/hooks/useRender'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { nextTick } from 'vue'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
const pathname = window.location.pathname
console.log(pathname)

export //status
const statusMap = {
  0: { color: '', text: '待执行' },
  1: { color: 'green', text: '已执行' },
  15: { color: 'red', text: '已结束' },
  16: { color: 'red', text: '已作废' }
}
export const financestatus = {
  1: { label: '未确认', value: 1 },
  2: { label: '主管未审批', value: 2 },
  3: { label: '财务未审核', value: 3 },
  4: { label: '财务已审核', value: 4 },
  5: { label: '财务驳回', value: 5 }
}
export const checkMap = {
  0: { color: '', text: '未审核' },
  1: { color: 'green', text: '通过' },
  2: { color: 'red', text: '驳回' }
}
export const managerstatus = {
  0: { color: '', text: '待审核' },
  1: { color: 'green', text: '总经理助理已审核' },
  2: { color: 'pink', text: '总经理已审核' },
  3: { color: 'red', text: '总经理助理驳回' },
  4: { color: 'red', text: '总经理驳回' }
}
export const orderMap = {
  1: { color: 'green', text: '销售订单' },
  2: { color: 'red', text: '采购订单' },
  3: { color: 'orange', text: 'OA订单' },
  4: { color: 'skyblue', text: '费用报销(无发票)' },
  5: { color: 'pink', text: '餐厅报销' },
  6: { color: 'blue', text: '费用报销(有发票)' },
  7: { color: 'DarkTurquoise', text: '财务费用支出' }
}
export const bindfund = {
  0: { color: 'red', text: '未关联 ' },
  1: { color: 'green', text: '已关联' }
}
export const urgentlevel = {
  1: { color: '', text: '一般 ' },
  2: { color: 'red', text: '紧急' }
}

export const typeMap = {
  1: { text: '个人报销', color: 'default' },
  2: { text: '款项支出', color: 'blue' },
  3: { text: '财务费用', color: 'green' }
}

export const columns: BasicColumn[] = [
  {
    title: '其他支出单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '支付账号名称',
    dataIndex: 'account_name',
    width: 200,
    resizable: true
  },
  {
    title: '开户行',
    dataIndex: 'bank',
    width: 200,
    resizable: true,
    ifShow: pathname == '/car/' ? true : false
  },
  {
    title: '支出备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true
  },
  {
    title: '紧急状态',
    dataIndex: 'urgent_level',
    width: 200,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },

  {
    title: '审核日期',
    dataIndex: 'check_at',
    width: 200,
    resizable: true
  },
  {
    title: '是否关联流水',
    dataIndex: 'is_bind_fund',
    width: 100,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'order',
    width: 100,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return useRender.renderTag(typeMap[text].text, typeMap[text].color)
    }
  },
  {
    title: '结算货币',
    dataIndex: 'currency',
    width: 100,
    resizable: true
  },
  {
    title: '结算货币汇率',
    dataIndex: 'exchange_rate',
    width: 100,
    resizable: true
  },
  {
    title: '外汇金额',
    dataIndex: 'foreign_currency_amount',
    width: 120,
    customRender: ({ value }) => {
      return formatter.format(value)
    },
    resizable: true
  },
  {
    title: '支出金额(rmb)',
    dataIndex: 'amount',
    width: 150,
    customRender: ({ record }) => {
      return formatter.format(record.amount)
    },
    resizable: true
  },
  // {
  //   title: '已付金额',
  //   dataIndex: 'paid',
  //   width: 100,
  //   customRender: ({ value }) => {
  //     return formatter.format(value)
  //   },
  //   resizable: true
  // },
  // {
  //   title: '财务审核状态',
  //   dataIndex: 'is_check',
  //   width: 100,
  //   customRender: ({ value }) => {
  //     return h(Tag, { color: statusMap[value].color }, value)
  //   },
  //   resizable: true
  // },
  {
    title: '单据进行状态',
    dataIndex: 'finance_status',
    width: 100,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    resizable: true
  },

  {
    title: '行政审核',
    dataIndex: 'administration_status',
    width: 150,
    resizable: true
  },
  {
    title: '总经理审核',
    dataIndex: 'manager_status',
    width: 150,
    resizable: true
  },
  {
    title: '驳回备注',
    dataIndex: 'manager_status_remark',
    width: 100,
    resizable: true
  },
  {
    title: '总经理审核日期',
    dataIndex: 'manager_status_at',
    width: 200,
    resizable: true
  },
  {
    title: '财务审核',
    dataIndex: 'is_check',
    width: 100,
    resizable: true
  },
  {
    title: '出纳审核',
    dataIndex: 'is_check2',
    width: 100,
    resizable: true
  },

  {
    title: '支出摘要',
    dataIndex: 'desc',
    width: 200,
    resizable: true
  }
]

const statusOptions = [
  { label: '待执行', value: 0 },
  { label: '已执行', value: 1 },
  { label: '已结束', value: 15 },
  { label: '已作废', value: 16 }
]

export const ALL = 'all'
export const SALE = 'sale'
export const PURCHASE = 'purchase'
export const OA = 'oa'
export const OTHER = 'other'
export const FIVE = 'five'
export const SIX = 'six'
export const SEVEN = 'seven'
export const mapTypeMenu = {
  [ALL]: null,
  [SALE]: 1,
  [PURCHASE]: 2,
  [OA]: 3,
  [OTHER]: 4,
  [FIVE]: 5,
  [SIX]: 6,
  [SEVEN]: 7
}
export const menuOptions = [
  { value: ALL, label: '全部' },
  { value: SALE, label: '销售订单' },
  { value: PURCHASE, label: '采购订单' },
  { value: OA, label: 'OA工单号' },
  { value: OTHER, label: '费用报销(无发票)' },
  { value: SIX, label: '费用报销(有发票)' },
  { value: FIVE, label: '餐厅报销' },
  { value: SEVEN, label: '财务费用支出' }
]

const status_schema = GET_STATUS_SCHEMA(statusOptions, {
  colProps: {
    span: 12
  }
})

export function formConfigFn(params: number, tableAction?): FormSchema[] {
  return [
    {
      field: 'mapOrder',
      label: '',
      defaultValue: ALL,
      component: 'RadioButtonGroup',
      componentProps: ({ formActionType }) => ({
        options: menuOptions,
        onChange: (value) => {
          tableAction?.setProps({
            searchInfo: {
              order: mapTypeMenu[value]
            }
          })

          nextTick(() => formActionType.submit())
        }
      }),

      colProps: {
        span: 12
      }
    },
    status_schema,
    {
      field: 'strid',
      label: '其他支出单号',
      component: 'Input'
    },
    {
      field: 'source_uniqid',
      label: '销售单号',
      component: 'Input'
    },
    {
      field: 'item_strid',
      label: '支出明细单号',
      component: 'Input'
    },
    {
      field: 'account_name',
      label: '支付账号名称',
      component: 'Input'
    },
    // {
    //   field: 'strid',
    //   label: '销售订单号',
    //   component: 'Input'
    // },
    // {
    //   field: 'status',
    //   label: '账单状态',
    //   component: 'Select',
    //   componentProps: {
    //     options: statusOptions
    //   }
    // },
    {
      field: 'urgent_level',
      label: '紧急状态',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '一般',
            value: 1
          },
          {
            label: '紧急',
            value: 2
          }
        ]
      }
    },
    {
      field: 'is_check',
      label: '财务审核',
      component: 'Select',
      componentProps: {
        options: [
          { label: '未审核', value: 0 },
          { label: '通过', value: 1 },
          { label: '驳回', value: 2 }
        ]
      }
    },
    {
      field: 'finance_status',
      label: '单据进行状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '未确认', value: 1 },
          { label: '主管未审批', value: 2 },
          { label: '财务未审核', value: 3 },
          { label: '财务已审核', value: 4 },
          { label: '财务驳回', value: 5 }
        ]
      }
    },
    {
      field: 'is_bind_fund',
      label: '是否关联流水',
      component: 'Select',
      componentProps: {
        options: [
          { label: '未关联', value: 0 },
          { label: '已关联', value: 1 }
        ]
      }
    },
    {
      field: 'desc',
      label: '支出摘要',
      component: 'Input'
    },
    {
      field: 'detail_desc',
      label: '明细摘要',
      component: 'Input'
    },
    {
      field: 'check_at',
      label: '审核日期',
      component: 'SingleRangeDate',

      componentProps: {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        }
      }
    },
    {
      field: 'dept_ids',
      label: '部门(多选)',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        immediate: false,
        lazyLoad: true,
        maxTagCount: 3,
        treeCheckable: true,
        multiple: true,
        treeSelectProps: {
          treeDataSimpleMode: true,
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          treeCheckable: true,
          showCheckedStrategy: 'SHOW_ALL',
          treeDefaultExpandAll: true,
          showSearch: true,
          treeLine: {
            showLeafIcon: false
          },

          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      }
    },
    {
      field: 'dept_id',
      label: '部门(单选)',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        immediate: false,
        lazyLoad: true,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          optionFilterProp: 'name',
          treeDefaultExpandAll: true,
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      }
    },
    {
      field: 'type',
      label: '类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: '个人报销', value: '1' },
          { label: '款项支出', value: '2' }
        ]
      },
      ifShow: params == 1 ? false : true
    },
    {
      field: 'creator',
      label: '创建人',
      component: 'PagingApiSelect',
      componentProps: {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getCreatorList,
        resultField: 'items',
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'applicant',
      label: '申请人',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCreatorList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCreatorList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items',
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'doc_amount',
      label: '支出金额',
      component: 'InputNumber'
    },
    {
      field: 'mx_amount',
      label: '明细支出金额',
      component: 'InputNumber'
    },
    {
      field: 'account_code',
      label: '支出科目',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_code',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        },

        params: {
          status: 1
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    }
  ]
}
