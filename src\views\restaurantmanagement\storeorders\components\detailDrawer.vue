<template>
  <BasicDrawer @register="registerDrawer" title="详情" width="90%">
    <Description @register="register" class="mt-4" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Description, useDescription } from '/@/components/Description/index'
import { ref } from 'vue'
import { schema } from '../datas/detail'

const mockData = ref()

const [registerDrawer] = useDrawerInner((data) => {
  console.log(data)
  mockData.value = data
})

const [register] = useDescription({
  data: mockData,
  size: 'middle',
  schema
})
</script>
