import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetReversalOrderList = '/erp/finance/ft/getList',
  AddReversalOrder = '/erp/finance/ft/create',
  UpdateReversalOrder = '/erp/finance/ft/update',
  DeleteReversalOrder = '/erp/finance/ft/remove',
  DetailsReversalOrder = '/erp/finance/ft/details',
  ExamineReversalOrder = '/erp/finance/ft/setStatus'
}

interface addReversalOrderType {
  doc: { inCharge: number; applicant: number; amount: number }
  fund1: Array<{ fund_id: number; amount: number }>
  fund2: Array<{ fund_id: number; amount: number }>
}

interface updateReversalOrderType {
  doc: { inCharge: number; applicant: number; amount: number; doc_id: number }
  fund1: Array<{ fund_id: number; amount: number; id: number }>
  fund2: Array<{ fund_id: number; amount: number; id: number }>
}

/** 获取 */
export const getReversalOrderList = (params?: {}) => defHttp.get({ url: Api.GetReversalOrderList, params })

/** 新增 */
export const addReversalOrder = (params: addReversalOrderType) => defHttp.post({ url: Api.AddReversalOrder, params })

/** 编辑 */
export const updateReversalOrder = (params: updateReversalOrderType) => defHttp.post({ url: Api.UpdateReversalOrder, params })

/** 删除 */
export const deleteReversalOrder = (params: { id: number }) => defHttp.get({ url: Api.DeleteReversalOrder, params })

/** 详情 */
export const detailsReversalOrder = (params: { id: number }) => defHttp.get({ url: Api.DetailsReversalOrder, params })

/** 审核 */
export const examineReversalOrder = (params: { id: number }) => defHttp.get({ url: Api.ExamineReversalOrder, params })
