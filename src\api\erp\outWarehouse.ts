import { defHttp } from '/@/utils/http/axios'
import type {
  AddOutWarehouseData,
  OutWarehouseItem,
  OutWarehouseParams,
  OrderOutDetail,
  OutWareHouseItemDetail
} from '/@/api/erp/modle/types'
import type { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel'
import type { IGetInWarehouseList } from './modle/inWarehouseModel'
import type { IGetOutWarehouseList } from './modle/outWarehouseModle'

enum Api {
  GetOutWarehouseListByDate = '/erp/stock/ow/getListByDate',
  GetOutWarehouseList = '/erp/stock/ow/getDoc',
  AddOutWarehouse = '/erp/stock/ow/update',
  GetOutWarehouseDetail = '/erp/stock/ow/getItem',
  //获取入库单列表
  GetstockgetDoc = '/erp/stock/ew/getDoc',
  GetOutNum = '/erp/stock/son/getOutNum',
  SetOutNum = '/erp/stock/son/update',
  DelOutWarehouse = '/erp/stock/ow/deleteDoc',
  ApproveOutWarehouse = '/erp/stock/ow/setAuthStatus',
  GetOutWarehouseSaleOrder = '/tools/getStockWorkSales',
  DeleteGoodsDetail = '/erp/stock/ow/deleteItem',
  VerifyOutWarehouse = '/erp/stock/ow/setAuthStatus',
  GetListByDate = '/erp/stock/ow/getListByDate',
  SetVerifyCount = '/erp/stock/ow/isVerify',
  VerifyOutOrder = '/tools/getOuthouseSchedule',
  SetUrgency = '/erp/stock/ow/setUrgency',
  SetRemark = '/erp/stock/ow/setRemark',
  SetCancel = '/erp/stock/ow/setCancel',
  SetShipmentCharge = '/erp/stock/ow/setShipmentInCharge',
  ExportFile = '/erp/stock/ow/export',
  //附件更改
  setUploadFiles = '/erp/stock/ow/setFiles',
  //同步水印图片
  updateImages = '/erp/stock/ow/updateImages',
  SetInvoiceNumber = '/erp/stock/ow/setInvoiceNumber',
  stockowsetStatus = '/erp/stock/ow/setStatus'
}

export const getstockgetDoc = (params?: Partial<Omit<IGetInWarehouseList, 'work_strid'>> & Partial<BasicPageParams> & { strid?: string }) =>
  defHttp.get({ url: Api.GetstockgetDoc, params })

//按日期获取出库单
export const getOutWarehouseListByDate = (params: OutWarehouseParams) =>
  defHttp.get<{ items: { [key: string]: OutWarehouseItem } }>({ url: Api.GetOutWarehouseListByDate, params }).then((res) =>
    Object.keys(res.items)
      .sort((a, b) => +new Date(a) - +new Date(b))
      .map((key) => ({ date: key, order_info: res.items[key] }))
  )

//获取出库单列表
export const getOutWarehouseList = (params: Partial<BasicPageParams> & Partial<IGetOutWarehouseList>) =>
  defHttp.get({ url: Api.GetOutWarehouseList, params })

export const addOutWarehouse = (data: AddOutWarehouseData) => defHttp.post({ url: Api.AddOutWarehouse, data })

export const getOrderOutCount = (params: { work_ids: number[]; doc_id: number }) =>
  defHttp.get<BasicFetchResult<OrderOutDetail>>({ url: Api.GetOutNum, params })

export const setOrderOutCount = (params: { work_id: number; quantity: number; doc_id: number }) =>
  defHttp.get({ url: Api.SetOutNum, params })

export const getOutWarehouseDetail = (params: { doc_id: number; is_residuce?: number }) =>
  defHttp.get<BasicFetchResult<OutWareHouseItemDetail>>({ url: Api.GetOutWarehouseDetail, params })

export const delOutWarehouse = (id: number) => defHttp.get<{ msg: string }>({ url: Api.DelOutWarehouse, params: { id } })

export const approveOutWarehouse = (params: { doc_id: number; status: number }) => defHttp.get({ url: Api.ApproveOutWarehouse, params })

/**获取出库的销售单号 */
export const getOutWarehouseSaleOrder = (params?: {}) => defHttp.get({ url: Api.GetOutWarehouseSaleOrder, params })

//删除明细(删除商品)
export const deleteGoodsDetail = (params: { id: number }) =>
  defHttp.get({ url: Api.DeleteGoodsDetail, params }, { successMessageMode: 'message' })

//审核出库单
export const verifyOutWarehouse = (params: { doc_id: number; status: 1 }) =>
  defHttp.get({ url: Api.VerifyOutWarehouse, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//看板
export const getListByDate = (params?: any) => defHttp.get({ url: Api.GetListByDate, params })

// 导出出库单的搜索条件
export const exportOutWarehouse = (params?: any) =>
  defHttp.get({ url: Api.ExportFile, params, responseType: 'blob' }, { isTransformResponse: false })

// 设置预计包裹数
export const setVerifyCount = (params: { doc_id: number; verify: Array<{ work_id: number | string; pkg_quantity: number }> }) =>
  defHttp.get({ url: Api.SetVerifyCount, params })

// 获取验证的出库订单
export const verifyOutOrder = (doc_id: number) => defHttp.get({ url: Api.VerifyOutOrder, params: { doc_id } })

// 设置出库单紧急状态
export const setUrgency = (params: { id: number; urgent_level: number }) => defHttp.get({ url: Api.SetUrgency, params })

export const setRemark = (params: { id: number; remark: string }) => defHttp.get({ url: Api.SetRemark, params })

export const setCancel = (id: number) => defHttp.get({ url: Api.SetCancel, params: { id } })

export const setShipmentCharge = (params) => defHttp.get({ url: Api.SetShipmentCharge, params })
export const setUploadFiles = (params) => defHttp.post({ url: Api.setUploadFiles, params })
export const updateImages = (params) =>
  defHttp.get({ url: Api.updateImages, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
// 设置发票号
export const setInvoiceNumber = (data: { id: number; invoice_number: string }) => defHttp.post({ url: Api.SetInvoiceNumber, data })
export const stockowsetStatus = (params?: {}) =>
  defHttp.post({ url: Api.stockowsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
