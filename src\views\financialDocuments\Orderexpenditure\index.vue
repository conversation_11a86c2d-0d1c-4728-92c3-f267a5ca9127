<template>
  <div>
    <BasicTable @register="registerTable">
      <template #expandedRowRender="{ record: fatherrecord }">
        <BasicTable
          :ref="(el) => (expandedRowRefs[fatherrecord.id] = el)"
          :api="projectwkgetBillDetails.bind(null, { pageSize: 9999, source_uniqid: fatherrecord.source_uniqid })"
          :canResize="false"
          :columns="childercolumns(handleViewRelate)"
          :showIndexColumn="false"
        >
          <template #footer>
            <div class="footer">
              <span class="mr-[20px]"
                >已收金额合计： {{ handleAfterFetch(expandedRowRefs[fatherrecord.id], 'totalAmount').toFixed(2) }}
              </span>
              <span class="mr-[20px]"
                >未收金额合计：{{ handleAfterFetch(expandedRowRefs[fatherrecord.id], 'totalAmountAns').toFixed(2) }}</span
              >
              <span class="mr-[20px]"
                >已付金额合计：{{ handleAfterFetch(expandedRowRefs[fatherrecord.id], 'totalAmountRec').toFixed(2) }}</span
              >
              <span>未付金额合计：{{ handleAfterFetch(expandedRowRefs[fatherrecord.id], 'totalAmountRecAns').toFixed(2) }}</span>
            </div>
          </template>
        </BasicTable>
      </template>
      <template #footer>
        <div class="footer">
          <div class="totals-container">
            <!-- 第一行：7个字段 -->
            <div class="totals-row first-row">
              <span class="total-item">改单后金额{{ formateerNotCurrency.format(totalsData.receivable_left, 2) }}</span>
              <span class="total-item">实收金额：{{ formateerNotCurrency.format(totalsData.received_actual, 2) }}</span>
              <span class="total-item">已采购销售金额：{{ formateerNotCurrency.format(totalsData.purSaleAmount, 2) }}</span>
              <span class="total-item">未采购销售金额：{{ formateerNotCurrency.format(totalsData.noPurSaleAmount, 2) }}</span>
              <span class="total-item">已采购采购金额：{{ formateerNotCurrency.format(totalsData.purchaselotalPrice, 2) }}</span>
              <span class="total-item">采购实付金额：{{ formateerNotCurrency.format(totalsData.purchasePaidActual, 2) }}</span>
              <span class="total-item">采购未付金额：{{ formateerNotCurrency.format(totalsData.purchaseNoPay, 2) }}</span>
              <!-- <span class="total-item">预估毛利：{{ formateerNotCurrency.format(totalsData.estimateGrossProfits, 2) }}</span> -->
              <span class="total-item">其他支出总金额：{{ formateerNotCurrency.format(totalsData.otherDisburseAmount, 2) }}</span>
              <span class="total-item">其他支出未付总金额：{{ formateerNotCurrency.format(totalsData.otherNoPayAmount, 2) }}</span>
              <span class="total-item">其他收入总金额：{{ formateerNotCurrency.format(totalsData.otherReceiptAmount, 2) }}</span>
              <span class="total-item">其他收入未收总金额：{{ formateerNotCurrency.format(totalsData.otherNoReceiptAmount, 2) }}</span>
              <span class="total-item">预估利润：{{ formateerNotCurrency.format(totalsData.estimateProfits, 2) }}</span>
            </div>
          </div>
        </div>
      </template>
    </BasicTable>
    <VerifyDrawer @register="registersalesDrawer" />
    <receiptDetailsDrawer @register="registerreceiptDetailsDrawer" />
    <paymentDetailsDrawer @register="registerpaymentDetailsDrawer" />
    <RefundDrawer @register="refundDrawer" />
    <IncomeDetailsDrawer @register="registerIncomeDetailsDrawer" />
    <otherexpendDetailsDrawer @register="registerotherexpendDetailsDrawer" />
    <refunddDetailDrawer @register="refunddDrawer" />
    <purcgaseDetailDrawer @register="purchaseDrawer" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, TableActionType, useTable } from '/@/components/Table'
import { childercolumns, columns, schemas } from './datas/datas'
import { getSalesOrderList, projectwkgetBillDetails } from '/@/api/erp/sales'
import { ref } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { projectwkgetBillOrderList } from '/@/api/wms/realTimeInventory'
import VerifyDrawer from '/@/views/erp/saleOrder/components/VerifyDrawer.vue'
import receiptDetailsDrawer from '/@/views/financialDocuments/receiptOrder/components/DetailsDrawer.vue'
import paymentDetailsDrawer from '/@/views/financialDocuments/paymentOrder/components/DetailsDrawer.vue'
import RefundDrawer from '/@/views/financialDocuments/refund/components/refundDrawer.vue'
import IncomeDetailsDrawer from '/@/views/financialDocuments/otherExpend/components/DetailsDrawer.vue'
import otherexpendDetailsDrawer from '/@/views/financialDocuments/otherIncomeExpend/components/DetailsDrawer.vue'
import refunddDetailDrawer from '/@/views/financialDocuments/refund/components/refundDrawer.vue'
import purcgaseDetailDrawer from '/@/views/erp/purchaseOrder/components/purchaseDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { getOtherExpendList } from '/@/api/financialDocuments/otherExpend'
import { getOthergetList } from '/@/api/financialDocuments/otherIncome'
import { getFundManage } from '/@/api/financialDocuments/refund'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'

const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})

//详情
const [registersalesDrawer, { openDrawer: opensalesDrawer }] = useDrawer()
const [registerreceiptDetailsDrawer, { openDrawer: openreceipptDrawer }] = useDrawer()
const [registerpaymentDetailsDrawer, { openDrawer: openrepaymentDrawer }] = useDrawer()
const [refundDrawer, { openDrawer: openRefundDrawer }] = useDrawer()
const [registerIncomeDetailsDrawer, { openDrawer: openIncomeDetailsDrawer }] = useDrawer()
const [registerotherexpendDetailsDrawer, { openDrawer: openotherexpendDetailsDrawer }] = useDrawer()
const [refunddDrawer, { openDrawer: refundDetailsDrawer }] = useDrawer()
const [purchaseDrawer, { openDrawer: purshaseDetailsDrawer, setDrawerProps: setpurchaseDrawerProps }] = useDrawer()

// 13个字段的合计
const totalsData = ref({
  receivable_left: 0, // 改单后订单总额
  received_actual: 0, // 实收金额
  purSaleAmount: 0, // 已采购销售金额
  noPurSaleAmount: 0, // 未采购销售金额
  purchaselotalPrice: 0, // 已采购采购金额
  purchasePaidActual: 0, // 采购实付金额
  purchaseNoPay: 0, // 采购未付金额
  // estimateGrossProfits: 0, // 预估毛利
  otherDisburseAmount: 0, // 其他支出总金额
  otherNoPayAmount: 0, // 其他支出未付总金额
  otherReceiptAmount: 0, // 其他收入总金额
  otherNoReceiptAmount: 0, // 其他收入未收总金额
  estimateProfits: 0 // 预估利润
})

// 更新合计数据的函数
function updateTotals(totals: any) {
  totalsData.value = { ...totals }
}

const [registerTable] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  columns: columns(handleViewRelate),
  api: projectwkgetBillOrderList,
  afterFetch: (data) => {
    // 计算13个字段的合计
    const totals = {
      receivable_left: 0, // 改单后订单总额
      received_actual: 0, // 实收金额
      purSaleAmount: 0, // 已采购销售金额
      noPurSaleAmount: 0, // 未采购销售金额
      purchaselotalPrice: 0, // 已采购采购金额
      purchasePaidActual: 0, // 采购实付金额
      purchaseNoPay: 0, // 采购未付金额
      // estimateGrossProfits: 0, // 预估毛利
      otherDisburseAmount: 0, // 其他支出总金额
      otherNoPayAmount: 0, // 其他支出未付总金额
      otherReceiptAmount: 0, // 其他收入总金额
      otherNoReceiptAmount: 0, // 其他收入未收总金额
      estimateProfits: 0 // 预估利润
    }

    // 遍历数据计算合计
    data.forEach((item) => {
      totals.receivable_left += Number(item.receivable_left) || 0
      totals.received_actual += Number(item.received_actual) || 0
      totals.purSaleAmount += Number(item.purSaleAmount) || 0
      totals.noPurSaleAmount += Number(item.noPurSaleAmount) || 0
      totals.purchaselotalPrice += Number(item.purchaselotalPrice) || 0
      totals.purchasePaidActual += Number(item.purchasePaidActual) || 0
      totals.purchaseNoPay += Number(item.purchaseNoPay) || 0
      // totals.estimateGrossProfits += Number(item.estimateGrossProfits) || 0
      totals.otherDisburseAmount += Number(item.otherDisburseAmount) || 0
      totals.otherNoPayAmount += Number(item.otherNoPayAmount) || 0
      totals.otherReceiptAmount += Number(item.otherReceiptAmount) || 0
      totals.otherNoReceiptAmount += Number(item.otherNoReceiptAmount) || 0
      totals.estimateProfits += Number(item.estimateProfits) || 0
    })

    // 对所有金额进行四舍五入保留两位小数
    Object.keys(totals).forEach((key) => {
      totals[key] = Math.round(totals[key] * 100) / 100
    })

    // 将合计数据存储到响应式变量中（需要在组件中定义这些变量）
    updateTotals(totals)

    return data
  },
  formConfig: {
    schemas
  }
})

async function handleViewRelate(record, type) {
  console.log(record, type)
  switch (record.type) {
    case 3:
    case 27:
      opensalesDrawer(true, record)
      break
    case '收款单':
      if (type !== 'mx') {
        openreceipptDrawer(true, {
          id: record.doc_fund_id
        })
      } else {
        const detail = await getSalesOrderList({ source_uniqid: record.mx_strid, menuTypes: 'all', types: [3, 27] })
        opensalesDrawer(true, detail.items[0])
      }
      break
    case '付款单':
      if (type !== 'mx') {
        openrepaymentDrawer(true, {
          id: record.doc_fund_id,
          showProjectList: true,
          type: 'detail'
        })
      } else {
        const detail = await getPurchaseOrderList({ strid: record.mx_strid })
        purshaseDetailsDrawer(true, { record: detail.items[0], isUpdate: false, type: 'detail' })
        setpurchaseDrawerProps({ title: '查看采购订单详情' })
      }
      break
    case '收款单（采购退款）':
      if (type !== 'mx') {
        openreceipptDrawer(true, {
          id: record.doc_fund_id
        })
      } else {
        const detail = await getFundManage({ strid: record.mx_strid })
        refundDetailsDrawer(true, {
          type: 'detail',
          record: detail.items[0]
        })
      }

      break
    case '付款单（销售退款）':
      if (type !== 'mx') {
        openRefundDrawer(true, {
          type: 'detail',
          record: {
            id: record.mx_id
          }
        })
      } else {
        const detail = await getFundManage({ strid: record.mx_strid })
        refundDetailsDrawer(true, {
          type: 'detail',
          record: detail.items[0]
        })
      }
      break
    case '其他支出单':
      if (type !== 'mx') {
        openrepaymentDrawer(true, {
          id: record.doc_fund_id,
          showProjectList: true,
          type: 'detail'
        })
      } else {
        const detail = await getOtherExpendList({ item_strid: record.mx_strid, is_finance: record.is_finance })
        console.log(detail)

        openIncomeDetailsDrawer(true, {
          type: 'detail',
          record: detail.items[0]
        })
      }

      break
    case '其他收入单':
      if (type !== 'mx') {
        openreceipptDrawer(true, {
          id: record.doc_fund_id
        })
      } else {
        const detail = await getOthergetList({ item_strid: record.mx_strid })
        console.log(detail)

        openotherexpendDetailsDrawer(true, {
          type: 'detail',
          record: detail.items[0]
        })
      }
      break
  }
}

function handleAfterFetch(actions, type) {
  const dataSource = actions?.tableAction?.getDataSource()

  // 如果没有数据源，返回0
  if (!dataSource || !Array.isArray(dataSource)) {
    return 0
  }

  // 为当前子表格单独计算各种金额，不使用全局变量
  let currentTotalAmount = 0
  let currentTotalAmountAns = 0
  let currentTotalAmountRec = 0
  let currentTotalAmountRecAns = 0

  // 遍历当前子表格的数据源计算各种金额
  dataSource.forEach((item) => {
    if (item.is_income == 1 && item.is_finish == 1) {
      currentTotalAmount += Number(item.amount) || 0
    } else if (item.is_income == 1 && item.is_finish == 0) {
      currentTotalAmountAns += Number(item.amount) || 0
    } else if (item.is_income == 0 && item.is_finish == 1) {
      currentTotalAmountRec += Number(item.amount) || 0
    } else if (item.is_income == 0 && item.is_finish == 0) {
      currentTotalAmountRecAns += Number(item.amount) || 0
    }
  })

  // 根据传入的类型返回对应的值，并保留两位小数
  switch (type) {
    case 'totalAmount':
      return Math.round(currentTotalAmount * 100) / 100
    case 'totalAmountAns':
      return Math.round(currentTotalAmountAns * 100) / 100
    case 'totalAmountRec':
      return Math.round(currentTotalAmountRec * 100) / 100
    case 'totalAmountRecAns':
      return Math.round(currentTotalAmountRecAns * 100) / 100
    default:
      return 0
  }
}
</script>

<style lang="less" scoped>
.footer {
  padding: 16px 20px;
  background-color: #f8f9fa;

  .totals-container {
    .totals-row {
      display: flex;
      flex-wrap: wrap;

      &:last-child {
        margin-bottom: 0;
      }

      .total-item {
        margin-right: 20px;
        margin-bottom: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
