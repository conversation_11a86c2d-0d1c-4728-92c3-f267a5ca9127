import type { TreeDataItem } from 'ant-design-vue/es/tree/Tree'
export interface MenuItem {
  id?: number
  parentId: number
  type: number
  menuName: string
  icon: string
  order_no: string
  isExt: number
  isHidden: number
  noCache: number
  routePath: string
  component: string
  api: string
  status: number
  createTime?: string
}

export interface MenuSelectListItem extends TreeDataItem {
  id: number
  parentId: number
}
