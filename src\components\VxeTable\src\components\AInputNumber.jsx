import { create<PERSON>dit<PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON>, createForm<PERSON>temR<PERSON>, createDefaultFilterR<PERSON>, createDefaultRender, } from './common';
export default {
    autofocus: 'input.ant-input-number-input',
    renderDefault: createDefaultRender(),
    renderEdit: createEdit<PERSON><PERSON>(),
    renderFilter: createFilterRender(),
    defaultFilterMethod: createDefaultFilterRender(),
    renderItemContent: createFormItemRender(),
};
