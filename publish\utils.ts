// 解析参数
const formatParams = function (args) {
  return args.reduce((acc, arg, index, arr) => {
    if (arg.startsWith('--')) {
      // 参数名
      const paramName = arg.slice(2)

      // 参数值，如果下一个参数不是以 "--" 开头，则认为当前参数是值
      const paramValue = arr[index + 1]?.startsWith('--') ? undefined : arr[index + 1]

      // 存储参数
      acc[paramName] = paramValue
    }

    return acc
  }, {})
}

const getSSHPrivateKey = function (keyPath = '~/.ssh/id_rsa') {
  const shelljs = require('shelljs')
  const privateKeyResult = shelljs.cat(keyPath)
  if (privateKeyResult.stderr || privateKeyResult.code !== 0) {
    console.log('无法获取到私钥', keyPath)
    throw new Error(privateKeyResult.stderr)
  }
  return privateKeyResult.stdout
}

module.exports = {
  formatParams,
  getSSHPrivateKey
}
