import { computed, ref } from 'vue'
import { getStoreList } from '/@/api/commonUtils'
// import { WorkItem } from '/@/api/commonUtils/modle/types'

// export const workList = ref<WorkItem[]>([])
// export const mapWorkList = computed(() => {
//   if (workList.value?.length) {
//     const mapList: { [key: number]: WorkItem } = {}
//     for (const item of workList.value) {
//       mapList[item.id] = item
//     }
//     return mapList
//   }
//   return {}
// })

export const storeList = ref<{ id: number; name: string }[]>([])

export const mapStoreList = computed(() => {
  if (storeList.value?.length) {
    const mapList: { [key: number]: { id: number; name: string } } = {}
    for (const item of storeList.value) {
      mapList[item.id] = item
    }
    return mapList
  }
  return {}
})

// export async function getSalesWorkList() {
//   try {
//     const { items } = await getWorkList({ pageSize: 99999 })
//     workList.value = items
//   } catch (e) {
//     console.log(e)
//   }
// }

export async function getWarehouseList() {
  try {
    const { items } = await getStoreList()
    storeList.value = items
  } catch (e) {
    console.log(e)
  }
}
