import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 100,
    resizable: true
  },
  {
    title: '登记人',
    dataIndex: 'register_name',
    width: 100,
    resizable: true
  },
  {
    title: '登记日期',
    dataIndex: 'date',
    width: 100,
    resizable: true
  },
  {
    title: '登记类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: text == 1 ? '#1890ff' : '#f50' }, text == 1 ? '采购发票' : '报关发票')
    }
  },
  {
    title: '确认状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: text == 1 ? '#1890ff' : '#f50' }, text == 1 ? '已登记' : '未登记')
    }
  },
  {
    title: '确认时间',
    dataIndex: 'status_at',
    width: 100,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'strid',
    label: '单号',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'type',
    label: '登记类型',
    component: 'Select',
    colProps: {
      span: 8
    },
    componentProps: {
      options: [
        {
          label: '采购发票',
          value: 1
        },
        {
          label: '报关发票',
          value: 2
        }
      ]
    }
  },
  {
    field: 'status',
    label: '确认状态',
    component: 'Select',
    colProps: {
      span: 8
    },
    componentProps: {
      options: [
        {
          label: '已登记',
          value: 1
        },
        {
          label: '未登记',
          value: 2
        }
      ]
    }
  },
  {
    field: 'data',
    label: '登记时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'register_name',
    label: '登记人',
    component: 'Input',
    colProps: {
      span: 8
    }
  }
]
