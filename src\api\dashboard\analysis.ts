import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetPersonAnalysis = '/account/getList',
  GetOnTimeRatTop = '/account/getOnTimeRatTop',
  GetOnQcTimeRateTop = '/account/getDeliveryRatTop',
  GetRoles = '/erp/ad/getRoles'
}

export const getPersonAnalysis = (params?: {}) => defHttp.get({ url: Api.GetPersonAnalysis, params }, { isTransformResponse: false })

export const getOnTimeRatTop = (params?: { top: number; startDate: string; endDate: string }) =>
  defHttp.get({ url: Api.GetOnTimeRatTop, params })

export const getOnQcTimeRateTop = (params?: { top: number; startDate: string; endDate: string }) =>
  defHttp.get({ url: Api.GetOnQcTimeRateTop, params })

export const getRoles = (params: { roleId: number; page?: number; pageSize?: number }) => defHttp.get({ url: Api.GetRoles, params })
