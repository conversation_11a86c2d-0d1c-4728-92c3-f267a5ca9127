import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetItemsList = '/erp/purchase/getItemsList',
  SetStatus = '/erp/purchase/track/setStatus',
  Export = '/erp/purchase/export'
}

export const getItemsList = (params: {}) => defHttp.get({ url: Api.GetItemsList, params })

export const getItemsListexcel = (params: {}) =>
  defHttp.get(
    { url: Api.GetItemsList, params, responseType: 'blob' },
    { isTransformResponse: false, successMessageMode: 'message', errorMessageMode: 'message' }
  )
export const setStatus = (params: {}) => defHttp.post({ url: Api.SetStatus, params })

// 导出
export const exportFile = (params: {}) => defHttp.get({ url: Api.Export, params, responseType: 'blob' }, { isTransformResponse: false })
