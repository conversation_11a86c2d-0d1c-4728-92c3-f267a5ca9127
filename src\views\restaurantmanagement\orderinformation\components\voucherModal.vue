<template>
  <BasicModal @register="register" title="凭证生成" @ok="handok">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { meituanmfcreateOrderToInfoCert } from '/@/api/restaurantmanagement/orderinformation'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

const [register] = useModalInner(() => {})

const [registerForm, { validate }] = useForm({
  labelWidth: 90,
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'date',
      label: '日期',
      component: 'SingleRangeDate',
      required: true,
      componentProps: {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        }
      },
      rules: [
        {
          required: true,
          validator: (_, value) => {
            if (!value || !Array.isArray(value)) {
              return Promise.reject(new Error('请选择日期范围'))
            }
            if (!value[0] || !value[1]) {
              return Promise.reject(new Error('请选择完整的开始日期和结束日期'))
            }
            if (value.length !== 2) {
              return Promise.reject(new Error('日期范围必须包含开始日期和结束日期'))
            }
            return Promise.resolve()
          },
          trigger: 'change'
        }
      ]
    }
  ],
  fieldMapToTime: [['date', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
})

async function handok() {
  try {
    const formdata = await validate()
    console.log(formdata)

    meituanmfcreateOrderToInfoCert({
      begin_date: dayjs(formdata.date[0]).format('YYYY-MM-DD'),
      end_date: dayjs(formdata.date[1]).format('YYYY-MM-DD')
    })
      .then(() => {
        message.success('凭证生成成功')
      })
      .catch(() => {
        message.error('凭证生成失败')
      })
  } catch (e) {
    console.log(e)
  }
}
</script>
