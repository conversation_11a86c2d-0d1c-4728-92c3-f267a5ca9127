import type { ComputedRef, Slots } from 'vue'
import type { BasicTableProps, FetchParams } from '../types/table'
import { unref, computed, ref } from 'vue'
import type { FormProps } from '/@/components/Form'
import { isFunction } from '/@/utils/is'

export function useTableForm(
  propsRef: ComputedRef<BasicTableProps>,
  slots: Slots,
  fetch: (opt?: FetchParams | undefined) => Promise<void>,
  getLoading: ComputedRef<boolean | undefined>,
  tabsInfo: any
) {
  const getFormProps = computed((): Partial<FormProps> => {
    const { formConfig } = unref(propsRef)
    const { submitButtonOptions } = formConfig || {}
    return {
      showAdvancedButton: true,
      ...formConfig,
      submitButtonOptions: {
        loading: unref(getLoading),
        ...submitButtonOptions
      },
      compact: true
    }
  })

  const getFormSlotKeys: ComputedRef<string[]> = computed(() => {
    const keys = Object.keys(slots)
    return keys.map((item) => (item.startsWith('form-') ? item : null)).filter((item) => !!item) as string[]
  })

  function replaceFormSlotKey(key: string) {
    if (!key) return ''
    return key?.replace?.(/form\-/, '') ?? ''
  }

  function handleSearchInfoChange(info: Recordable) {
    const { handleSearchInfoFn } = unref(propsRef)
    if (handleSearchInfoFn && isFunction(handleSearchInfoFn)) {
      info = handleSearchInfoFn(info) || info
    }

    console.log(tabsInfo.getBindValues.value)

    // 如果有用Tabs
    if (tabsInfo.getBindValues.value.useSearchTabs) {
      fetch({
        searchInfo: {
          ...info,
          [tabsInfo.getBindValues.value.tabsConfig.fields]: tabsInfo.activeTabKey.value
        },
        page: 1
      })
    } else {
      fetch({ searchInfo: info, page: 1 })
    }
  }
  const show = ref(true)
  function getShowForm() {
    return unref(show)
  }

  function setShowForm(flag: boolean) {
    show.value = flag
  }

  return {
    getFormProps,
    replaceFormSlotKey,
    getFormSlotKeys,
    handleSearchInfoChange,
    getShowForm,
    setShowForm
  }
}

export function useTableTabs(
  propsRef: ComputedRef<BasicTableProps>,
  fetch: (opt?: FetchParams | undefined) => Promise<void>,
  useForm: boolean,
  formRef: any
) {
  // 控制当前选中的 Tab
  const activeTabKey: any = ref(undefined)

  // 控制 Tab 页内容的显示与否
  const showTabs = ref(true)

  // 获取表单的配置和插槽
  const getTabsProps = computed(() => {
    const { tabsConfig } = unref(propsRef)
    // const { submitButtonOptions } = tabsConfig || {}
    return {
      ...tabsConfig
      // submitButtonOptions: { loading: unref(getLoading), ...submitButtonOptions },
    }
  })

  // 切换 Tab 时的处理函数
  function handleTabChange(key: string) {
    const { tabsConfig } = unref(propsRef)
    activeTabKey.value = key
    // 在 Tab 切换时可以执行一些操作，比如刷新数据
    // 可以根据需要执行 fetch 或者其他操作
    if (useForm) {
      // 如果有表单，则将当前 Tab 的值加上表单的值作为表单的搜索条件
      const formData = formRef.value.formActionType.getFieldsValue
      let info = {}
      const { handleSearchInfoFn } = unref(propsRef)
      if (handleSearchInfoFn && isFunction(handleSearchInfoFn)) {
        info = handleSearchInfoFn({ ...formData, [tabsConfig.fields]: key }) || {}
      }
      fetch({ searchInfo: info, page: 1 })
    } else {
      fetch({ page: 1, searchInfo: { [tabsConfig.fields]: key } })
    }
  }

  // 获取当前显示的表单（根据 Tab 或其他条件）
  function getShowTabs() {
    return unref(showTabs)
  }

  // 设置表单显示状态
  function setShowTabs(flag: boolean) {
    showTabs.value = flag
  }

  return {
    activeTabKey,
    handleTabChange,
    getTabsProps,
    getShowTabs,
    setShowTabs
  }
}
