import { Data } from 'ant-design-vue/lib/_util/type'

// 员工
export interface personnel {
  id: number
  name: string
}

// 部门
export interface department {
  id: number
  name: string
}

// 下拉流水
export interface fundList {
  id: number
  fund_srtid: string
  amount: string
  amount_left: string
}

// 资金流水
export interface capitalFlow {
  occurrence_at: Data
  type: number
  from_plaform: string
  from_currency: string
  to_plaform: number
  to_currency: string
  client_id: number
  supplier_id: number
  fee: number
  amount: number
  fund_id?: number
}

// 收款单
export interface receiptOrder {
  strid: string
  created_at: string
  creator: string
  creator_name: string
  fund_id: number
  processor_name: string
  type: string
  amount: string
  amount_left: string
  status: number
}

// 付款单
export interface paymentOrder {
  strid: string
  created_at: string
  creator: string
  creator_name: string
  fund_id: number
  processor_name: string
  type: string
  amount: string
  amount_left: string
  status: number
}

// 其他收入单
export interface otherIncome {
  id: any
  name: string
  date: string
  order_no: string
  customer: string
  department: string
  fund_return: string
  amount: string
  salesorder_no: string
  amount_collected: string
  fund_id: string
  category: string
  revenue_details: Array<string>
}

// 其他支出单
export interface otherExpend {
  date: string
  order_no: string
  customer: string
  department: string
  fund_return: string
  amount: string
  salesorder_no: string
  account_bank: string
  account_bank_no: string
  fund_id: string
  category: string
  abstract: string
  expense_details: Array<string>
}

export interface IFundFlowBathImportItem {
  occurrence_at: Date
  type: 1 | 2
  amount: number
  to_plaform: string
  from_plaform: string
  remark: string
}
