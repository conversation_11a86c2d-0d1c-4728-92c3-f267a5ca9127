import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { isNull } from 'lodash-es'
import { useI18n } from '/@/hooks/web/useI18n'
import { getStaffList } from '/@/api/erp/systemInfo'
import { getDeptTree } from '/@/api/admin/dept'
import { usePermission } from '/@/hooks/web/usePermission'
import { GET_STATUS_SCHEMA } from '/@/const/status'

const { hasPermission } = usePermission()
const { t } = useI18n()
//订单类型和状态
const saleStore = useSaleOrderStore()
export const commonMap = useMapStoreWithOut()
export const mapQcType = {
  1: '线上质检',
  2: '到厂质检',
  3: '到仓质检',
  4: '他人质检'
}

export const mapQcStage = {
  1: '生产中',
  2: '生产完成',
  3: '包装后'
}

export const mapQcDoubleCheck = {
  0: '否',
  1: '是'
}

const statusOptions = [
  {
    label: '未质检',
    value: 0
  },
  {
    label: '已质检',
    value: 1
  }
]

const status_schema = GET_STATUS_SCHEMA(statusOptions, {
  field: 'qc_status',
  defaultValue: 0
})

export const searchFormSchema: FormSchema[] = [
  status_schema,
  {
    field: 'strid',
    label: '任务号',
    component: 'Input'
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: 'status',
    label: '销售订单状态',
    component: 'Select',
    defaultValue: [3, 4, 5],
    componentProps: {
      mode: 'multiple',
      allowClear: true,
      optionFilterProp: 'label',
      options: saleStore.mapOrderStatusOptions.filter((item) => [3, 4, 5, 15].includes(item.value))
    }
  },
  // {
  //   label: '质检状态',
  //   field: 'qc_status',
  //   defaultValue: 0,
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {
  //         label: '未质检',
  //         value: 0
  //       },
  //       {
  //         label: '已质检',
  //         value: 1
  //       }
  //     ]
  //   }
  // },
  {
    label: '是否逾期',
    field: 'is_overdue',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    }
  },
  {
    label: '交付日期',
    field: 'deliver_at',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label: '出库日期',
    field: 'out_at',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    label: '方案经理',
    field: 'program_incharge',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        multiple: true,
        showCheckedStrategy: 'TreeSelect.SHOW_ALL',
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'SingleRangeDate',
    // 默认值是今年的一月一日到今天
    // defaultValue: [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD 23:59:59')],
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

export const columns: BasicColumn[] = [
  // {
  //   dataIndex: 'name',
  //   title: '名称',
  //   width: 200,
  //   resizable: true
  // },
  {
    dataIndex: 'source_uniqid',
    title: '销售订单号',
    helpMessage: ['点击销售订单号', '可查看销售单下的所有的质检单'],
    width: 200,
    resizable: true
  },
  {
    dataIndex: 'stock_at',
    title: '可备货日期',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'est_finished_at',
    title: '结束日期',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'audit_at',
    title: '结算日期',
    width: 150,
    resizable: true
  },
  {
    title: '销售订单状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      // return h(Tag, { color: saleStore.statusColor[record.status] }, () => saleStore.saleStatus[record.status])
      return useRender.renderTag(saleStore.saleStatus[record.status], saleStore.statusColor[record.status])
    }
  },
  {
    dataIndex: 'program_incharge_name',
    title: '方案经理',
    resizable: true,
    width: 150
  },
  {
    dataIndex: 'qc_status',
    title: '质检状态',
    resizable: true,
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) ? useRender.renderTag(t(`tag.colorTag.${text}.label`), t(`tag.colorTag.${text}.color`)) : '-'
  },
  {
    dataIndex: 'qc_rate',
    title: '质检率',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'deliver_at',
    title: '交付日期',
    resizable: true,
    width: 150
  },
  // {
  //   dataIndex: 'code',
  //   title: '编码',
  //   width: 200,
  //   resizable: true
  // },
  // {
  //   dataIndex: 'item_request_count',
  //   title: '总商品数',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   dataIndex: 'qc_item_count',
  //   title: '质检商品数',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   dataIndex: 'qc_rate',
  //   title: '质检率',
  //   width: 150,
  //   resizable: true,
  //   customRender: ({ text }) => `${text}%`
  // },
  {
    dataIndex: 'qc_status_at',
    title: '质检完成日期',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    dataIndex: 'is_overdue',
    title: '是否逾期',
    resizable: true,
    width: 150,
    customRender: ({ record }) => {
      if (record.deliver_at && record.qc_status_at) {
        const deliverAt = +new Date(record.deliver_at)
        const qcStatusAt = +new Date(record.qc_status_at)
        // 判断是否逾期
        const isOverdue = qcStatusAt > deliverAt
        return useRender.renderTag(isOverdue ? '是' : '否', isOverdue ? '#ff0000' : '#55D187')
      }
      return ''
    }
  },
  {
    dataIndex: 'department_name',
    title: '部门',
    resizable: true,
    width: 200
    // customRender: ({ record }) => commonMap.getMapDept[record.dept_id]
  },
  {
    dataIndex: 'qc_commission',
    title: '质检提成金额',
    width: 200,
    resizable: true,
    ifShow: hasPermission([444])
  },
  {
    dataIndex: 'receivable',
    title: '销售单总应收金额',
    width: 200,
    resizable: true,
    ifShow: hasPermission([445])
  }
]

export const childrenColumns: BasicColumn[] = [
  {
    dataIndex: 'id',
    title: 'ID',
    resizable: true,
    width: 100
  },
  // {
  //   dataIndex: 'product',
  //   title: '质检商品',
  //   resizable: true,
  //   width: 150,
  //   ellipsis: false
  // },
  {
    dataIndex: 'qc_type_id',
    title: '质检方式',
    resizable: true,
    width: 150,
    customRender: ({ record }) => mapQcType[record.qc_type_id]
  },
  {
    dataIndex: 'content',
    title: '质检内容',
    resizable: true,
    width: 150,
    customRender: ({ record }) => record.content.join('、')
  },
  {
    dataIndex: 'qc_stage_id',
    title: '质检时期',
    resizable: true,
    width: 150,
    customRender: ({ record }) => mapQcStage[record.qc_stage_id]
  },
  {
    dataIndex: 'double_check',
    title: '是否多次质检',
    resizable: true,
    width: 100,
    customRender: ({ record }) => mapQcDoubleCheck[record.double_check]
  },
  {
    dataIndex: 'result',
    title: '质检结果',
    resizable: true,
    width: 250
  },
  {
    dataIndex: 'images',
    title: '图片',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'user_id',
    title: '质检人',
    resizable: true,
    width: 200,
    customRender: ({ record }) => commonMap.getMapPerson[record.user_id]
  },
  {
    dataIndex: 'created_at',
    title: '创建时间',
    resizable: true,
    width: 200
  }
]
