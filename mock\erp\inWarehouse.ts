import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
// import Mock from 'mockjs'

// 随机生成部门
function randomDepartment() {
  const departments = ['敬城石材产品部', '技术支持部', '市场营销部', '人力资源部', '财务部']
  const index = Math.floor(Math.random() * departments.length)
  return departments[index]
}

const inWarehouseList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    const currentDate = '@date'
    result.push({
      id: '@id',
      date: currentDate,
      'order_info|2-5': [
        {
          date: currentDate,
          receipt_no: /[a-z]{2}\d{4}/,
          department: randomDepartment(),
          'images|0-5': ['@image("200x200")'],
          order_no: /[A-Z]\d{4}-\d{8}/,
          rece_page_no: '@integer(1, 10)',
          'Actual_number|1-10': 2,
          instruction: '@cparagraph',
          plate: /[A-Z]{1}[a-z]{1}\d{5}/,
          ERPInWarehouse_no: /\d{9}/,
          remark: '@cparagraph',
          'status|0-1': 0
        }
      ]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/warehouse/inWarehouseList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(inWarehouseList)
    }
  }
] as MockMethod[]
