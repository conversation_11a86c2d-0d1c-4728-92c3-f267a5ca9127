import { DescItem } from '/@/components/Description'

export const schema: DescItem[] = [
  // 订单基本信息
  {
    field: 'order_number',
    label: '订单编号'
  },
  {
    field: 'opening_at',
    label: '营业日期'
  },
  {
    field: 'status_name',
    label: '订单状态名称'
  },
  {
    field: 'type_name',
    label: '订单分类'
  },
  // 菜品信息
  {
    field: 'name',
    label: '菜品名称'
  },
  {
    field: 'code',
    label: '菜品编码'
  },
  {
    field: 'unit',
    label: '单位'
  },
  {
    field: 'size',
    label: '规格'
  },
  // 分类信息
  {
    field: 'big_category',
    label: '菜品大类'
  },
  {
    field: 'small_category',
    label: '菜品小类'
  },
  {
    field: 'big_category_id',
    label: '菜品大类 ID'
  },
  {
    field: 'small_category_id',
    label: '菜品小类 ID'
  },
  // 金额信息
  {
    field: 'unit_price',
    label: '菜品单价(元)'
  },
  {
    field: 'quantity',
    label: '销售数量'
  },
  {
    field: 'sales_amount',
    label: '销售金额(元)'
  },
  {
    field: 'sales_discounts',
    label: '菜品优惠金额(元)'
  },
  // 订单金额
  {
    field: 'sales_income',
    label: '菜品收入金额(元)'
  },
  {
    field: 'amount',
    label: '订单总金额(元)'
  },
  {
    field: 'discounts',
    label: '订单优惠金额(元)'
  },
  {
    field: 'income',
    label: '订单收入金额(元)'
  },
  // 时间信息
  {
    field: 'dot_at',
    label: '点菜时间'
  },
  {
    field: 'order_at',
    label: '下单时间'
  },
  {
    field: 'checkout_at',
    label: '结账时间'
  },
  {
    field: 'cashier_name',
    label: '收银员'
  },
  // 位置信息
  {
    field: 'desk_area',
    label: '餐台区域'
  },
  {
    field: 'desk_number',
    label: '桌号'
  },
  {
    field: 'take_number',
    label: '取餐号'
  },
  {
    field: 'out_department',
    label: '出品部门名称'
  },
  // 其他信息
  {
    field: 'business_model',
    label: '经营模式'
  },
  {
    field: 'source',
    label: '菜品下单来源'
  },
  {
    field: 'sub_source',
    label: '菜品订单子来源'
  },
  {
    field: 'food',
    label: '关联菜品名称'
  },
  {
    field: 'label',
    label: '标记'
  }
]
