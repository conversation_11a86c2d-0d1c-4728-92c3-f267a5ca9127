<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="采购订单-新建付款" useWrapper defaultFullscreen>
    <template #footer>
      <Button @click="handleModalCancel">取消</Button>
      <Button type="primary" @click="handleOk" :disabled="updateStatus">确定</Button>
    </template>
    <Row :gutter="[8, 8]">
      <Col span="12">
        <Card title="采购订单" style="height: 90%">
          <template #extra>
            <div class="mb-2">
              是否付款调拨：
              <Switch v-model:checked="allotStatus" checked-children="是" un-checked-children="否" />
            </div>
          </template>
          <ScrollContainer style="width: 100%">
            <template v-for="(item, index) in clodeepRecordData" :key="item.strid">
              <div
                :style="{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '10px',
                  border: activeIndex === index ? '1px solid skyblue' : ''
                }"
                @click="handleClick(item.work_id, index)"
              >
                <div style="color: black; width: 50%">
                  <div>单号: {{ item.strid }}</div>
                  <div v-if="!warehousedisa">总采购商品数: {{ item.qty_purchased_actual }}</div>
                  <div v-if="!warehousedisa">总质检商品数: {{ item.qr_num_actual }}</div>
                </div>

                <Form :model="item" ref="formRef" style="width: 70%" layout="vertical">
                  <Row :gutter="[8, 8]" v-if="!['人民币', 'CNY'].includes(item.currency)">
                    <Col span="12">
                      <FormItem has-feedback name="foreign_currency_amount" :rules="formRulesFn(index)">
                        <template #label>
                          <div style="color: #0960bd"> 请输入本次应付金额(外币):</div>
                        </template>
                        <InputNumber
                          v-model:value="item.foreign_currency_amount"
                          min="0"
                          autocomplete="off"
                          :precision="2"
                          @change="
                            (value) => {
                              item.no_amount = new Decimal(value).mul(item.exchange_rate).toDecimalPlaces(2).toNumber()
                            }
                          "
                          placeholder="请输入本次应付金额"
                        />
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem has-feedback name="no_amount" :rules="formRulesFn(index)">
                        <template #label>
                          <div style="color: #0960bd"> 请输入本次应付金额(人民币):</div>
                        </template>
                        <InputNumber
                          v-model:value="item.no_amount"
                          min="0"
                          :disabled="true"
                          autocomplete="off"
                          :precision="2"
                          placeholder="请输入本次应付金额"
                        /> </FormItem
                    ></Col>
                  </Row>
                  <FormItem has-feedback name="no_amount" :rules="formRulesFn(index)" v-else>
                    <template #label>
                      <div style="color: #0960bd"> 请输入本次应付金额(人民币):</div>
                    </template>
                    <InputNumber
                      v-model:value="item.no_amount"
                      min="0"
                      autocomplete="off"
                      :precision="2"
                      placeholder="请输入本次应付金额"
                    />
                  </FormItem>
                </Form>
              </div>
              <!-- </DescriptionsItem> -->
              <!-- </Descriptions> -->
            </template>
          </ScrollContainer>
        </Card>
        <div class="leftPrice"
          >本次应付总金额:
          <text style="color: #0960bd; margin-left: 10px"> ￥{{ computedTotalAmount }}</text>
        </div>
      </Col>
      <Col span="12">
        <Card title="付款单填写信息" style="padding-bottom: 10px">
          <BasicForm @register="registerFrom">
            <template #Files>
              <Upload
                v-model:file-list="filesList"
                action="/api/oss/putImg2Stocking"
                :custom-request="handleFileRequest"
                :multiple="true"
                :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
              >
                <a-button type="primary">
                  <upload-outlined />
                  Upload
                </a-button>
              </Upload>
            </template>
            <template #Account="{ model }">
              <Select
                v-model:value="model.account"
                :options="supplierData?.item"
                :field-names="{ label: 'account', value: 'account' }"
                @change="
                  (_, shall:any) => {
                    model.account_name = shall.account_name
                    model.bank = shall.bank
                    model.is_public_account = shall.is_public_account
                  }
                "
              />
            </template>
          </BasicForm>
        </Card>
        <Card title="采购订单金额追加记录" style="height: 300px">
          <template #extra>
            <Tooltip>
              <template #title>点击查询采购订单关联的其他支出单</template>
              <Button type="primary" @click="changePurchse" size="small">查询</Button>
            </Tooltip>
          </template>
          <BasicTable @register="registerTabel" />
        </Card>
      </Col>
    </Row>
    <Card title="供应商信息">
      <template #extra>
        <span v-if="updateStatus">
          <Button type="primary" @click="handleSave" size="small" class="mr-2">保存</Button>
          <Button @click="handleCancel" size="small">取消</Button>
        </span>
        <span v-else>
          <Button type="primary" @click="handleUpdateSupplier" size="small">编辑</Button>
        </span>
      </template>
      <BasicForm @register="registerSupplierFrom">
        <template #businessicense>
          <Upload
            v-model:file-list="businessicense"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handlebusinessicenseFileRequest"
            :multiple="true"
            :disabled="!updateStatus"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #contract>
          <Upload
            v-model:file-list="contracts"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handlecontractsFileRequest"
            :multiple="true"
            :disabled="!updateStatus"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #names="{ model }">
          <Input v-if="!model.is_company" v-model:value="model.name" :disabled="!updateStatus" />
          <div v-else>
            <InputSearch
              v-model:value="model.name"
              enter-button="搜索"
              :loading="searchlodaing"
              @search="search"
              :disabled="!updateStatus"
            />
            <div class="serchdiv" v-if="qccarray.length > 0 && qccshow">
              <ul ref="list" class="serchul">
                <li class="serchli" v-for="item in qccarray" :key="item.name" @click="select(item)">{{ item.Name }}</li>
              </ul>
            </div>
          </div>
        </template>
      </BasicForm>
      <BasicTable @register="registersuppliertable">
        <template #toolbar>
          <Button type="primary" @click="handleAdd" :disabled="!updateStatus">新增</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </Card>
  </BasicModal>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  InputNumber,
  message,
  Row,
  Switch,
  Tooltip,
  UploadFile,
  Upload,
  Select,
  InputSearch,
  Input
} from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { addBatch } from '/@/api/financialDocuments/paymentOrder'
import { ScrollContainer } from '/@/components/Container'
import { getSubWorkPurchase } from '/@/api/erp/purchaseOrder'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { columns } from '../datas/modal'
import { BasicForm, useForm } from '/@/components/Form'
import { getSupplier, updateSupplier } from '/@/api/baseData/supplier'
import { schemas, schemasFn } from '/@/views/baseData/supplier/datas/SupplierModal'
import { sub, add } from '/@/utils/math'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadOutlined } from '@ant-design/icons-vue'
import { suppliercolumns } from '/@/views/baseData/supplier/datas/SupplierModal'
import Decimal from 'decimal.js'

const emit = defineEmits(['register', 'success'])

//附件
const filesList = ref<UploadFile[]>([])

/** 订单号对应的其他支出单数组 */
const otherExpendData = ref<Array<any>>([])

const clodeepRecordData = ref<Array<any>>([])
const recordData = ref<any>()
const formRef = ref()
const supplierId = ref<number>()
const updateStatus = ref<boolean>(false)
const supplierData = ref()
const allotStatus = ref<boolean>(false)
const activeIndex = ref()
const PurchaseWork_id = ref()

const propsData = ref()
//主入库单显示
const warehousedisa = ref(false)
//gubilder驳回后是否一键生成付款单
const isgublider = ref(false)

//营业执照
const businessicense = ref<UploadFile[]>([])
//合同
const contracts = ref<UploadFile[]>([])

const searchlodaing = ref(false)
const qccarray = ref<any>([])

//保存点击,其他禁用
const currentEditKeyRef = ref('')

/** 打开弹出时会触发 */
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  try {
    changeLoading(true)
    resetFields()
    setDisable(true)
    resetFromFields()
    updateSchema(schemasFn(true, 'purchase', { updateSchema, getFieldsValue, clearValidate }))
    await updateFromSchema(await schemas(data.record[0].is_ticket, data.record[0].ticket))
    setFormFieldsValue({
      contracting_party: data.record[0].contracting_party
    })
    propsData.value = data
    activeIndex.value = null
    PurchaseWork_id.value = null
    updateStatus.value = false
    allotStatus.value = false
    clodeepRecordData.value = []
    otherExpendData.value = []
    filesList.value = []
    supplierId.value = data.supplierId
    warehousedisa.value = data.hasOwnProperty('type')
    isgublider.value = data.hasOwnProperty('is_gublider')

    // 获取供应商信息
    let { items } = (await getSupplier({ id: data.supplierId })) as any
    supplierData.value = items[0]
    const imgsdata = typeof items[0].business_license === 'string' ? JSON.parse(items[0].business_license) : items[0].business_license
    const imgsdata2 = typeof items[0].contract === 'string' ? JSON.parse(items[0].contract) : items[0].contract
    businessicense.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    console.log(businessicense.value)

    contracts.value = (
      typeof imgsdata2 === 'string'
        ? [{ url: imgsdata2, uid: +new Date().toString(), name: imgsdata2 }]
        : (imgsdata2 as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    setFieldsValue(items[0])
    setTableData(items[0].item)
    recordData.value = data
    if (!data.hasOwnProperty('type')) {
      clodeepRecordData.value = cloneDeep(data.record).map((item: any) => {
        item.no_amount = data.form === 'Storage' ? item.in_no_pay_amount : sub(item.work.cost_left, item.work.paid_actual, 2)
        return item
      })
    } else {
      clodeepRecordData.value = cloneDeep(data.record)
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFormFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
/** 注册其他支出单表格 */
const [registerTabel, { setLoading }] = useTable({
  columns,
  dataSource: otherExpendData,
  maxHeight: 400
})
/** 注册供应商卡号明细表格 */
const [registersuppliertable, { updateTableDataRecord, getColumns, getDataSource, setTableData, deleteTableDataRecord }] = useTable({
  columns: suppliercolumns,
  maxHeight: 400,
  title: '供应商分属账户',
  showIndexColumn: false,
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action'
  }
})

/** 注册供应商表单 */
const [registerSupplierFrom, { validate: supplierFromValidate, resetFields, setFieldsValue, updateSchema, getFieldsValue, clearValidate }] =
  useForm({
    labelWidth: 120,
    schemas: schemasFn(true, 'purchase'),
    showSubmitButton: false,
    showResetButton: false
    // baseColProps: { span: 24 }
  })
//
const [registerFrom, { updateSchema: updateFromSchema, validate, resetFields: resetFromFields, setFieldsValue: setFormFieldsValue }] =
  useForm({
    labelWidth: 80,
    schemas: schemas(),
    showSubmitButton: false,
    showResetButton: false,
    baseColProps: { span: 12 },
    labelCol: { span: 10 }
  })
/** 计算总金额 */
const computedTotalAmount = computed(() => {
  let totalAmount = clodeepRecordData.value.reduce((pre, cur) => {
    return add(pre, Number(cur.no_amount) || 0, 2)
  }, 0)
  return totalAmount
})

function handleClick(work_id, index) {
  console.log(index)

  activeIndex.value = index
  PurchaseWork_id.value = work_id
}

/** 点击查询采购单关联的的其他支出单 */
async function changePurchse() {
  if (!PurchaseWork_id.value) {
    message.error('请先选择左侧采购单')
    return
  }
  setLoading(true)
  const { items } = await getSubWorkPurchase({ work_id: PurchaseWork_id.value })
  otherExpendData.value = items
  setLoading(false)
}

/** 编辑供应商 */
async function handleUpdateSupplier() {
  updateStatus.value = true
  setDisable(false)
}

/** 保存 */
async function handleSave() {
  try {
    // 供应商信息校验
    let supplier = await supplierFromValidate()
    const tabledata = formatSubmit()
    supplier.inCharge = supplier.inCharge ? supplier.inCharge : null
    await updateSupplier({
      id: supplierId.value,
      ...supplier,
      Invoicing_is_self: supplier.Invoicing_is_self ?? undefined,
      tax_point: supplier.tax_point ?? undefined,
      ticket_point: supplier.ticket_point ?? undefined,
      items: tabledata
    })
    setDisable(true)
    updateStatus.value = false
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
    throw new Error(`${error}`)
  }
}

/** 取消 */
async function handleCancel() {
  updateStatus.value = false
  // 重置供应商信息
  await setFieldsValue(supplierData.value)
  setDisable(true)
}

/** 设置disable */
function setDisable(status: boolean) {
  updateSchema(schemasFn(status, 'purchase', { updateSchema, getFieldsValue, clearValidate }))
}

/** 校验 */
const formRulesFn = (): any => {
  return [
    {
      trigger: 'change',
      validator: (_, value) => {
        if (Number(value) <= 0) {
          return Promise.reject('输入的付款金额必须大于0')
        } else {
          return Promise.resolve()
        }
      }
    }
  ]
}

/** 弹窗点击取消 */
const handleModalCancel = () => {
  closeModal()
}

/** 点击确定 */
const handleOk = async () => {
  try {
    await changeLoading(true)
    await changeOkLoading(true)
    const {
      is_ticket,
      ticket,
      tax_point,
      Invoicing_is_self,
      payment_type,
      remark,
      files,
      contracting_party,
      account,
      account_name,
      is_public_account,
      bank,
      rate
    } = await validate()
    console.log(account, account_name)

    const parameter = {
      works: [] as any[],
      files,
      is_allot: 0,
      dept_id: propsData.value.dept_id,
      g_remark: remark,
      clause: 2,
      payment_type: payment_type,
      is_ticket,
      ticket,
      tax_point,
      Invoicing_is_self,
      contracting_party,
      account,
      account_name,
      is_public_account,
      bank,
      rate
    }

    // 校验
    for (let item of formRef.value) {
      await item.validate()
    }

    for (let item of clodeepRecordData.value) {
      if (payment_type !== 1 && !warehousedisa.value) {
        if (
          item.is_quality_approved == 1 ||
          item.no_amount <= 3000 ||
          (item.is_quality_approved !== 1 && item.no_amount > 3000 && Number(item.qr_num_actual) >= Number(item.qty_purchased_actual))
        ) {
          parameter.works.push({
            work_id: item.work_id,
            amount: item.no_amount,
            supplier_id: item.supplier_id,
            foreign_currency_amount: ['人民币', 'CNY'].includes(item.currency) ? undefined : item.foreign_currency_amount
          })
        } else {
          throw new Error('不能生成付款单,请先完成质检')
        }
      } else {
        parameter.works.push({
          work_id: item.work_id,
          amount: item.no_amount,
          supplier_id: item.supplier_id,
          doc_in_warehouse_header_id: item.doc_in_warehouse_header_id,
          foreign_currency_amount: ['人民币', 'CNY'].includes(item.currency) ? undefined : item.foreign_currency_amount
        })
      }
    }
    parameter.is_allot = allotStatus.value ? 1 : 0
    parameter.is_purchase_submit = isgublider.value ? 1 : 0
    console.log(parameter)

    await closeModal()
    await addBatch({ ...parameter })
    changeLoading(false)
    changeOkLoading(false)
    emit('success')
    message.success('成功生成付款单!')
  } catch (error) {
    message.error(error?.message ?? '生成付款单失败')
    changeLoading(false)
    changeOkLoading(false)
    throw new Error(error)
  }
}

//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

//上传
async function handlebusinessicenseFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  businessicense.value = businessicense.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: businessicense.value.map((item) => item.url)
  })
}
//附件上传
async function handlecontractsFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  contracts.value = contracts.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: contracts.value.map((item) => item.url)
  })
}

watch(
  () => businessicense.value,
  async (val) => {
    await setFieldsValue({ business_license: val?.map((item) => item.url) })
  }
)
watch(
  () => contracts.value,
  async (val) => {
    await setFieldsValue({ contract: val?.map((item) => item.url) })
  }
)

function search(e) {
  qccarray.value = []
  qccshow.value = true
  searchlodaing.value = true
  setTimeout(async () => {
    const { Result } = await smqccsearchKey({ searchKey: e })
    qccarray.value = Result
    searchlodaing.value = false
  }, 2000)
}

function select(item) {
  setFieldsValue({
    name: item.Name,
    credit_code: item.CreditCode,
    oper_name: item.OperName,
    company_status: item.Status,
    address: item.Address
  })
  qccshow.value = false
}

//添加明细
async function handleAdd() {
  const newRowDataItem = {}
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handlesupllierSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handlesuplierCancel.bind(null, record)
      }
    }
  ]
}

// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

function handlecopylink(record) {
  const newrecord = formatObject(record) as any
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//保存
async function handlesupllierSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性
      const Columnskey = formatObject(record)
      for (const key in Columnskey) {
        // 如果不是business_type且值为空（或undefined）
        if (Columnskey[key] === '' || Columnskey[key] === undefined) {
          message.error('全为必填,请检查是否完成填写')
          return
        }
      }
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//取消
function handlesuplierCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}
</script>

<style scoped lang="less">
.leftPrice {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20px;
  border: 1px solid #e8e8e8;
}
</style>
