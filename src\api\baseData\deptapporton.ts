import { defHttp } from '/@/utils/http/axios'

enum Api {
  deptappronList = '/erp/ds/getList',
  deptappronAdd = '/erp/ds/update'
}
export const deptappronList = (params: Recordable) => defHttp.get({ url: Api.deptappronList, params })
export const deptappronAdd = (params: Recordable) =>
  defHttp.post({ url: Api.deptappronAdd, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
