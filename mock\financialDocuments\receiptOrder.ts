import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
const receiptOrderList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      strid: '@guid',
      created_at: '@date',
      creator: '财务',
      'fund_id|1': ['0', '1', '2'],
      'processor|1': ['张三', '里斯', '王五'],
      'status|1': [0, 1],
      type: '@ctitle'
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/receiptOrder',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(receiptOrderList)
    }
  }
] as MockMethod[]
