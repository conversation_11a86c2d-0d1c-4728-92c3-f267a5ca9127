import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const purchaseOrderList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      id: '@id',
      date: '@date',
      'strid|1': ['@guid', ''],
      'status|1': [1, 2],
      dept: {
        dept_id: '@id',
        dept_name: '测试部门'
      },
      sales_order_no: '@guid',
      supplier: {
        supplier_id: '@id',
        supplier_name: '@ctitle'
      },
      purchase_img: '@image(100)',
      'purchase_file|2': [
        {
          id: '@id',
          name: '@cname',
          url: '@url'
        }
      ],
      purchase_factory: '@cname'
      // client_code: '@guid',
      // guide_person: {
      //   guide_person_id: '@id',
      //   guide_person_name: '测试导购员'
      // },
      // sales_person: {
      //   sales_person_id: '@id',
      //   sales_person_name: '测试业务员'
      // },
      // channel: {
      //   channel_id: '@id',
      //   'channel_name|1': ['线上', '线下']
      // },
      // 'total_sale|10-1000': 1000,
      // 'commissions|10-1000': 1000,
      // delivery_date: '@date'
    })
  }
  return result
})()

const detail = {
  id: '@id',
  date: '@date',
  order_no: '@guid',
  'status|1': [1, 2],
  dept_id: 2,
  dept_name: '测试部门',
  sales_order_no: '@guid',
  supplier_id: '@id',
  supplier_name: '@ctitle',
  'purchase_img|4': [
    {
      id: '@id',
      name: '@cname',
      url: '@url'
    }
  ],
  'purchase_file|2': [
    {
      id: '@id',
      name: '@cname',
      url: '@url'
    }
  ],
  purchase_factory: '@cname',
  'relate_pay_order|5': [
    {
      id: '@id',
      order_name: '@ctitle',
      order_no: '@guid',
      'product_list|5': [
        {
          id: '@id',
          type: 1,
          work_id: 100,
          batch_code: '@guid',
          name: '@ctitle',
          uniqid: '12303548674145',
          parent_uniqid: '0',
          imgs: [
            'https: //img.gbuilderchina.com/gb/pdms/20230401/160143/sub_803_1.jpg',
            'https://img.gbuilderchina.com/gb/pdms/20230401/160143/sub_803_1.jpg'
          ],
          puid: '************',
          unit: '件',
          unit_price: 100,
          qty_request: 100,
          qty_request_left: 101,
          total_amount: 10001,
          desc: '测试',
          remark: '测试',
          updated_at: '2023-09-13 03:19:19',
          created_at: '2023-09-13 03:19:19'
        }
      ]
    }
  ]
}

const payOrderList = (function () {
  const result: any[] = []
  for (let i = 0; i < 15; i++) {
    result.push({
      id: '@id',
      order_no: '@guid',
      order_name: '@ctitle'
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/purchase/get',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(purchaseOrderList)
    }
  },
  {
    url: '/upload/erp/purchase/upload',
    timeout: 1000,
    method: 'post',
    response: () => {
      return resultSuccess({ data: { url: '@url' } })
    }
  },
  {
    url: '/api/erp/purchase/getpayorder',
    timeout: 1000,
    method: 'get',
    response: () => resultSuccess(payOrderList)
  },
  {
    url: '/api/erp/purchase/detail',
    timeout: 1000,
    method: 'get',
    response: () => resultSuccess(detail)
  }
] as MockMethod[]
