import type { BasicColumn } from '/@/components/Table/src/types/table'

import { h, Ref, toRaw } from 'vue'

import EditableCell from './EditableCell.vue'
import { isArray } from '/@/utils/is'

interface Params {
  text: string
  record: Recordable
  index: number
}

export function renderEditCell(column: BasicColumn) {
  return ({ text: value, record, index }: Params) => {
    toRaw(record).onValid = async () => {
      if (isArray(record?.validCbs)) {
        const validFns = (record?.validCbs || []).map((fn) => fn())
        const res = await Promise.all(validFns)
        return res.every((item) => !!item)
      } else {
        return false
      }
    }

    toRaw(record).onEdit = async (edit: boolean, submit = false) => {
      if (!submit) {
        record.editable = edit
      }

      if (!edit && submit) {
        if (!(await record.onValid())) return false
        const res = await record.onSubmitEdit?.()
        if (res) {
          record.editable = false
          return true
        }
        return false
      }
      // cancel
      if (!edit && !submit) {
        record.onCancelEdit?.()
      }
      return true
    }

    // let editRenderVal
    // if (column.editRender) {
    //   editRenderVal = column.editRender({ text: value, record, column, index, currentValue: value })
    // }

    return h(EditableCell, {
      // value: 以前是只会获取列表中获取的值，自定义编辑框内容是无法设置为默认值，只会是空字符串，现在要先判断editRender属性
      // 设置了editRender属性，就使用column.editRender返回值做默认值
      // value: column.editRender ? column.editRender({ text: value, record, column, index, currentValue: value }) ?? value : value,
      // value: value ?? editRenderVal, // 这里是可以，但是不知道value是null会不会又触发无限递归
      value,
      record,
      column,
      index
    })
  }
}

export type EditRecordRow<T = Recordable> = Partial<
  {
    onEdit: (editable: boolean, submit?: boolean) => Promise<boolean>
    onValid: () => Promise<boolean>
    editable: boolean
    onCancel: Fn
    onSubmit: Fn
    submitCbs: Fn[]
    cancelCbs: Fn[]
    validCbs: Fn[]
    editValueRefs: Recordable<Ref>
  } & T
>
