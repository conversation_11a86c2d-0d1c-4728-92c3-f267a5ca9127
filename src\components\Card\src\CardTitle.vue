<script lang="ts" setup name="CardTitle">
defineProps({
  title: {
    type: String,
    required: true
  }
})
</script>

<template>
  <div class="card-title">{{ title }}</div>
</template>

<style scoped lang="less">
.card-title {
  font-size: 14px;
  font-weight: 600;
  padding-left: 5px;
  &:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 14px;
    background-color: @primary-color;
    // background: red;
    position: relative;
    left: -5px;
    top: 8px;
    border-radius: 5px;
    transform: translateY(-50%);
  }
}
</style>
