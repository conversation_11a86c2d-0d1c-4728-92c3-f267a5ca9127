#!/bin/sh

protected_branch="master"

while read oldoid newoid ref ref_sha
do
    if echo "$newoid" | grep -qsE '^0+$'
    then
        # deletion.
        if [ "refs/heads/$protected_branch" = "$ref" ]
        then
            echo "不允许对master进行删除操作！"
            exit 1
        fi
        continue
    elif echo "$oldoid" | grep -qsE '^0+$'
    then
        # creation
        # 当前分支
        branch=$(git rev-parse --abbrev-ref HEAD)
        # 远程分支，此处可根据分支名进行截取
        remote_ref=${ref}

        if [ "refs/heads/${branch}" != "${remote_ref}" ]
        then
            echo "
              当前分支${branch}和要推送到目标分支${remote_ref}，分支名不一致
            "
            exit 1
        fi
        continue
    else
        # update
        # 当前分支
        branch=$(git rev-parse --abbrev-ref HEAD)
        # 远程分支，此处可根据分支名进行截取
        remote_ref=${ref}

        if [ "refs/heads/${branch}" != "${remote_ref}" ]
        then
            echo "
              当前分支${branch}和要推送到目标分支${remote_ref}，分支名不一致
            "
            exit 1
        fi
        continue
    fi
done
