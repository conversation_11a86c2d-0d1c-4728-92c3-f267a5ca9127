import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetRealTimeInventoryList = '/erp/wms/inventory/getList',
  projectwkgetOrderList = '/erp/project/wk/getOrderList',
  projectwkgetBillOrderList = '/erp/project/wk/getBillOrderList'
}

export const getRealTimeInventoryList = (params?: {}) => defHttp.get({ url: Api.GetRealTimeInventoryList, params })
export const projectwkgetOrderList = (params?: {}) => defHttp.get({ url: Api.projectwkgetOrderList, params })
export const projectwkgetBillOrderList = (params?: {}) => defHttp.get({ url: Api.projectwkgetBillOrderList, params })
