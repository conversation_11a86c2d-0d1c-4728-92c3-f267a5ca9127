import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { personnel, department } from './modle/types'
import { isArray } from 'lodash-es'

enum Api {
  GetCreatorList = '/erp/ad/get',
  GetdepartmentList = '/department/getSelectTree',
  GetFundList = '/erp/finance/cw/getFundList',
  GetReceiptOrderList = '/erp/finance/wrc/getReceiptList',
  GetPaymentList = '/erp/finance/wpc/getPaymentList',
  LockTable = '/erp/finance/rc/dflock',
  GetClientList = '/erp/si/getCustomer'
}

// 获取系统用户列表
export const getCreatorList = (params?) => defHttp.get<BasicFetchResult<personnel>>({ url: Api.GetCreatorList, params })

// 获取客户
export const getClientList = () => defHttp.get({ url: Api.GetClientList })

// 获取部门
export const getdepartmentList = (params?: {}) =>
  defHttp.get<BasicFetchResult<department>>({
    url: Api.GetdepartmentList,
    params,
    transformResponse: (data) => {
      const newData = JSON.parse(data)
      if (isArray(newData.result)) {
        return {
          ...newData,
          result: newData.result.map((item) => ({ ...item, name: item.deptName }))
        }
      }
      return JSON.parse(newData)
    }
  })

// 获取流水下拉列表，可以通过流水单号查到该流水全部信息
export const getFundList = (params?: {}) => defHttp.get<BasicFetchResult<any>>({ url: Api.GetFundList, params })

// 获取收款下拉列表
export const getReceiptOrderList = (params?: {}) => defHttp.get({ url: Api.GetReceiptOrderList, params })

// 获取付款下拉列表
export const getPaymentList = (params?: {}) => defHttp.get({ url: Api.GetPaymentList, params })

// 锁表
export const lockTable = (params: { id: number }) => defHttp.get({ url: Api.LockTable, params })
