<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="60%" @ok="handleSubmit" :minHeight="540" destroyOnClose>
    <BasicForm @register="registerForm" @field-value-change="handvaluechange" />
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button @click="handleSubmit('sole')" type="primary">确认</a-button>
      <a-button @click="handleSubmit('next')" type="primary" v-if="!propsData.isUpdate">确认并填写下一条</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'

import { getSchemas } from '../datas/modal'
import { message } from 'ant-design-vue'

const emit = defineEmits(['add-success', 'update-success', 'register'])
//部门数据
const handdeptList = ref<any>([])
//销售订单
const salesList = ref<any>([])

const propsData = ref<{ isUpdate: boolean; record?: Recordable }>({ isUpdate: false })
const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, getFieldsValue }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 7 },
  schemas: getSchemas(),
  colon: true
})

const [registerModal, { closeModal, changeOkLoading, changeLoading }] = useModalInner(async (data) => {
  try {
    changeLoading(true)
    await resetFields()
    propsData.value = data
    handdeptList.value = []
    await updateSchema(getSchemas({ setFieldsValue, updateSchema, getFieldsValue }, handList))

    if (propsData.value?.isUpdate) {
      setFieldsValue(propsData.value.record)
    } else {
      setFieldsValue({ type: '0' })
    }
  } catch (err) {
    console.log(err)
  } finally {
    changeLoading(false)
  }
})

async function handleSubmit(type: 'sole' | 'next') {
  changeOkLoading(true)
  try {
    await validate()
    const formData = await getFieldsValue()
    const { isUpdate } = propsData.value
    if (formData.corres_type && !formData.corres_pondent) {
      return message.error('请选择往来单位信息')
    }
    if (!formData.corres_type && formData.corres_pondent) {
      return message.error('请选择往来单位类型')
    }
    const params = {
      ...formData,
      key: isUpdate ? propsData.value.record?.key : Date.now()
    }

    emit(isUpdate ? 'update-success' : 'add-success', { params, deptlist: handdeptList.value, salesList: salesList.value })

    type === 'sole' && (await closeModal())
    resetFields()
    if (type === 'next') {
      setFieldsValue({ type: '0', amount0: undefined, amount1: undefined })
      updateSchema([
        {
          field: 'amount0',
          componentProps: {
            disabled: false
          }
        },
        {
          field: 'amount1',
          componentProps: {
            disabled: false
          }
        }
      ])
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
  }
}
function handList(e) {
  if (e.source_uniqid) {
    const dept = {
      id: e.dept_id,
      name: e.department_name
    }
    const clear_dept = {
      id: e.clear_dept_id,
      name: e.clear_department
    }
    handdeptList.value.push(dept, clear_dept)
  } else {
    handdeptList.value.push(e)
  }
}

//修改借贷款输入
function handvaluechange(key, value) {
  if (key == 'amount0') {
    setFieldsValue({ amount1: value > 0 ? 0 : undefined })
    updateSchema({
      field: 'amount1',
      componentProps: {
        disabled: value > 0 ? true : false
      }
    })
  } else if (key == 'amount1') {
    setFieldsValue({ amount0: value > 0 ? 0 : undefined })
    updateSchema({
      field: 'amount0',
      componentProps: {
        disabled: value > 0 ? true : false
      }
    })
  }
}
</script>
