import { h } from 'vue'
import dayjs from 'dayjs'
import { Badge, Button, Tag } from 'ant-design-vue'
import TableImg from '../components/TableImg.vue'
import { isArray, isString } from '/@/utils/is'
import { Icon } from '/@/components/Icon'
import { JsonPreview } from '/@/components/CodeEditor'

export const useRender = {
  /**
   * 渲染图片
   * @param text 图片地址
   * @returns image标签
   */
  renderImg: (text: string, otherProps?: Recordable) => {
    if (text) {
      if (isArray(text)) return h(TableImg, { imgList: text, ...otherProps })
      else if (isString(text)) return h(TableImg, { imgList: [text], ...otherProps })
    }
    return ''
  },
  /**
   * 渲染链接
   * @param url 链接地址
   * @param text 文字说明
   * @returns link 按钮
   */
  renderLink: (url: string, text?: string) => {
    if (url) return h(<PERSON><PERSON>, { type: 'link', href: url, target: '_blank' }, () => text || '')

    return ''
  },
  /**
   * 渲染文本，将text与val 拼接到一起
   * @param text 文本1
   * @param val 文本2
   * @returns 文本1 + 文本2
   */
  renderText: (text: string, val: string) => {
    if (text) return `${text} ${val}`
    else return ''
  },
  /**
   * 渲染标签
   * @param text 标签文本
   * @param color 标签颜色
   * @returns 标签
   */
  renderTag: (text: string | number, color?: string) => {
    if (color) return h(Tag, { color }, () => text)
    else return h(Tag, {}, () => text)
  },
  /**
   * 渲染多标签
   * @param texts 文本
   * @returns 多标签
   */
  renderTags: (texts: string[]) => {
    if (texts) {
      return h('div', null, [
        texts.map((text) => {
          return h(Tag, null, () => text)
        })
      ])
    }
    return ''
  },
  /**
   * 渲染日期
   * @param text 日期
   * @param format 格式化
   * @returns 格式化后日期
   */
  renderDate: (text: string, format?: string) => {
    if (!text) return ''

    if (!format) return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    else return dayjs(text).format(format)
  },

  /**
   * 渲染图标icon
   * @param text icon
   * @returns icon
   */
  renderIcon: (text: string) => {
    if (text) return h(Icon, { icon: text })
  },
  /**
   * 使用JsonPreview组件  方便预览JSON
   * @param json json字符串/obj
   * @returns 能转为json返回JsonPreview 否则返回自身
   */
  renderJsonPreview: (json: any) => {
    if (!json) return ''
    if (typeof json === 'object') return h(JsonPreview, { data: json })

    if (typeof json === 'string') {
      try {
        const data = JSON.parse(json)
        return h(JsonPreview, { data })
      } catch (e) {
        return json
      }
    }
  },
  /**
   * 渲染价格
   * 格式化价格, 保留两位小数,默认人民币
   * 未修复,有问题
   */
  // renderPrice: (price: string, currency?: string) => {
  //   const formatter = new Intl.NumberFormat('zh-CN', {
  //     style: 'currency',
  //     currency: currency ? 'CNY' : currency,
  //     currencyDisplay: 'symbol',
  //     minimumFractionDigits: 2,
  //     maximumFractionDigits: 2
  //   })
  //   if (!price) return ''
  //   return formatter
  // }
  /**
   * 渲染徽标
   * @param text 标签文本
   * @param color 标签颜色
   * @returns 标签
   */
  renderBadge: (text: string | number, color?: string, status?: 'success' | 'processing' | 'default' | 'error' | 'warning') => {
    if (color) return h(Badge, { color, text })
    if (status) return h(Badge, { status, text })
    else return h(Badge, { status: 'default', text })
  }
}
