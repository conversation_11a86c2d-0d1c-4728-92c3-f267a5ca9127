<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #form-advanceBefore>
        <Button type="primary" :loading="buttonlodaing" @click="handleExport('condition')">条件明细导出 </Button>
      </template>
      <template #toolbar>
        <Tooltip>
          <template #title>根据本页勾选数据导出</template>
          <Button type="primary" :loading="buttonlodaing" @click="handleExport('key')">明细导出 </Button>
        </Tooltip>
      </template>
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
        <template v-if="column.key === 'is_check'">
          <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_check2'">
          <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'corres_type'">
          <Tag :color="correstype[record.corres_type]?.color"> {{ correstype[record.corres_type]?.text || '-' }}</Tag>
        </template>
        <template v-if="column.key === 'is_cancel'">
          <Tag :color="cancel[record.is_cancel]?.color"> {{ cancel[record.is_cancel]?.text || '-' }}</Tag>
        </template>
        <template v-if="column.dataIndex === 'files'">
          <TableImg :size="60" :simpleShow="true" :imgList="text" />
        </template>
      </template>
      <template #footer="data">
        <div class="footer">支出金额合计(当前页)：{{ statisticsAmount(data) }}</div>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { Tag, Tooltip, Button, message } from 'ant-design-vue'
// import { usePermission } from '/@/hooks/web/usePermission'
import { getOtherExpendList } from '/@/api/financialDocuments/otherExpendApportion'
import { columns, formConfigFn, checkMap, correstype, cancel } from './datas/datas'
import { BasicTable, useTable, TableImg, EditRecordRow, ActionItem, TableAction } from '/@/components/Table'
import { postexport } from '/@/api/financialDocuments/otherExpend'
import { add } from '/@/utils/math'
import { useRoute } from 'vue-router'
import { usePermission } from '/@/hooks/web/usePermission'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { ref } from 'vue'
const { hasPermission } = usePermission()
const route = useRoute()
const { path: routePath } = route
const buttonlodaing = ref(false)

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
//初始化
const [registerTable, { getForm, getSelectRowKeys, reload, setLoading }] = useTable({
  title: '其他支出单明细列表',
  api: getOtherExpendList,
  columns,
  titleHelpMessage: '分摊模式规则：填了分摊科目就按照分摊科目进行分摊，如果没有就按照明细的科目进行分摊',
  showTableSetting: true,
  useSearchForm: true,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 150,
    actionColOptions: {
      span: 24
    },
    //自动展开行
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    autoAdvancedLine: 2,
    showAdvancedButton: false,
    baseColProps: {
      span: 8
    },
    schemas: formConfigFn() //这里还不如直接定义any类型
  },
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox'
  },
  actionColumn: hasPermission([574])
    ? {
        width: 100,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right'
      }
    : void 0
})
function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '编辑',
      onClick: handleedit.bind(null, record),
      ifShow: hasPermission([574])
    }
  ]

  if (record.has_fund == 1) {
    editButtonList.forEach((item) => {
      item.disabled = true
    })
  }

  return editButtonList
}
//数据export导出
async function handleExport(e) {
  setLoading(true)
  buttonlodaing.value = true
  const data = await getForm().getFieldsValue()
  const ids = await getSelectRowKeys()
  if (e == 'key' && ids.length == 0) return message.error('请选择明细')
  try {
    try {
      await postexport(true, { ...data, pageSize: 1000, ids: ids })
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await postexport(false, { ...data, pageSize: 1000, ids: ids })
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `支出明细-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    setLoading(false)
    buttonlodaing.value = false
  }
}

// 底部统计函数
function statisticsAmount(data) {
  return data.reduce((total, item) => add(total, item.amount), 0)
}

function handleedit(record) {
  openDrawer(true, { record })
  setDrawerProps({ title: '编辑', showFooter: true })
}
</script>

<style scoped lang="less">
.footer {
  font-size: 16px;
  font-weight: bold;
}
</style>
