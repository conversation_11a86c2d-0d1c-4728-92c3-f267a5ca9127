import { defHttp } from '/@/utils/http/axios'

enum Api {
  mfgetCheckMethod = '/meituan/mf/getCheckMethod',
  mfupdateCheckCap = '/meituan/mf/updateCheckCap'
}

export const mfgetCheckMethod = (params?: {}) => {
  return defHttp.get({
    url: Api.mfgetCheckMethod,
    params
  })
}
export const mfupdateCheckCap = (params?: {}) => {
  return defHttp.post(
    {
      url: Api.mfupdateCheckCap,
      params
    },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
