<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <!-- <template #toolbar>
        <Button v-if="hasPermission([117])" type="primary" @click="handleCreate">创建新任务</Button>
      </template> -->
      <!--      <template #bodyCell="{ column, record }">-->
      <!--        <template v-if="column.key === 'action'">-->
      <!--          <TableAction :actions="createActions(record)" />-->
      <!--        </template>-->
      <!--      </template>-->
    </BasicTable>
    <!--    <listboardDrawer @register="registerDrawer" @success="reload" />-->
  </div>
</template>

<script lang="ts" setup name="/task/list-board">
import { BasicTable, useTable } from '/@/components/Table'
import { columns, searchFormSchema } from './datas/datas'
import { getTaskList } from '/@/api/task/task'
// import { Button } from 'ant-design-vue'
// import listboardDrawer from './compenent/listboardDrawer.vue'
// import { useDrawer } from '/@/components/Drawer'
// import { usePermission } from '/@/hooks/web/usePermission'

// const { hasPermission } = usePermission()
const [registerTable] = useTable({
  // title: '任务面板',
  api: getTaskList,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    fieldMapToTime: [['createdBy', ['createdBy_at_start', 'createdBy_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  useSearchTabs: true,
  tabsConfig: {
    fields: 'status',
    tabPaneProps: {
      tabsSchemas: [
        {
          key: undefined,
          tab: '全部'
        },
        {
          key: 0,
          tab: '未处理'
        },
        {
          key: 1,
          tab: '处理中'
        },
        {
          key: 2,
          tab: '已处理'
        }
      ]
    }
  }
  // actionColumn: {
  //   width: 200,
  //   title: '操作',
  //   dataIndex: 'action',
  //   fixed: 'right'
  // }
})

// function createActions(record: Recordable): Recordable[] {
//   let editButtonList: ActionItem[] = [
//     // {
//     //   icon: 'ant-design:eye-outlined',
//     //   tooltip: '查看任务详情',
//     //   onClick: handleDetail.bind(null, record)
//     // },
//     {
//       icon: 'clarity:note-edit-line',
//       label: '修改',
//       onClick: handleChangeStatus.bind(null, record),
//       ifShow: hasPermission([118])
//     }
//   ]
//
//   return editButtonList
// }
// function handleDetail(record: Recordable): void {
//   openDrawer(true, { record, type: 'detail' })
//   setDrawerProps({ title: '任务详情' })
// }

// async function handleChangeStatus(record: Recordable) {
//   openDrawer(true, { record, type: 'edit' }), setDrawerProps({ title: '修改' })
// }
// //创建
// function handleCreate() {
//   openDrawer(true, { type: 'add' }), setDrawerProps({ title: '新建' })
// }
//drawer
// const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
</script>
