import { UploadApiResult } from './model/uploadModel'
import { defHttp } from '/@/utils/http/axios'
import { UploadFileParams } from '/@/types/axios'
import { useGlobSetting } from '/@/hooks/setting'

const { uploadUrl = '' } = useGlobSetting()

/**
 * @description: Upload interface
 */
export function uploadApi(params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: uploadUrl,
      onUploadProgress,
      transformResponse: (res) => {
        console.log(JSON.parse(res), 'res')
        const {
          result: { path }
        } = JSON.parse(res)
        return { url: path }
      }
    },
    params
  )
}
