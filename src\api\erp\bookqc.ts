import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'

enum Api {
  GetBookingqcList = '/erp/qr/pq/getList',
  AddqcUpdateBooking = '/erp/qr/pq/update',
  ApproveqcBooking = '/erp/qr/pq/setStatus',
  DelqcBooking = '/erp/qr/pq/delete',
  qrpqsetFiles = '/erp/qr/pq/setFiles'
}

export const GetBookingqcList = (params) =>
  defHttp.get<
    BasicFetchResult<{ id: number; sale_work_id: string; inCharge: number; plan_out_at: string; remark: string; status: number }>
  >({ url: Api.GetBookingqcList, params })

export const AddqcUpdateBooking = (data: {
  id?: number // 预约出库单ID-编辑才传
  inCharge: number // 负责人
  plan_out_at: string // 预约出库日期
  remark?: string // 备注
  sale_work_id: number // 销售任务ID
  status: number // 状态：0-未审核  1-已审核
}) => defHttp.post({ url: Api.AddqcUpdateBooking, data })

export const ApproveqcBooking = (data: { ids: Array<{ id: number; status: number }> }) => defHttp.post({ url: Api.ApproveqcBooking, data })

export const DelqcBooking = (params: { id: number }) => defHttp.get({ url: Api.DelqcBooking, params })
export const qrpqsetFiles = (params?: {}) =>
  defHttp.post({ url: Api.qrpqsetFiles, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
