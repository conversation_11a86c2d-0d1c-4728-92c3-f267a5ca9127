import { defHttp } from '/@/utils/http/axios'

enum Api {
  mfgetShopMenuList = '/meituan/mf/getShopMenuList',
  mfupdateMenuRelDept = '/meituan/mf/updateMenuRelDept'
}

export const mfgetShopMenuList = (params?: {}) => {
  return defHttp.get({
    url: Api.mfgetShopMenuList,
    params
  })
}
export const mfupdateMenuRelDept = (params?: {}) => {
  return defHttp.post(
    {
      url: Api.mfupdateMenuRelDept,
      params
    },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
