import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'

enum Api {
  GetBookingList = '/erp/stock/po/getList',
  AddOrUpdateBooking = '/erp/stock/po/update',
  ApproveBooking = '/erp/stock/po/setStatus',
  DelBooking = '/erp/stock/po/delete',
  //编辑日期与备注
  updateOutWarehouse = '/erp/stock/po/updateOutWarehouse'
}

export const getBookingList = (params) =>
  defHttp.get<
    BasicFetchResult<{ id: number; sale_work_id: string; inCharge: number; plan_out_at: string; remark: string; status: number }>
  >({ url: Api.GetBookingList, params })

export const addOrUpdateBooking = (data: {
  id?: number // 预约出库单ID-编辑才传
  inCharge: number // 负责人
  plan_out_at: string // 预约出库日期
  remark?: string // 备注
  sale_work_id: number // 销售任务ID
  status: number // 状态：0-未审核  1-已审核
}) => defHttp.post({ url: Api.AddOrUpdateBooking, data })

export const approveBooking = (data: { ids: Array<{ id: number; status: number }> }) => defHttp.post({ url: Api.ApproveBooking, data })

export const delBooking = (params: { id: number }) => defHttp.get({ url: Api.DelBooking, params })
export const updateOutWarehouse = (params: {}) =>
  defHttp.post({ url: Api.updateOutWarehouse, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
