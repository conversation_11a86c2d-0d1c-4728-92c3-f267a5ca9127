import type { <PERSON>ckMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const revisitList = (function () {
  const items: any[] = []
  for (let i = 0; i < 5; i++) {
    items.push({
      id: '@id',
      belongs: '@ctitle',
      'visit_way|1': ['1', '2'],
      group_name: '@name',
      visit_date: '@date',
      'comment|1': [2, 3, 4],
      suggestion: '@cparagraph'
    })
  }
  return { items, total: 5 }
})()

export default [
  {
    url: '/api/revisit/getrevisitList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(revisitList)
    }
  }
] as MockMethod[]
