import { defHttp } from '/@/utils/http/axios'

enum Api {
  expenpostupdate = '/erp/cf/update',
  expengetList = '/erp/cf/getList',
  expensetIsDisabled = '/erp/cf/setIsDisabled'
}

export const expenpostupdate = (params: any) =>
  defHttp.post({ url: Api.expenpostupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const expengetList = (params: any) => defHttp.get({ url: Api.expengetList, params })
export const expensetIsDisabled = (params: any) =>
  defHttp.get({ url: Api.expensetIsDisabled, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
