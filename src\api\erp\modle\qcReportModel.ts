export interface IGetQcReport {
  id: number
  qc_type_id: number
  content: string
  qc_stage_id: number
  result: string
  double_check: 0 | 1
  user_id: number
  stocking_id: number
  order_id: number
  work_id: number
}

export interface QualityDetectionReportItem {
  id: number
  qc_type_id: number
  content: string
  qc_stage_id: number
  result: string
  double_check: number
  images: string[]
  user_id: number
  order_id: number
  work_id: number
  created_at: string
  updated_at: string
}

export interface QcItemListParams {
  has_qc?: 1 | 0 // 1:已质检,0:未质检或者不通过或者二次质检
  item_id?: number // 指定某个item_request商品
  order_by?: string
  page?: number
  pageSize?: number
  sort?: string
  work_id?: number // 销售订单id
  [property: string]: any
}
