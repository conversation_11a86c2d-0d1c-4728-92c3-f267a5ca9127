import { FormSchema } from '/@/components/Form'
import { getAccountList, getItemRequest } from '/@/api/commonUtils'
import { BasicColumn } from '/@/components/Table'
// import { mapItemRequest, mapTreeData } from '/@/views/erp/outWarehouse/datas/OutWarehouseDrawer'
import { ref } from 'vue'
import { TreeSelectProps } from 'ant-design-vue'
import { getOutWarehouseSaleOrder } from '/@/api/erp/outWarehouse'
import { Rule } from 'ant-design-vue/lib/form'

export const treeData = ref<Recordable[]>([])
export const schemas: FormSchema[] = [
  {
    field: 'applicant',
    label: '申请人',
    component: 'ApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'confirmed_at',
    label: '确认时间',
    required: true,
    component: 'DatePicker',
    componentProps: {
      // showTime: true,
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
      // options: typeOptions
    }
  },
  {
    field: 'checkout_at',
    label: '出库时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      // showTime: true,
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'shipment_type',
    label: '出库类型',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        { label: '送出装柜', value: 1 },
        { label: '送货', value: 2 },
        { label: '装柜', value: 3 },
        { label: '自提', value: 4 },
        { label: '工厂出货', value: 5 }
      ]
    }
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: 1 },
        { label: '紧急', value: 2 },
        { label: '非常紧急', value: 3 }
      ]
    }
  },
  {
    field: 'shipment_inCharge',
    label: '仓库出货人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    }
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'FilesSlot',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ]
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    colProps: {
      span: 24
    }
  },
  // {
  //   field: 'relate_id',
  //   label: '出库订单',
  //   component: 'TreeSelect',
  //   slot: 'relate_id',
  //   colProps: { span: 24 },
  //   required: true
  // },
  {
    field: 'relate_detail',
    label: '出库商品明细',
    component: 'Input',
    slot: 'relate_detail',
    colProps: { span: 24 },
    rules: [{ required: true, validator: validDetail }]
  }
]

export const productColumns: BasicColumn[] = [
  {
    title: '所属订单',
    dataIndex: 'from_at',
    width: 200,
    customRender: ({ record }) => {
      return record.source_uniqid
    }
  },
  {
    title: '所属订单商品名称',
    dataIndex: 'name',
    width: 150
    // customRender: ({ record }) => {
    //   return record.name ? record.name : mapItemRequest.value[record.request_id]?.name
    // }
  },
  // {
  //   title: '所属订单商品批次号',
  //   dataIndex: 'batch_code',
  //   width: 200,
  //   customRender: ({ record }) => {
  //     return record.batch_code ? record.batch_code : mapItemRequest.value[record.request_id]?.batch_code
  //   }
  // },
  // {
  //   title: '出库状态',
  //   dataIndex: 'status',
  //   width: 150,
  //   resizable: true
  // },
  {
    title: '出库数量',
    dataIndex: 'quantity',
    resizable: true,
    width: 150
  },
  // {
  //   title: '关联库存商品',
  //   dataIndex: 'stocking',
  //   width: 250
  // },
  {
    title: '部门',
    dataIndex: 'dept_id',
    width: 250
  }
]

function validDetail(_rule: any, value: any[]) {
  console.log(value)
  if (!value || value.length === 0) return Promise.reject('请选择出库商品')
  // console.log(value.quantity)
  const validRes = value.every((item) => item.quantity >= 0.01 && item.dept_id)
  return validRes ? Promise.resolve() : Promise.reject('请完善明细信息！')
}

export async function handleLoadTree(treeNode: TreeSelectProps['treeData'][number], isInvoke?: boolean) {
  const curData = isInvoke
    ? treeData.value.find((item: Recordable) => item.id === treeNode.id)
    : treeData.value.find((item: Recordable) => item.id === treeNode.dataRef?.id)
  return new Promise(async (resolve) => {
    const { id } = !isInvoke ? treeNode.dataRef : treeNode
    const { items } = await getItemRequest({ work_id: id })
    console.log(treeNode)
    if (curData) {
      curData.children = items.map((item) => ({
        ...item,
        isLeaf: true,
        key: `${item.id}-child`,
        pId: id,
        value: `${item.id}-child`,
        from_at: treeNode.strid,
        status: 0
      }))
    }
    resolve(curData || [])
  })
}

async function getRelateList() {
  // const { items } = await getWorkList({ pageSize: 999999, type: 3 })
  const { items } = await getOutWarehouseSaleOrder()
  treeData.value = items.map((work) => ({
    ...work,
    isLeaf: false,
    key: `${work.work_id}-parent`,
    name: work.strid,
    pId: 0,
    value: `${work.work_id}-parent`
    // checkable: false
  }))
}

getRelateList()

export const mapDetailStatus = [
  {
    label: '未出库',
    value: 0
  },
  {
    label: '准备出库',
    value: 1
  },
  {
    label: '已出库',
    value: 2
  }
]
