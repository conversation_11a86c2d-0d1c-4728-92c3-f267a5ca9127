<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { mfgetPayInfo } from '/@/api/restaurantmanagement/cashwithorder'
import { BasicTable, useTable } from '/@/components/Table'

const [registerTable] = useTable({
  showTableSetting: true,
  showIndexColumn: false,
  api: mfgetPayInfo,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas,
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['checkout_at', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})
</script>
