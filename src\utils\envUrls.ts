/**
 * 环境URL配置工具
 * 根据环境类型动态生成不同的URL
 */

/**
 * 获取环境相关的URL配置
 * @returns 包含所有环境URL的对象
 */
export function getEnvUrls() {
  const envType = import.meta.env.VITE_ENV_TYPE as string
  const isTest = envType === 'test'

  // 根据环境类型确定URL后缀
  const suffix = isTest ? 'tests' : ''

  return {
    // 项目质检链接
    PROJECTQCLINK: `https://erp.gbuilderchina.com/projectQCShare${suffix}/#/projectQCShare?`,

    // 项目产品质检链接
    PROJECTPRODUCTQCLINK: `https://erp.gbuilderchina.com/projectQCShare${suffix}/#/productQCShare?`,

    // 链接计算
    LINKCALC: `https://erp.gbuilderchina.com/share${suffix}/?source_uniqid=`,

    // 进度线
    PROGRESSLINE: `https://erp.gbuilderchina.com/progressShare${suffix}/#/PCShare?`,

    // 产品链接计算
    LINKCALC_PRODUCT: `https://erp.gbuilderchina.com/projectQCShare${suffix}/#/productQCShareOrder?`,

    // 项目产品质检链接（员工自用）
    PROJECTPRODUCTQCLINK_SELF: `https://erp.gbuilderchina.com/projectQCShare${suffix}/#/productQCShareSelf?`
  }
}

/**
 * 环境URL常量
 * 在组件中可以直接使用这个对象
 */
export const ENV_URLS = getEnvUrls()

/**
 * 获取特定的环境URL
 * @param urlType URL类型
 * @returns 对应的URL字符串
 */
export function getEnvUrl(urlType: keyof ReturnType<typeof getEnvUrls>): string {
  return ENV_URLS[urlType]
}
