import dayjs from 'dayjs'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { message } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '订单编号',
    dataIndex: 'order_number',
    width: 150,
    resizable: true
  },
  {
    title: '出品部门',
    dataIndex: 'out_department',
    width: 150,
    resizable: true
  },
  {
    title: '菜品部门',
    dataIndex: 'department',
    width: 150,
    resizable: true
  },
  {
    title: '菜品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '菜品大类',
    dataIndex: 'big_category',
    width: 150,
    resizable: true
  },
  {
    title: '菜品小类',
    dataIndex: 'small_category',
    width: 150,
    resizable: true
  },
  {
    title: '销售数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  },
  {
    title: '销售金额(元)',
    dataIndex: 'sales_amount',
    width: 150,
    resizable: true
  },
  {
    title: '菜品收入金额(元)',
    dataIndex: 'sales_income',
    width: 150,
    resizable: true
  },
  {
    title: '结账时间',
    dataIndex: 'checkout_at',
    width: 150,
    resizable: true
  },
  {
    title: '订单分类',
    dataIndex: 'type_name',
    width: 150,
    resizable: true
  },
  {
    title: '桌号',
    dataIndex: 'desk_number',
    width: 150,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'checkout_at',
    label: '结账时间',
    defaultValue: [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    component: 'SingleRangeDate',
    componentProps: ({ formModel }) => {
      return {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        },
        endPickerProps: {
          // 点击结束日期选择器时检查开始日期是否已选择
          onFocus: () => {
            // 如果开始日期为空
            if (!formModel.checkout_at || !formModel.checkout_at[0]) {
              // 提示用户需要先选择开始日期
              message.warning('请先选择开始日期')

              // 将焦点设置到开始日期选择器
              setTimeout(() => {
                const startDateInput = document.querySelector('input[placeholder="开始日期"]') as HTMLInputElement
                if (startDateInput) {
                  startDateInput.focus()
                }
              }, 100)

              return false
            }
            return true
          },
          disabledDate: (current: any) => {
            if (!current) return false

            // 结束日期不能超过今天
            const isAfterToday = current > dayjs().endOf('day')

            // 获取开始日期的值
            const startDate = formModel.checkout_at && formModel.checkout_at[0] ? dayjs(formModel.checkout_at[0]) : null

            // 结束日期不能早于开始日期
            const isBeforeStart = startDate && current < startDate

            return isAfterToday || isBeforeStart
          }
        },
        startPickerProps: {
          disabledDate: (current: any) => {
            if (!current) return false

            // 开始日期不能超过今天
            const isAfterToday = current > dayjs().endOf('day')

            return isAfterToday
          }
        }
      }
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'order_number',
    label: ' 订单编号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'code',
    label: '菜品编码',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'name',
    label: '菜品名称',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'big_category',
    label: '菜品大类',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'small_category',
    label: '菜品小类',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'dept_id',
    label: '菜品部门',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '空中餐厅',
          value: 173
        },
        {
          label: 'SAWA餐厅',
          value: 174
        },
        {
          label: '麟料理餐厅',
          value: 175
        },
        {
          label: '江南餐厅',
          value: 176
        },
        {
          label: '法餐厅',
          value: 177
        },
        {
          label: '甜点酒水',
          value: 178
        },
        {
          label: '粤棠餐厅',
          value: 189
        }
      ]
    },
    colProps: {
      span: 6
    }
  }
]
