<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="{ no_cache: 0 }">
      <template #toolbar>
        <Button
          type="primary"
          @click="
            () => {
              reload()
            }
          "
          >更新数据</Button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editModal @register="registereditModal" @success="reload" />
  </div>
</template>
<script lang="ts" setup>
import { columns, schemas } from './datas/datas'
import { mfgetShopMenuList } from '/@/api/restaurantmanagement/restaurantdishes'
import { BasicTable, useTable, TableImg, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import editModal from './components/editModal.vue'
import { useModal } from '/@/components/Modal'

const [registereditModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  useSearchForm: true,
  showTableSetting: true,
  columns,
  showIndexColumn: false,
  api: mfgetShopMenuList,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '绑定部门',
      onClick: handleEdit.bind(null, record)
    }
  ]

  return editButtonList
}

function handleEdit(record) {
  openModal(true, record)
}
</script>
