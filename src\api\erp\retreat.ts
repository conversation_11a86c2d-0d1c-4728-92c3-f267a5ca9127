import type { BasicPageParams } from '../model/baseModel'
import type { IAddRetreat, IGetRetreatList, IRetreatUpdate } from './modle/retreatMoel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetRetreatList = '/erp/retreat/getList',
  GetRetreatDetail = '/erp/retreat/details',
  RetreatAdd = '/erp/retreat/create',
  RetreatUpdate = '/erp/retreat/update',
  GetRetreatInWarehouseSelect = '/tools/getInStockSel',
  GetRetreatInWarehouseProductSelect = '/tools/getItemStocking',
  SetRetreatStatus = '/erp/retreat/setStatus',
  DeleteRetreat = '/erp/retreat/delete',
  UpdateWorksAudit = '/erp/finance/ws/updateWorksAudit'
}

//获取退货单列表
export const getRetreatList = (params?: BasicPageParams & Partial<IGetRetreatList>) => defHttp.get({ url: Api.GetRetreatList, params })

//获取退货单详情
export const getRetreatDetail = (params?: {}) => defHttp.get({ url: Api.GetRetreatDetail, params })

//退货单新增
export const retreatAdd = (params: IAddRetreat) => defHttp.post({ url: Api.RetreatAdd, params }, { successMessageMode: 'message' })

//退货单编辑
export const retreatUpdate = (params: IRetreatUpdate) => defHttp.post({ url: Api.RetreatUpdate, params }, { successMessageMode: 'message' })

//退货采购单下拉列表
export const getRetreatInWarehouseSelect = (params?: {}) => defHttp.get({ url: Api.GetRetreatInWarehouseSelect, params })

//退货采购单商品下拉列表 ,这两个参数是字面量类型,因为一定是传这两个值status: 2; is_residue: 1
export const getRetreatInWarehouseProductSelect = (params: { doc_in_id: number; status: 2; is_residue: 1 }) =>
  defHttp.get({ url: Api.GetRetreatInWarehouseProductSelect, params })

//退货单状态修改
export const setRetreatStatus = (params: { id: number; status: number }) =>
  defHttp.get({ url: Api.SetRetreatStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//退货单金额状态修改
export const UpdateWorksAudit = (params: { work_ids: Object; is_audit: number }) =>
  defHttp.post({ url: Api.UpdateWorksAudit, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//退货单删除
export const deleteRetreat = (params: { id: number }) =>
  defHttp.get({ url: Api.DeleteRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
