import { defHttp } from '/@/utils/http/axios'
import { SearchListParams } from '/@/api/Performance/types'

enum Api {
  GetSalesPerformance = '/erp/cert/score/getlist',
  EditSalesPerformance = '/account/st/save',
  GetDeptColumns = '/department/getOperate',
  GetDeptList = '/erp/cert/score/getDeptlist',
  GetSalesDetailList = '/erp/cert/score/getDetailist',
  CalcCount = '/erp/cert/score/count',
  ExportPersonal = '/erp/cert/score/exportgetList',
  ExportDept = '/erp/cert/score/exportDeptList',
  ExportSalesDetail = '/erp/cert/score/exportDetailist',
  GetOperateDept = '/erp/cert/score/getOperateSumlist',
  GetOperatePerson = '/erp/cert/score/getOpMeSumlist'
}

// 获取业绩统计列表
export const getSalesPerformance = (params?: SearchListParams) => defHttp.get({ url: Api.GetSalesPerformance, params })

// 编辑目标金额
export const editSalesTargetPerformance = (data: { dept_id?: number; id?: number; yearMonth: number; target_amount: number }) =>
  defHttp.post({ url: Api.EditSalesPerformance, data })

// 获取业绩统计运营中心表头
export const getDeptColumns = () => defHttp.get({ url: Api.GetDeptColumns })

// 获取部门统计的业绩
export const getDeptStatistics = (params?: SearchListParams) => defHttp.get({ url: Api.GetDeptList, params })

// 销售单明细业绩报表统计
export const getSalesDetailStatistics = (params?: SearchListParams) => defHttp.get({ url: Api.GetSalesDetailList, params })

// 业绩统计重新计算
export const calcSalesDetail = (params) => defHttp.get({ url: Api.CalcCount, params }, { isTransformResponse: false })

// 个人业绩导出
export const exportPersonal = (params) =>
  defHttp.get({ url: Api.ExportPersonal, params, responseType: 'blob' }, { isTransformResponse: false })

// 部门业绩导出
export const exportDept = (params) => defHttp.get({ url: Api.ExportDept, params, responseType: 'blob' }, { isTransformResponse: false })

// 销售单明细导出
export const exportSalesDetail = (params) =>
  defHttp.get({ url: Api.ExportSalesDetail, params, responseType: 'blob' }, { isTransformResponse: false })

// 运营中心部门业绩统计
export const getOperateDept = (params) => defHttp.get({ url: Api.GetOperateDept, params })

// 运营部门成员业绩统计
export const getOperatePerson = (params) => defHttp.get({ url: Api.GetOperatePerson, params })
