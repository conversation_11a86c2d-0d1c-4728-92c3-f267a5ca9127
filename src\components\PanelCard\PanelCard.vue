<template>
  <div :class="{ selected: isSelect }" @[cardHandler]="handleEmit(cardEvent || cardHandler)">
    <div v-if="cardData.isSelect" class="absolute select-icon">
      <CheckOutlined class="selected-svg" />
    </div>
    <Card class="cart-item" hoverable v-bind="$attrs">
      <!-- 标题部分start -->
      <template #title>
        <slot name="title"></slot>
      </template>
      <!-- 标题部分end -->

      <!-- 卡片主体部分start -->
      <slot name="card-main"></slot>

      <div class="card-body">
        <slot name="body"></slot>
        <!-- 卡片内部表格部分start -->
        <Table :dataSource="tableData" :columns="tableColumns" :pagination="false" :scroll="{ y: '100px', x: '100%' }">
          <template #headerCell="data">
            <slot name="TableHeaderCell" v-bind="data"></slot>
          </template>
        </Table>
        <!-- 卡片内部表格部分end -->
      </div>

      <!-- 卡片主体部分end -->
      <div>
        <slot name="card-tips" :tips-fetch-data="tipsFetchData"></slot>
      </div>
      <template #actions v-if="operationFooter">
        <div
          style="width: 100%; display: flex; justify-content: center; align-items: center"
          v-for="(item, index) in compOperations"
          :key="index"
          v-bind="item"
        >
          <Popconfirm v-if="item.popConfirm" v-bind="item.popConfirm" @confirm.stop="handleEmit(item.value)">
            <Icon :icon="item.icon" :class="{ 'mr-1': !!item.text }" v-if="item.icon" />
            <template v-if="item.text">{{ item.text }}</template>
          </Popconfirm>
          <div v-else @click.stop="props.operates?.includes(item.value) ? handleEmit(item.value) : item.onClick()">
            <Icon :icon="item.icon" v-if="item.icon" />
            {{ item.text || item.label }}</div
          >
        </div>
        <slot name="extra-actions"></slot>
        <Dropdown :trigger="['hover']" :dropMenuList="getDropdownList" popconfirm v-if="dropDownActions && getDropdownList.length > 0">
          <slot name="more"></slot>
          <a-button type="link" size="small" v-if="!$slots.more"> <Icon icon="ion:ios-more" /> </a-button>
        </Dropdown>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts" name="PanelCard">
import { Card, Table, Popconfirm } from 'ant-design-vue'
import Icon from '/@/components/Icon'
import { Dropdown } from '/@/components/Dropdown'
import { computed, CSSProperties, onMounted, PropType, ref, toRaw } from 'vue'
import { mapOperateBtn, Operations } from './datas/PanelCard'
import { BasicColumn } from '/@/components/Table/src/types/table'
import { PackageInfo } from '/@/api/erp/modle/packageTypes'
import { CheckOutlined } from '@ant-design/icons-vue'
import { isUndefined } from 'lodash-es'
import { usePermission } from '/@/hooks/web/usePermission'
import { ActionItem } from '/@/components/Table'
import { isBoolean, isFunction } from '/@/utils/is'

const tipsFetchData = ref<any>()
const emits = defineEmits<{
  (e: 'edit', val: any): void
  (e: 'detail', val: any): void
  (e: 'approve', val: any): void
  (e: 'delete', val: any): void
  (e: 'selected', val: any): void
}>()
const props = defineProps({
  tipsApi: { type: Function as PropType<(arg?: Recordable) => Promise<Recordable>> },
  cardData: {
    type: Object as PropType<PackageInfo>,
    default: () => ({})
  },
  //表格数据
  tableData: {
    type: Array as PropType<Recordable[]>,
    default: null
  },
  //表格规范
  tableColumns: {
    type: Array as PropType<BasicColumn[]>,
    default: () => []
  },
  //表格背景style
  mainStyle: {
    type: Object as () => CSSProperties,
    default: () => ({})
  },
  //是否开启操作栏
  operationFooter: {
    type: Boolean,
    default: false
  },
  //操作栏参数
  operates: {
    type: Array as PropType<('detail' | 'approve' | 'edit' | 'delete')[]>,
    default: () => ['detail', 'approve', 'edit', 'delete']
  },
  // 额外功能的点击按键
  extraActions: {
    type: Array as PropType<Operations[]>,
    default: () => []
  },
  // 额外功能的点击按键
  dropDownActions: {
    type: Array as PropType<Operations[]>,
    default: () => []
  },
  // 卡片外层div组件触发的事件
  cardHandler: {
    type: String,
    default: null
  },
  // 卡片外层div组件emit的事件名称
  cardEvent: {
    type: String,
    default: null
  },
  // 是否选中卡片
  isSelect: {
    type: Boolean,
    default: false
  },
  operatePermission: {
    type: Object as PropType<{ [key in 'detail' | 'approve' | 'edit' | 'delete']: boolean }>,
    default: () => ({
      detail: true,
      approve: true,
      edit: true,
      delete: true
    })
  }
})

const { hasPermission } = usePermission()

const compOperations = computed<Operations[]>(() => {
  return [
    ...props.operates?.filter((item) => props.operatePermission[item]).map((item) => mapOperateBtn[item]),
    ...props.extraActions
  ].filter((item) => isUndefined(item.ifShow) || item.ifShow)
})

const getDropdownList = computed((): any[] => {
  const list = (toRaw(props.dropDownActions) || []).filter((action) => {
    return hasPermission(action.auth) && isIfShow(action)
  })
  return list.map((action) => {
    const { label, popConfirm } = action
    return {
      ...action,
      ...popConfirm,
      onConfirm: popConfirm?.confirm,
      onCancel: popConfirm?.cancel,
      text: label
    }
  })
})

function isIfShow(action: ActionItem): boolean {
  const ifShow = action.ifShow

  let isIfShow = true

  if (isBoolean(ifShow)) {
    isIfShow = ifShow
  }
  if (isFunction(ifShow)) {
    isIfShow = ifShow(action)
  }
  return isIfShow
}

function handleEmit(emitVal: any) {
  emits(emitVal, props.cardData)
}

async function handleFetch() {
  if (props?.tipsApi) {
    try {
      const data = await props.tipsApi()
      console.log(data)
      tipsFetchData.value = data
    } catch (e) {
      console.log(e)
    }
  }
}

onMounted(() => {
  handleFetch()
})
</script>

<style scoped lang="less">
.selected {
  outline: 1px solid #0960bd;
  position: relative;

  .select-icon {
    left: 0;
    top: 0;
    border-top: 20px solid #0960bd;
    border-left: 20px solid #0960bd;
    border-right: 20px solid transparent;
    border-bottom: 20px solid transparent;
    z-index: 2;
    //display: flex;
    //justify-content: center;
    //align-items: center;

    .selected-svg {
      background: #0960bd;
      color: #fff;
      font-weight: 900;
      position: absolute;
      left: 0;
      top: 0;
      transform: translate(-100%, -100%);
    }
  }
}
.cart-item {
  font-size: 14px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tit-content {
      font-size: 13px;
    }
    .date {
      color: #808080;
    }
    .no {
      color: #f81d22;
    }
  }
  .card-tips {
    display: flex;
    align-items: center;
    background: #ffe6e5;
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 10px;

    &.confirm {
      background: #deffe4;

      .tips-msg {
        color: #55d187;
      }
    }
    .tips-msg {
      text-align: left;
      color: #ff4d4f;
      flex: 1;
    }
  }
  .card-main {
    margin-bottom: 5px;
    .sales-bar {
      text-align: left;
    }
    .dept-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dept-left {
        flex: 1;
        text-align: left;
        white-space: pre-wrap;
        width: 65%;
        //flex-direction: column;
      }
      .dept-right {
        display: flex;
        justify-content: space-between;
        // color: #1890ff;
        //width: 40%;
        // .img:hover,
        // .detail:hover {
        //   color: #0561b8;
        // }
      }
    }
    .card-table {
      text-align: center;
      tr {
        td:last-child {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .info {
      text-align: left;
      margin: 12px 0;
      .info-key-value {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .key {
          font-weight: 600;
        }
      }
    }
    .status-bar {
      color: #f81d22;
      font-size: 16px;
      font-weight: 600;
      text-align: right;
    }
    .status-bar.active {
      color: #87d068;
    }
    .download-btn {
      display: flex;
      justify-content: flex-start;
      font-size: 20px;

      .btn {
        color: #1890ff;
        &:hover {
          color: #0561b8;
        }
      }
    }
  }
  .card-body {
    margin-bottom: 5px;
    ::v-deep(.ant-table) {
      margin-left: 0 !important;
      .ant-table-thead > tr > th {
        white-space: break-spaces;
      }
    }
  }
}
/deep/.ant-tag {
  border: none;
}
.ant-alert {
  background-color: #deffe4;
  border: none;
  /deep/.ant-alert-content {
    flex: none;
    .ant-alert-message {
      color: #55d187;
    }
  }
}
</style>
