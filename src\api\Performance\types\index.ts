export interface SearchListParams {
  // 部门：1线上 2线下
  dept_cls?: number
  // 部门id
  dept_id?: string
  // 负责人ID
  inCharge?: string
  // 排序字段默认finish_amount
  order_by?: string
  // 页数
  page?: string
  // 每页条数
  pageSize?: string
  // 排序默认倒序
  sort?: string
  // 类型：1.产品部方案经理 2.运营中心项目经理方案 3.导购员业绩 4.带单业绩（个人）5.社媒运营 6.2D设计师 7.3D设计师 8.设计师 9.翻译 10.销售跟单
  // 11.【部门表】产品部业绩 12.【运营中心表】运营中心业绩 13.【部门表】带单部门业绩
  type?: number
  // 统计日期202406
  yearMonth: number
  [property: string]: any
}
