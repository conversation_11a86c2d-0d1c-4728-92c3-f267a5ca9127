import { defHttp } from '/@/utils/http/axios'

enum Api {
  SetIsStock = '/erp/package/setIsStock',
  GetPackageList = '/erp/package/getList',
  AddPackage = '/erp/package/create',
  GetPackageDetail = '/erp/package/detail',
  EditPackage = '/erp/package/update',
  UpdatePacking = '/erp/packing/updatePacking',
  //调拨
  getallot = '/erp/package/allot',
  //作废
  setIsCancel = '/erp/package/setIsCancel',
  //调拨列表
  getallotList = '/erp/package/allotList',
  //转换列表
  getconvertList = '/erp/package/convertList',
  //导入
  updateDesc = '/erp/package/updateDesc',
  //转换列表批量跟进
  setIsFollow = '/erp/package/setIsFollow',
  //打包件数更改
  setPkgQuantit = '/erp/package/setPkgQuantity',
  //包裹是否出库
  checkPackage = '/erp/package/checkPackage',
  setIsInRetreat = '/erp/package/setIsInRetreat',
  packagefastConversion = '/erp/package/fastConversion',
  packagecancelStock = '/erp/package/cancelStock'
}

// 包裹列表
export const getPackageList = (params) => defHttp.post({ url: Api.GetPackageList, params })
// 包裹列表导出
export const getPackageListExcel = (params) =>
  defHttp.post({ url: Api.GetPackageList, params, responseType: 'blob' }, { isTransformResponse: false })

// 批量包裹添加
export const addPackage = (data) => defHttp.post({ url: Api.AddPackage, data })

// 包裹详情
export const getPackageDetail = (data: {
  ids?: number[]
  pageSize?: number
  page?: number
  purchase_work_ids?: number[]
  packing_ids: number[]
}) => defHttp.post({ url: Api.GetPackageDetail, data })

// 编辑包裹
export const editPackage = (data) => defHttp.post({ url: Api.EditPackage, data })

export const createBoxingOrder = (data) => defHttp.post({ url: Api.UpdatePacking, data })
//调拨包裹
export const getallot = (data) => defHttp.post({ url: Api.getallot, data }, { successMessageMode: 'message', errorMessageMode: 'message' })
//包裹作废
export const setIsCancel = (data) =>
  defHttp.post({ url: Api.setIsCancel, data }, { successMessageMode: 'message', errorMessageMode: 'message' })
//调拨列表
export const getallotList = (params) => defHttp.get({ url: Api.getallotList, params })
//转换列表
export const getconvertList = (params) => defHttp.get({ url: Api.getconvertList, params })

// 包裹清点
export const setIsStock = (data: { stockList: Array<{ id: number }>; stock_source: 1 | 2 }) => defHttp.post({ url: Api.SetIsStock, data })

// 包裹导入
export const updateDesc = (data: {}) =>
  defHttp.post({ url: Api.updateDesc, data }, { successMessageMode: 'message', errorMessageMode: 'message' })
//转换列表批量跟进
export const setIsFollow = (data: {}) =>
  defHttp.post({ url: Api.setIsFollow, data }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const setPkgQuantit = (params: {}) =>
  defHttp.get({ url: Api.setPkgQuantit, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const checkPackage = (params: {}) => defHttp.post({ url: Api.checkPackage, params })
export const setIsInRetreat = (params: {}) =>
  defHttp.post({ url: Api.setIsInRetreat, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const packagefastConversion = (params: {}) =>
  defHttp.post({ url: Api.packagefastConversion, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const packagecancelStock = (params: {}) =>
  defHttp.post({ url: Api.packagecancelStock, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
