import { defHttp } from '/@/utils/http/axios'

enum Api {
  ratingfeedbackupdate = '/erp/rating/feedback/update',
  ratingfeedbackgetList = '/erp/rating/feedback/getList',
  ratingfeedbacksetStatus = '/erp/rating/feedback/setStatus',
  ratingfeedbacksetManageStatus = '/erp/rating/feedback/setManageStatus',
  ratingfeedbacksetReject = '/erp/rating/feedback/setReject',
  ratingfeedbacksetManageReject = '/erp/rating/feedback/setManageReject'
}

// 导出一个名为ratingfeedbackupdate的函数，该函数接收一个参数params
export const ratingfeedbackupdate = (params) =>
  // 使用defHttp.post方法发送post请求，请求的url为Api.ratingfeedbackupdate，请求的参数为params
  defHttp.post({ url: Api.ratingfeedbackupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingfeedbackgetList = (params) => defHttp.get({ url: Api.ratingfeedbackgetList, params })
export const ratingfeedbacksetStatus = (params) =>
  defHttp.post({ url: Api.ratingfeedbacksetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingfeedbacksetManageStatus = (params) =>
  defHttp.post({ url: Api.ratingfeedbacksetManageStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingfeedbacksetReject = (params) =>
  defHttp.post({ url: Api.ratingfeedbacksetReject, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingfeedbacksetManageReject = (params) =>
  defHttp.post({ url: Api.ratingfeedbacksetManageReject, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
