import { defHttp } from '/@/utils/http/axios'

enum Api {
  //新增
  GetChanneLcreate = '/erp/source/create',
  //新增,
  GetChanneLupdate = '/erp/source/update',
  GetChanneLgetList = '/erp/source/getList'
}

export const getChanneLcreate = (params: {}) => defHttp.post({ url: Api.GetChanneLcreate, params }, { successMessageMode: 'message' })
export const getChanneLupdate = (params: {}) => defHttp.post({ url: Api.GetChanneLupdate, params }, { successMessageMode: 'message' })
export const getChanneLgetList = (params: {}) => defHttp.get({ url: Api.GetChanneLgetList, params }, { successMessageMode: 'message' })
