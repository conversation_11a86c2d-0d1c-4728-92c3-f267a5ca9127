<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { propTypes } from '/@/utils/propTypes'

const props = defineProps({
  src: propTypes.string.def('')
})
const loading = ref(true)
const height = ref('')
const frameRef = ref<HTMLElement | null>(null)
function init() {
  height.value = `${document.documentElement.clientHeight - 94.5}px`
  loading.value = false
}
onMounted(() => {
  setTimeout(() => {
    init()
  }, 300)
})
</script>

<template>
  <div v-loading="loading" :style="`height:${height}`">
    <iframe ref="frameRef" :src="props.src" style="width: 100%; height: 100%" frameborder="no" scrolling="auto"></iframe>
  </div>
</template>
