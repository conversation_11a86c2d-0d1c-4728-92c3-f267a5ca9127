import { defHttp } from '/@/utils/http/axios'
import type { BasicFetchResult, BasicFormDataResult, BasicPageParams } from '/@/api/model/baseModel'
import type { SearchInventory, InventoryParams } from './modle/types'
import { RcFile } from 'ant-design-vue/lib/vc-upload/interface'
import { EditStockingParams } from './modle/types'

enum Api {
  EditStocking = '/erp/stock/updateItemStocking',
  SetBatchCost = '/erp/stock/setBatchUniprice',
  GetInventoryList = '/erp/stock/ts/getList2',
  UploadInventoryImg = '/oss/putImg2Stocking',
  CreateInventory = '/erp/stock/ts/add',
  EditInventory = '/erp/stock/ts/update2',
  DelInventory = '/erp/stock/ts/remove2',
  //审核
  postconfirm = '/erp/stock/ts/confirm',
  //库存转换单
  GetInventoryListst = '/erp/stock/st/getDoc',
  EditInventoryst = '/erp/stock/st/updateBatch',
  DeltInventoryst = '/erp/stock/st/deleteDoc',
  DeltitemInventoryst = 'erp/stock/st/deleteItem',
  getitemInventoryst = '/erp/stock/st/getItem',
  UploadaddaddInventoryImg = '/oss/putImg', //图片
  SetStatus = '/erp/stock/stp/setStatus',
  //获取tools/GetItemStocking
  GetItemStocking = '/tools/getItemStocking',
  //获取供应商
  gettoolsgetErpSupplier = '/tools/getErpSupplier',
  //库存
  getstockList = '/erp/stock/getItemStocking',
  SetReserve = '/erp/stock/setReserve',
  Export = '/erp/stock/export',
  //获取库存
  stockgetList = '/erp/stock/getList',
  UpdateTransform = '/erp/stock/stp/update',
  //盘点包裹新增
  stocktspupdate = '/erp/stock/tsp/update',
  stocktspsetStatus = '/erp/stock/tsp/setStatus'
}

export const setBatchCost = (data: { unit_price: number; account_name: string; account_code: string; ids: Array<{ id: number }> }) =>
  defHttp.post<{ msg: string }>({
    url: Api.SetBatchCost,
    data
  })

export const editStocking = (data: EditStockingParams) => defHttp.post({ url: Api.EditStocking, data })

export const getInventoryList = (params?: SearchInventory | BasicPageParams) => {
  return defHttp.get<BasicFetchResult<any[]>>({
    url: Api.GetInventoryList,
    params
  })
}

export const uploadInventoryImg = (data: { file: string | Blob | RcFile }) => {
  return defHttp.post<BasicFormDataResult>({
    url: Api.UploadInventoryImg,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

export const createInventory = (data: InventoryParams) => {
  return defHttp.post<{ code: number; msg: string }>({
    url: Api.CreateInventory,
    data
  })
}

export const editInventory = (data?: {}) => {
  return defHttp.post<{ code: number; msg: string }>({
    url: Api.EditInventory,
    data
  })
}

export const delInventory = (id: number) => {
  return defHttp.get<{ code: number; msg: string }>({
    url: Api.DelInventory,
    params: {
      id
    }
  })
}
//库存转换单
export const getInventoryListst = (params?: {}) => {
  return defHttp.get({
    url: Api.GetInventoryListst,
    params
  })
}
export const getitemInventoryst = (params?: {}) => {
  return defHttp.get({
    url: Api.getitemInventoryst,
    params
  })
}

export const editInventoryst = (data?: {}) => {
  return defHttp.post<{ code: number; msg: string }>(
    {
      url: Api.EditInventoryst,
      data
    },
    { isTransformResponse: false }
  )
}

export const delInventoryst = (id: number) => {
  return defHttp.get<{ code: number; msg: string }>({
    url: Api.DeltInventoryst,
    params: {
      id
    }
  })
}
export const DeltitemInventoryst = (id: number) => {
  return defHttp.get<{ code: number; msg: string }>({
    url: Api.DeltitemInventoryst,
    params: {
      id
    }
  })
}
export const uploadaddaddInventoryImg = (data: { file: string | Blob | RcFile }) => {
  return defHttp.post<BasicFormDataResult>({
    url: Api.UploadaddaddInventoryImg,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
export const setStatus = (params?: { id: string; status: number }) => defHttp.get({ url: Api.SetStatus, params })

//获取getItemStocking
export const getItemStocking = (params?: {}) => {
  return defHttp.get({ url: Api.GetItemStocking, params })
}
//获取供应商
export const gettoolsgetErpSupplier = (params?: {}) => {
  return defHttp.get({ url: Api.gettoolsgetErpSupplier, params })
}
//获取库存
export const getstockList = (params?: {}) => {
  return defHttp.get({ url: Api.getstockList, params })
}
//获取库存导出
export const getstockListexcel = (params?: {}) => {
  return defHttp.get(
    { url: Api.getstockList, params, responseType: 'blob' },
    { isTransformResponse: false, successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
//审核
export const postconfirm = (params?: {}) => {
  return defHttp.post({ url: Api.postconfirm, params })
}
// 库存批量设置备货
export const setReserve = (data: { stockLists: { id: number; reserve_num: number } }) =>
  defHttp.post<{ msg: string }>({ url: Api.SetReserve, data })

// 导出
// 导出一个文件
export const exportFile = (params: {}) => defHttp.get({ url: Api.Export, params, responseType: 'blob' }, { isTransformResponse: false })
export const stockgetList = (params: {}) => defHttp.post({ url: Api.stockgetList, params })

export const updateTransform = (data) => defHttp.post({ url: Api.UpdateTransform, data })

//获取盘点包裹
export const stocktspupdate = (params: {}) =>
  defHttp.post({ url: Api.stocktspupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const stocktspsetStatus = (params: {}) =>
  defHttp.get({ url: Api.stocktspsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
