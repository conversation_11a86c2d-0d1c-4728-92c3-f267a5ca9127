import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { CreateOrEditPurchase, PurchaseOrderList, PurchaseOrderListParams, PurchaseDetail } from '/@/api/erp/modle/types'
import { otherExpend } from '../financialDocuments/modle/types'
enum Api {
  GetPurchaseOrder = '/erp/purchase/getDoc',
  // GetPayOrderList = '/erp/purchase/getpayorder',
  // getPayOrderDetail = '/erp/purchase/detail',
  CreatePurchase = '/erp/purchase/batchCreate',
  EditPurchase = '/erp/purchase/update',
  GetPurchaseDetail = '/erp/purchase/getItem',
  SetPurchaseStatus = '/erp/purchase/setStatus',
  DelPurchase = '/erp/purchase/deleteDoc',
  AddPurchase = '/erp/purchase/addClause',
  GetSubWorkPurchase = '/erp/purchase/getSubWork',
  //退款
  GetPurChildRefund = '/erp/purchase/getPurChildRefund',
  GetPurChildRetreat = '/erp/purchase/getPurChildRetreat',
  //旧erp附件上传
  GetupdateFiles = '/erp/purchase/updateFiles',
  ImportAgr = '/AGR/importCheck',
  SubmitAgr = '/AGR/createPurchaseEw',
  GetAgrItemRequest = '/AGR/getItemRequest',
  //传输金蝶
  PostsendToJd = '/erp/purchase/sendToJd',
  //传输金蝶
  updateWorksAudit = '/erp/finance/ws/updateWorksAudit',
  //流水调拨
  postpfundAllot = '/erp/purchase/fundAllot',
  //获取确认
  getsetApprove = '/erp/purchase/setApprove',
  //一键退货
  createandRetreat = '/erp/purchase/createandRetreat',
  //购销合同
  explodeContract = '/erp/purchase/explodeContract',
  //质检特批
  setisQualityApprovede = '/erp/purchase/setIsQualityApproved',
  //结算状态
  setIsAudit = '/erp/purchase/setIsAudit',
  //gbuilder
  setGbuiderStatus = '/erp/purchase/setGbuiderStatus',
  cpgetList = '/erp/cp/getList',
  purchasecreateMaleSelf = '/erp/purchase/createMaleSelf',
  purchasegetMaleSelfDoc = '/erp/purchase/getMaleSelfDoc'
}

//is_commission是用来区分是否是采购订单结算查询页面的标识,传1代表是
export const getPurchaseOrderList = (params?: PurchaseOrderListParams, isExcel = false) =>
  defHttp.get<BasicFetchResult<PurchaseOrderList>>(
    {
      url: Api.GetPurchaseOrder,
      params,
      responseType: !isExcel ? 'json' : 'blob'
    },
    { isTransformResponse: !isExcel }
  )

export const getPurchaseSelectList = (params?: PurchaseOrderListParams) =>
  defHttp
    .get<BasicFetchResult<PurchaseOrderList>>({
      url: Api.GetPurchaseOrder,
      params
    })
    .then((res) => {
      return {
        ...res,
        items: res.items.map((item) => ({ ...item, label: `${item.strid} - ${item.supplier_name}` }))
      }
    })

// export const getPayOrderList = (params?: {}) => defHttp.get<BasicFetchResult<any>>({ url: Api.GetPayOrderList, params })

// export const getPurchaseOrderDetail = (params?: {}) => defHttp.get<BasicFetchResult<any>>({ url: Api.getPayOrderDetail, params })

export const createPurchase = (data: CreateOrEditPurchase) => defHttp.post<{ code: number; msg: string }>({ url: Api.CreatePurchase, data })

export const editPurchase = (data: CreateOrEditPurchase) => defHttp.post<{ code: number; msg: string }>({ url: Api.EditPurchase, data })

export const getPurchaseDetail = (params: { doc_id: number; pageSize?: number; is_have_qr?: number; status?: number }) =>
  defHttp.get<BasicFetchResult<PurchaseDetail>>({ url: Api.GetPurchaseDetail, params })

export const setPurchaseStatus = (params: { id: number; status: number }) => defHttp.get({ url: Api.SetPurchaseStatus, params })

export const delPurchase = (id: number) => defHttp.get({ url: Api.DelPurchase, params: { id } })
export const addPurchase = (params) => defHttp.get({ url: Api.AddPurchase, params })
export const getSubWorkPurchase = (params) => defHttp.get({ url: Api.GetSubWorkPurchase, params })
export const getPurChildRefund = (params) => defHttp.get({ url: Api.GetPurChildRefund, params })
export const getPurChildRetreat = (params) => defHttp.get({ url: Api.GetPurChildRetreat, params })
export const GetupdateFiles = (params) => defHttp.post<BasicFetchResult<otherExpend>>({ url: Api.GetupdateFiles, params })

// 导入采购入库检查匹配
export const importAgr = (params: {}) => defHttp.post({ url: Api.ImportAgr, params }, { errorMessageMode: 'message' })
// 创建采购和入库
export const submitAgr = (params: {}) =>
  defHttp.post({ url: Api.SubmitAgr, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//通过puid搜索AGR产品
export const getAgrItemRequest = (params: { puid: string }) => defHttp.get({ url: Api.GetAgrItemRequest, params })
export const postsendToJd = (params: {}) => defHttp.post({ url: Api.PostsendToJd, params })
//计算
export const updateWorksAudit = (params: any) => defHttp.post({ url: Api.updateWorksAudit, params }, { successMessageMode: 'message' })

export const postpfundAllot = (params: any) =>
  defHttp.post({ url: Api.postpfundAllot, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//主管审批
export const getsetApprove = (params: any) => defHttp.get({ url: Api.getsetApprove, params }, { successMessageMode: 'message' })
//一键退货
export const createandRetreat = (params: any) => defHttp.post({ url: Api.createandRetreat, params })
//购销合同
export const explodeContract = (istree, params) =>
  defHttp.get({ url: Api.explodeContract, params, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })
//主管审批
export const setisQualityApprovede = (params: any) =>
  defHttp.get({ url: Api.setisQualityApprovede, params }, { successMessageMode: 'message' })
//结算状态
export const setIsAudit = (params: any) =>
  defHttp.get({ url: Api.setIsAudit, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//gbuilder状态
export const setGbuiderStatus = (params: any) =>
  defHttp.get({ url: Api.setGbuiderStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const cpgetList = (params?: any) => defHttp.get({ url: Api.cpgetList, params })
export const purchasecreateMaleSelf = (params?: any) =>
  defHttp.post({ url: Api.purchasecreateMaleSelf, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const purchasegetMaleSelfDoc = (params?: any) => defHttp.get({ url: Api.purchasegetMaleSelfDoc, params })
