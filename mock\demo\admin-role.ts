// // import { Random } from 'mockjs'
// // import { MockMethod } from 'vite-plugin-mock'
// import { resultPageSuccess, resultSuccess } from '../_util'
// // import { resultError, resultPageSuccess, resultSuccess } from '../_util'

// const roleList = (() => {
//   const result: any[] = []
//   for (let index = 0; index < 4; index++) {
//     result.push({
//       id: index + 1,
//       order_no: `${index + 1}`,
//       roleName: ['超级管理员', '管理员', '文章管理员', '普通用户'][index],
//       roleValue: '@first',
//       createTime: '@datetime',
//       remark: '@cword(10,20)',
//       menu: [[0, 1, 2], [0, 1], [0, 2], [2]][index],
//       'status|1': [0, 1]
//     })
//   }
//   return result
// })()

// export default [
//   {
//     url: '/api/role/update',
//     timeout: 500,
//     method: 'post',
//     response: ({ query }) => {
//       const { id, status } = query
//       return resultSuccess({ id, status })
//     }
//   },
//   {
//     url: '/api/role/getList',
//     timeout: 100,
//     method: 'get',
//     response: () => {
//       return resultSuccess(roleList)
//     }
//   },
//   {
//     url: '/api/role/getPageList',
//     timeout: 100,
//     method: 'get',
//     response: ({ query }) => {
//       const { page = 1, pageSize = 20 } = query
//       return resultPageSuccess(page, pageSize, roleList)
//     }
//   }
// ]
