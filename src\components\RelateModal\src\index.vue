<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerRelateModal" :defaultFullscreen="true" :draggable="false" @ok="closeModal">
      <Row :gutter="[16, 24]">
        <Col :span="24">
          <Alert message="请先勾选左侧操作的明细数据，在选择右侧进行具体快速操作" type="info" show-icon />
        </Col>
        <Col :span="12" :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <Divider orientation="left"><span class="divider-title">商品明细</span></Divider>
          <BasicTable @register="registerRelateTable" />
        </Col>
        <Col :span="1" :xs="1" :sm="1" :md="1" :lg="1" :xl="1" />
        <Col :span="11" :xs="23" :sm="11" :md="11" :lg="11" :xl="11">
          <slot name="rightContainer" v-bind="{ selectData }"></slot>
        </Col>
      </Row>
    </BasicModal>
  </div>
</template>

<script setup lang="ts" name="RelateModal">
/**
 * RelateModal
 * @description 关联组件; 注册需要modal方式先注册才可正常使用
 * @open open回调函数传入 leftTableData 和 leftTableColumns 左边的表格列和数据
 */
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable, EditRecordRow } from '/@/components/Table'
import { Row, Col, Alert, Divider } from 'ant-design-vue'
// import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'

const selectData = ref<EditRecordRow[]>([])

const [registerRelateModal, { closeModal }] = useModalInner((data) => {
  console.log(data)
  const { leftTableData, leftTableColumns } = data
  setTableData(leftTableData)
  setColumns(leftTableColumns)
})

const [registerRelateTable, { setTableData, setColumns }] = useTable({
  showIndexColumn: false,
  columns: [],
  pagination: false,
  scroll: { y: '65vh' },
  rowKey: 'id',
  rowSelection: {
    fixed: true,
    type: 'checkbox',
    onChange: (_selectedRowKeys, selectedRows) => {
      selectData.value = selectedRows
    }
  }
})
</script>

<style scoped lang="less">
.col-tips {
  background: #f5f5f5;
  font-size: 16px;
  font-weight: 500;
  color: red;
  padding: 15px 30px;
}

.divider-title {
  font-weight: 900;
}
</style>
