import { DefaultOptionType } from 'ant-design-vue/lib/vc-tree-select/TreeSelect'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { Rule } from 'ant-design-vue/lib/form'
import { getClientList, getWorkList } from '/@/api/commonUtils'
import type { FormSchema, BasicColumn } from '/@/components/Table'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getStaffList } from '/@/api/baseData/staff'
import { IRequest } from './types'
import { getRmbquot } from '/@/api/erp/sales'
import { mul, sub } from '/@/utils/math'
import dayjs, { Dayjs } from 'dayjs'
import { isNullOrUnDef } from '/@/utils/is'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'

//订单类型和状态
const saleStore = useSaleOrderStore()
// export const mapOrderStatusType: { [key: number | string]: string } = {
//   1: '正常订单',
//   2: '补货订单',
//   3: '销售订单',
//   4: '采购订单',
//   5: '入库订单',
//   20: '佣金',
//   21: '共摊',
//   22: '手续费',
//   23: '个人报销',
//   24: '款项支出',
//   25: '财务费用',
//   30: '其他'
// }
const originalDate = new Date(dayjs().format('YYYY-MM-DD'))
originalDate.setDate(originalDate.getDate() + 10)
const newDateString = originalDate.toISOString().split('T')[0]
const disableddate = [dayjs().format('YYYY-MM-DD'), newDateString]

export const schemas: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '单号：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.source_uniqid
  },
  {
    field: 'status',
    label: '订单状态：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => saleStore.saleStatus[model.status]
  },
  {
    field: 'department',
    label: '部门：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.department
  },
  {
    field: 'client_name',
    label: '客户名称：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.client_name
  },
  {
    field: 'creator_name',
    label: '创建人：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.creator_name
  },
  {
    field: 'auditor_name',
    label: '审核人：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.auditor_name ? model.auditor_name : '-----')
  },
  {
    field: 'inCharge_name',
    label: '负责人：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.inCharge_name
  },
  {
    field: 'source',
    label: '销售渠道：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.source
  },
  {
    field: 'foreign_currency_amount',
    label: '外汇总额:：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.foreign_currency_amount
  },
  {
    field: 'receivable_left',
    label: '应收金额：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => `￥ ${model.receivable_left}`
  },
  {
    field: 'received_actual',
    label: '实收金额：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.received_actual ? `￥ ${model.received_actual}` : '-----')
  },
  {
    label: '佣金',
    field: 'commission',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.commission ? `￥ ${model.commission}` : '-----')
  },
  {
    field: 'submited_at',
    label: '开单时间：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => model.created_at
  },
  {
    field: 'ach_app_at',
    label: '业绩核定日期',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.ach_app_at ? model.ach_app_at : '-----')
  },
  {
    field: 'audit_at',
    label: '结算时间：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.audit_at ? model.audit_at : '-----')
  },
  {
    field: 'remark',
    label: '备注：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.remark ? model.remark : '-----')
  },
  {
    field: 'finance_remark',
    label: '财务备注：',
    component: 'Input',
    colProps: { span: 8 },
    render: ({ model }) => (model.finance_remark ? model.finance_remark : '-----')
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    colProps: { span: 8 },
    slot: 'FilesSlot'
  },
  {
    field: 'relate_order',
    label: '订单产品',
    component: 'Checkbox',
    slot: 'relateOrder'
  }
]

//详情订单产品
export const drawerTableColumns: BasicColumn[] = [
  {
    title: '产品名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    key: 'imgs',
    width: 300,
    resizable: true
  },
  {
    title: '是否拆分完成',
    dataIndex: 'is_finish_split',
    key: 'is_finish_split',
    resizable: true,
    width: 250,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'green' : 'red' }, text == 1 ? '是' : '否')
    }
  },
  {
    title: '所在空间',
    dataIndex: 'location_space',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text || '--'
    }
  },
  {
    title: '长度(CM)',
    dataIndex: 'length',
    key: 'length',
    width: 100
  },
  {
    title: '宽度(CM)',
    dataIndex: 'width',
    key: 'width',
    width: 100
  },
  {
    title: '高度(CM)',
    dataIndex: 'height',
    key: 'height',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc',
    width: 120,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => saleStore.saleType[text]
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    key: 'puid',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (text ? text : '-')
  },
  {
    title: '产品型号',
    dataIndex: 'parent_uniqid',
    key: 'puid',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (text ? text : '-')
  },
  {
    title: '产品唯一码',
    dataIndex: 'uniqid',
    key: 'uniqid',
    resizable: true,
    width: 250
  },

  {
    title: '批号',
    dataIndex: 'batch_code',
    resizable: true,
    width: 150
  },
  {
    title: '原数量',
    dataIndex: 'qty_request_org',
    key: 'qty_request_org',
    width: 100
  },
  {
    title: '订单需求数量',
    dataIndex: 'qty_request',
    key: 'qty_request',
    width: 150
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    key: 'qty_request_actual',
    width: 150
  },
  {
    title: '订单剩余数量',
    dataIndex: 'qty_request_left',
    key: 'qty_request_left',
    width: 150
  },
  {
    title: '原外汇单价',
    dataIndex: 'foreign_currency_unit_pirce',
    key: 'unit_price',
    width: 150,
    customRender: ({ value }) => {
      return formateerNotCurrency.format(value)
    }
  },
  {
    title: '原单价(人民币)',
    dataIndex: 'unit_price_org',
    key: 'unit_price',
    width: 100,
    customRender: ({ value }) => {
      return formateerNotCurrency.format(value)
    }
  },
  {
    title: '单价(人民币)',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 100,
    customRender: ({ value }) => {
      return formateerNotCurrency.format(value)
    }
  },
  {
    title: '批号',
    dataIndex: 'batch_code',
    key: 'batch_code',
    width: 200
  },
  {
    title: '总价金额',
    dataIndex: 'total_amount',
    key: 'total_amount',
    width: 100,
    customRender: ({ record }) => {
      if (record.qty_request && record.unit_price) {
        return formateerNotCurrency.format(mul(record.qty_request, record.unit_price))
      } else {
        return '-----'
      }
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 120,
    resizable: true
  }
]

export const getSchemasList = (updateSchema: Function, type?: any): FormSchema[] => [
  {
    field: 'name',
    label: '订单名称',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'source',
    label: '订单渠道',
    component: 'Input',
    required: true,
    dynamicDisabled: ['split', 'edit'].includes(type)
  },
  {
    field: 'source_uniqid',
    label: '订单渠道单号',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'client',
    label: '客户',
    component: 'ApiSelect',
    componentProps: ({}) => ({
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        // disabled: true,
        labelInValue: true,
        disabled: ['split', 'edit'].includes(type)
      },
      // onChange: async () => {
      //   try {
      //     await formActionType.validateFields!(['client'])
      //   } catch (e) {
      //     console.log(e)
      //   }
      // },
      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        treeDefaultExpandAll: true,
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      },
      disabled: ['split', 'edit'].includes(type)
    },
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    required: true,
    componentProps: ({}) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      },
      params: {
        pageSize: 9999
      }
      // onChange: async () => {
      //   try {
      //     await formActionType!.validateFields!(['inCharge'])
      //   } catch (e) {
      //     console.log(e)
      //   }
      // }
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 12 },
    dynamicDisabled: ['split', 'edit'].includes(type)
  },
  {
    field: 'currency',
    label: '货币',
    component: 'Input',
    required: true,
    dynamicDisabled: ['split', 'edit'].includes(type)
  },
  {
    field: 'receivable',
    label: '应收金额',
    component: 'InputNumber',
    componentProps: {
      precision: 2
    },
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'exchange_rate',
    label: '汇率',
    component: 'ApiSelect',
    defaultValue: '1.0000',
    required: true,
    componentProps: {
      api: getRmbquot,
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'fBuyPri', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: ['split', 'edit'].includes(type)
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'submited_at',
    component: 'DatePicker',
    label: '开单日期',
    componentProps: {
      showTime: true,
      style: { width: '100%' },
      disabled: ['split', 'edit'].includes(type)
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    dynamicDisabled: type === 'split'
    // required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'filesSlot'
  },
  // {
  //   field: 'is_finish_split',
  //   label: '是否完成产品拆分',
  //   component: 'Select',
  //   required: true,
  //   defaultValue: 1,
  //   componentProps: {
  //     options: [
  //       {
  //         label: '是',
  //         value: 1
  //       },
  //       {
  //         label: '否',
  //         value: 0
  //       }
  //     ],
  //     onChange: async (val) => {
  //       await updateSchema({
  //         field: 'purchase_est_finish_at',
  //         required: val == 1,
  //         show: val == 1
  //       })
  //     }
  //   },
  //   labelWidth: 150,
  //   show: type == 'split',
  //   ifShow: type == 'split',
  //   dynamicDisabled: type === 'split' ? true : false
  // },
  {
    field: 'purchase_est_finish_at',
    label: '采购需求预计完成日期',
    component: 'DatePicker',
    required: true,
    defaultValue: dayjs().add(3, 'day').format('YYYY-MM-DD'),
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
      disabledDate: (current: Dayjs) => {
        if (!disableddate || !Array.isArray(disableddate) || disableddate.length !== 2) {
          return false
        }
        const tooLate = disableddate[0] && current.diff(dayjs(disableddate[0]), 'days') > 10
        const tooEarly = disableddate[1] && dayjs(disableddate[1]).diff(current, 'days') > 10
        return tooLate || tooEarly
      }
    },
    labelWidth: 200,
    show: type == 'split',
    ifShow: type == 'split',
    dynamicDisabled: type === 'split'
  },
  {
    field: 'relate_order',
    label: '订单产品列表',
    component: 'Checkbox',
    slot: 'relateOrderSlot',
    colProps: { span: 24 },
    rules: [{ required: true, validator: validateEditRelateOrder }]
  }
]

function validateEditRelateOrder(_rule: Rule, value: IRequest[]) {
  const validResult = value.every((item) => !!item.name && item.qty_request > 0)
  if (!validResult) return Promise.reject('请完善订单产品列表内的信息')
  return Promise.resolve()
}

export const getDrawerTableColumns = (): BasicColumn[] => [
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true
  },
  {
    title: '是否完成拆分',
    dataIndex: 'is_finish_split',
    key: 'is_finish_split',
    resizable: true,
    width: 150
  },
  {
    title: '原单价',
    dataIndex: 'unit_price_org',
    key: 'unit_price',
    width: 100,
    customRender: ({ value }) => {
      return formateerNotCurrency.format(value)
    }
  },

  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 150,
    resizable: true
  },

  {
    title: '原数量',
    dataIndex: 'qty_request_org',
    key: 'qty_request_org',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'qty_request',
    width: 100,
    resizable: true
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    key: 'qty_request_actual',
    width: 100
  },
  {
    title: '长度(CM)',
    dataIndex: 'length',
    key: 'length',
    width: 100
  },
  {
    title: '宽度(CM)',
    dataIndex: 'width',
    key: 'width',
    width: 100
  },
  {
    title: '高度(CM)',
    dataIndex: 'height',
    key: 'height',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 120,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    key: 'imgs',
    width: 300
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    customRender: ({ text }) => saleStore.saleType[text]
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    key: 'puid',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (text ? text : '-')
  },
  {
    title: '产品唯一码',
    dataIndex: 'uniqid',
    key: 'uniqid',
    resizable: true,
    width: 150
  },
  {
    title: '批号',
    dataIndex: 'batch_code',
    key: 'batch_code',
    width: 200
  },
  {
    title: '总价金额',
    dataIndex: 'total_amount',
    key: 'total_amount',
    width: 100,
    customRender: ({ record }) => {
      if (record.qty_request && record.unit_price) {
        return formateerNotCurrency.format(mul(record.qty_request, record.unit_price))
      } else {
        return '-----'
      }
    }
  },
  {
    title: '所在空间',
    dataIndex: 'location_space',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text || '--'
    }
  }
]

//流水调拨
export function getFormSchemas(order: number, hand: Function): FormSchema[] {
  return [
    {
      field: 'work_id',
      label: order == 1 ? '销售订单' : '采购订单',
      itemProps: {
        validateTrigger: 'blur'
      },
      component: 'PagingApiSelect',
      componentProps: {
        resultField: 'items',
        api: getWorkList,
        params: {
          status: [1, 2, 3, 4, 5, 15],
          type: order == 1 ? 3 : 4,
          auth: 4
        },
        searchMode: true,
        pagingMode: true,
        // returnParamsField: 'id',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: order == 1 ? 'source_uniqid' : 'purchase_strid' },
          optionFilterProp: order == 1 ? 'source_uniqid' : 'purchase_strid',
          showSearch: true,
          placeholder: '请选择'
        },
        disabled: true
      },
      required: true,
      labelWidth: 70
    },
    {
      field: 'to_work_id',
      label: '调入订单',
      itemProps: {
        validateTrigger: 'blur'
      },
      component: 'PagingApiSelect',
      componentProps: {
        resultField: 'items',
        api: getWorkList,
        params: {
          status: [1, 2, 3, 4, 5, 15],
          types: order == 1 ? [3, 27] : [4],
          is_audit_no: 1,
          auth: 4
        },
        searchMode: true,
        pagingMode: true,
        // returnParamsField: 'id',
        searchParamField: order == 1 ? 'source_uniqid' : 'strid',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: order == 1 ? 'source_uniqid' : 'purchase_strid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: order == 1 ? 'source_uniqid' : 'purchase_strid'
        },
        onChange(_, shall) {
          if (!shall) return
          hand && hand(shall)
        }
      },
      required: true,
      labelWidth: 70
    },
    {
      field: 'amount',
      label: '流水金额',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        disabled: true
      },
      required: true
    },
    {
      field: 'payment_type',
      label: '调入订单款项类型',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '定金',
            value: 1
          },
          {
            label: '最后一笔款',
            value: 2
          },
          {
            label: '全款',
            value: 3
          }
        ]
      },
      required: true
    },
    {
      field: 'is_audit',
      label: '调出订单新的结算状态',
      itemHelpMessage: '未结算:该调入单流水调拨后还可继续收款 待结算:该调入单流水调拨后不可继续收款了(已收齐款)  请谨慎操作。',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '未结算',
            value: 0
          },
          {
            label: '待结算',
            value: 2
          }
        ]
      },
      required: true
    },
    {
      field: 'payment',
      label: '流水款单',
      component: 'Divider',
      colProps: { span: 24 }
    }
  ]
}
/** 抽屉子表格 */
export function childColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'fund_id',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '流水单号',
      dataIndex: 'fund_strid',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '手续费',
      dataIndex: 'fee',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '-'
      }
    },
    {
      title: '剩余金额',
      dataIndex: 'amount_left',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '-'
      }
    },
    {
      title: '可回退金额',
      dataIndex: 'amount_allot',
      resizable: true,
      width: 100,
      customRender: ({ record }) => {
        return sub(record.amount_allot, record.amount_fund)
          ? formateerNotCurrency.format(sub(record.amount_allot, record.total_amount_fund))
          : '0.00'
      }
    },
    {
      title: '已回退金额',
      dataIndex: 'total_amount_fund',
      resizable: true,
      width: 100,
      customRender: ({ text }) => {
        return text ?? '0.00'
      }
    },
    {
      title: '本次回退金额',
      dataIndex: 'amount_fund',
      editRow: true,
      editComponent: 'InputNumber',
      width: 100,
      editComponentProps: ({ record }) => {
        return {
          precision: 2,
          max: formateerNotCurrency.format(sub(record.amount_allot, record.total_amount_fund)),
          min: 0,
          onChange: (val) => {
            console.log(val)
          }
        }
      },
      edit: true
    }
  ]
}
//子产品tablecolum
export const tablecolum = (type?: string): BasicColumn[] => [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'work_id',
    dataIndex: 'work_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '种类占比',
    dataIndex: 'proportion_org',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + '%' : '-'
    }
  },
  {
    title: '单个sku占比',
    dataIndex: 'proportion',
    width: 100,
    resizable: true
    // customRender: ({ text }) => {
    //   return text ? text + '%' : '-'
    // }
    // defaultHidden: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: type == 'retreat' ? '剩余可退货数量' : '剩余数量',
    dataIndex: 'quantity_left',
    width: 100,
    resizable: true
  },
  {
    title: '长度(CM)',
    dataIndex: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '宽度(CM)',
    dataIndex: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '高度(CM)',
    dataIndex: 'height',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 250,
    resizable: true
  },
  {
    title: 'type',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    defaultHidden: true
  }
]
//子产品表单
export const getSchemassplit = (): FormSchema[] => [
  {
    field: 'name',
    label: '订单名称',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'source',
    label: '订单渠道',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'source_uniqid',
    label: '订单渠道单号',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'client',
    label: '客户',
    component: 'ApiSelect',
    componentProps: ({}) => ({
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true,
        labelInValue: true
      },
      // onChange: async () => {
      //   try {
      //     await formActionType.validateFields!(['client'])
      //   } catch (e) {
      //     console.log(e)
      //   }
      // },
      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    dynamicDisabled: true,
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
      // disabled: !isUpdate
    },
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    required: true,
    dynamicDisabled: true,
    componentProps: ({}) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      },
      params: {
        pageSize: 9999
      }
      // onChange: async () => {
      //   try {
      //     await formActionType!.validateFields!(['inCharge'])
      //   } catch (e) {
      //     console.log(e)
      //   }
      // }
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 12 }
  },
  {
    field: 'currency',
    label: '货币',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'receivable',
    label: '应收金额',
    component: 'InputNumber',
    componentProps: {
      precision: 2
    },
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'exchange_rate',
    label: '汇率',
    component: 'ApiSelect',
    defaultValue: '1.0000',
    required: true,
    componentProps: {
      api: getRmbquot,
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'bankConversionPri', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'submited_at',
    component: 'DatePicker',
    label: '开单日期',
    componentProps: {
      showTime: true,
      style: { width: '100%' }
    },
    dynamicDisabled: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    dynamicDisabled: true
    // required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'filesSlot'
  },
  {
    field: 'relate_order',
    label: '订单产品列表',
    component: 'Checkbox',
    slot: 'relateOrderSlot',
    colProps: { span: 24 },
    rules: [{ required: true, validator: validateEditRelateOrder }]
  }
]
