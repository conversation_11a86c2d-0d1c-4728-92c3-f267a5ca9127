import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'

const taskList = (() => {
  const result: any[] = []
  for (let index = 0; index < 3; index++) {
    result.push({
      id: `${Math.floor(Math.random() * 1000)}`,
      type: index + 1,
      name: '这是个标题这是个标题这是个标题这是个标题这是个标题这是个标题这是个标题这是个标题',
      status: 1,
      inCharge: { label: '张三', value: 0 },
      beginTime: '2023-01-01 00:00',
      endTime: '2024-01-01 00:00',
      priority: index,
      subCount: index,
      parentId: `${Math.floor(Math.random() * 1000)}`,
      children: []
    })
  }

  return result
})()

export default [
  {
    url: '/api/task/getList',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(taskList)
    }
  }
] as MockMethod[]
