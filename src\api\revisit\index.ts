import type { BasicPageParams } from '../model/baseModel'
import type { IGetProjectList, IUpdateRevisit } from '../model/revisit'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetProjectAccount = '/erp/cm/getAccount',
  SetProjectAccount = '/erp/cm/setAccount',
  GetProjectList = '/erp/project/pro/getList',
  UpdateFollowAt = '/erp/project/pro/setFollowUpAt',
  UpdateDeliveryAt = '/erp/project/pro/setSaleDeliverAt',
  UpdateRevisitRecord = '/erp/project/pro/update',
  GetDeliveryLogList = '/erp/project/pro/getSaleDeliverList',
  getevaluateList = '/erp/rating/getList',
  getevaluatedetail = '/erp/rating/detail',
  SetIsRead = '/erp/project/pro/setIsRead',
  CreateRevisitLog = '/erp/project/pro/addProjectFollow',
  GetRevisitLog = '/erp/project/pro/getProjectFollowList',
  GetStarTop = '/account/getStarTop',
  GetDeptList = '/erp/si/getDept'
}

//获取项目列表
export const getProjectList = (params?: BasicPageParams & Partial<IGetProjectList>) => {
  return defHttp.get({
    url: Api.GetProjectList,
    params
  })
}

// 查看项目账号
export const getProjectAccount = (params) => defHttp.get({ url: Api.GetProjectAccount, params })

// 生成项目账号
export const setProjectAccount = (data) => defHttp.post({ url: Api.SetProjectAccount, data })

//设置下次回访日期
export const updateFollowAt = (params: { id: number }) => {
  return defHttp.get({ url: Api.UpdateFollowAt, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}

//设置交付日期
export const updateDeliveryAt = (params: { id: number; deliver_at: Date }) => {
  return defHttp.get({ url: Api.UpdateDeliveryAt, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}

//更新回访记录
export const UpdateRevisitRecord = (params: { id: number & IUpdateRevisit }) => {
  return defHttp.post({ url: Api.UpdateRevisitRecord, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}

//交付日期变更日志列表
export const getDeliveryLogList = (params: { project_number?: number; work_id?: number }) => {
  return defHttp.get({ url: Api.GetDeliveryLogList, params })
}

//回访评价列表
export const getevaluateList = (params: {}) => {
  return defHttp.get({ url: Api.getevaluateList, params })
}

//回访评价列表
export const getevaluatedetail = (params: {}) => {
  return defHttp.get({ url: Api.getevaluatedetail, params })
}

//设置是否已读
export const setIsRead = (params: { project_number?: number; sale_work_id?: number }) => {
  return defHttp.get({ url: Api.SetIsRead, params }, { errorMessageMode: 'message' })
}

//项目回访日志添加
export const createRevisitLog = (params?: {}) => {
  return defHttp.post({ url: Api.CreateRevisitLog, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}

//项目回访日志列表
export const getRevisitLog = (params: { project_number: string }) => {
  return defHttp.get({ url: Api.GetRevisitLog, params })
}

// 评价排行
export const getStarTop = (params?: {}) => defHttp.get({ url: Api.GetStarTop, params }, { isTransformResponse: false })
//获取业务部门
export const getOperateDeptList = (params?) => {
  return defHttp.get({ url: Api.GetDeptList, params }).then((res) => {
    return {
      items: Object.values(res.items)
        .filter((item: Recordable) => {
          return item.isAudit === 1
        })
        .map((item: Recordable) => {
          return { ...item, key: item.id, label: item.name }
        })
    }
  })
}
