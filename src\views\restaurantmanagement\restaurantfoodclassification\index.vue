<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="{ no_cache: 0 }">
      <template #toolbar>
        <Button
          type="primary"
          @click="
            () => {
              reload()
            }
          "
          >更新数据</Button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup>
import { columns, schemas } from './datas/datas'
import { mfgetShopCategory } from '/@/api/restaurantmanagement/restaurantfoodclassification'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { Button } from 'ant-design-vue'

const [registerTable, { reload }] = useTable({
  useSearchForm: true,
  showTableSetting: true,
  columns,
  api: mfgetShopCategory,
  showIndexColumn: false,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1
  }
})
</script>
