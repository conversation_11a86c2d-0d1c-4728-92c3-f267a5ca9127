import { defHttp } from '/@/utils/http/axios'

enum Api {
  ratingcomplaintupdate = '/erp/rating/complaint/update',
  ratingcomplaintgetList = '/erp/rating/complaint/getList',
  ratingcomplaintsetStatus = '/erp/rating/complaint/setStatus',
  ratingcomplaintaddFollowLog = '/erp/rating/complaint/addFollowLog',
  ratingsupplementgetList = '/erp/rating/supplement/getList',
  ratingcomplaintaddCommissionerFollowLog = '/erp/rating/complaint/addCommissionerFollowLog',
  emailmessagegetList = '/erp/email/message/getList',
  emailmessagebindProject = '/erp/email/message/bindProject',
  ratingcomplaintsetCancel = '/erp/rating/complaint/setCancel',
  emailmessagesetCancel = '/erp/email/message/setCancel'
}

// 导出一个名为ratingfeedbackupdate的函数，该函数接收一个参数params
export const ratingcomplaintupdate = (params) =>
  // 使用defHttp.post方法发送post请求，请求的url为Api.ratingfeedbackupdate，请求的参数为params
  defHttp.post({ url: Api.ratingcomplaintupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingcomplaintgetList = (params) => defHttp.get({ url: Api.ratingcomplaintgetList, params })
export const ratingcomplaintsetStatus = (params) =>
  defHttp.post({ url: Api.ratingcomplaintsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingcomplaintaddFollowLog = (params) =>
  defHttp.post({ url: Api.ratingcomplaintaddFollowLog, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingsupplementgetList = (params) => defHttp.get({ url: Api.ratingsupplementgetList, params })
export const ratingcomplaintaddCommissionerFollowLog = (params) =>
  defHttp.post({ url: Api.ratingcomplaintaddCommissionerFollowLog, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
//邮件
export const emailmessagegetList = (params) => defHttp.get({ url: Api.emailmessagegetList, params })
export const emailmessagebindProject = (params) =>
  defHttp.post({ url: Api.emailmessagebindProject, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const ratingcomplaintsetCancel = (params) =>
  defHttp.post({ url: Api.ratingcomplaintsetCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const emailmessagesetCancel = (params) =>
  defHttp.post({ url: Api.emailmessagesetCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
