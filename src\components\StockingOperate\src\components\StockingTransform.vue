<template>
  <BasicForm @register="registerForm">
    <template #transformDetail="{ model }">
      <FormItemRest>
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'name' && model.transformDetail">
              <!-- <span>{{ model.transformDetail[index].name }}</span> -->
              <Input v-if="model.transformDetail[index]" v-model:value="model.transformDetail[index].name" />
            </template>
            <template v-if="column.dataIndex === 'origin_name' && model.transformDetail">
              <span>{{ model.transformDetail[index].origin_name }}</span>
              <!-- <Input v-if="model.transformDetail[index]" v-model:value="model.transformDetail[index].origin_name" /> -->
            </template>
            <template v-if="column.dataIndex === 'source_uniqid' && model.transformDetail">
              <!--              <ApiSelect-->
              <!--                placeholder="请选择订单"-->
              <!--                :api="request.bind(null, record.work_id)"-->
              <!--                v-if="model.transformDetail[index]"-->
              <!--                :params="{ strid: record.work_strid }"-->
              <!--                v-model:value="model.transformDetail[index].work_id"-->
              <!--                :select-props="{ fieldNames: { value: 'id', label: 'source_uniqid' }, placeholder: '请选择' }"-->
              <!--                resultField="items"-->
              <!--                :always-load="false"-->
              <!--                @change="() => (model.transformDetail[index].request_id = void 0)"-->
              <!--              />-->
              <Select
                v-if="model.transformDetail[index]"
                v-model:value="model.transformDetail[index].work_id"
                placeholder="先输入销售订单进行搜索"
                :filterOption="false"
                :fieldNames="{ key: 'id', value: 'id', label: 'source_uniqid' }"
                :showSearch="true"
                :options="salesWorkList"
                @search="(val) => debounce(searchSalesWork, 200)(val)"
                @change="() => (model.transformDetail[index].request_id = void 0)"
              />
            </template>
            <template v-if="column.dataIndex === 'request_id' && model.transformDetail">
              <!-- {{ mapWorkList[record.work_id]?.strid }} -->
              <PagingApiSelect
                placeholder="请选择关联商品"
                :api="
                  (params) =>
                    model?.transformDetail[index]?.work_id && getItemRequest({ ...params, work_id: model.transformDetail[index].work_id })
                "
                v-if="model.transformDetail[index]"
                v-model:value="model.transformDetail[index].request_id"
                :select-props="{
                  fieldNames: { value: 'id', label: 'name' },
                  placeholder: '请选择'
                }"
                :pagingMode="true"
                resultField="items"
                :search-mode="true"
                :always-load="false"
                @change="
                  (_, shall) => {
                    ;(maxquantity = shall.qty_request_left), (model.transformDetail[index].quantity_need = shall.qty_request_left)
                  }
                "
              />
            </template>
            <template v-if="column.dataIndex === 'warehouse_id' && model.transformDetail">
              <!-- {{ mapStoreList[record.warehouse_id]?.name }} -->
              <PagingApiSelect
                placeholder="请选择仓库`"
                :pagingMode="true"
                :api="(params) => getWarehouse({ ...params })"
                :search-mode="true"
                :always-load="false"
                return-params-field="id"
                v-if="model.transformDetail[index]"
                v-model:value="model.transformDetail[index].warehouse_id"
                :select-props="{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' }"
                resultField="items"
              />
            </template>
            <template v-if="column.dataIndex === 'imgs'">
              <TableImg :imgList="record.imgs" :simpleShow="true" />
            </template>
            <template v-if="column.dataIndex === 'quantity' && model.transformDetail">
              <InputNumber
                :min="0.01"
                :max="model.transformDetail[index].qty_stocking"
                v-if="model.transformDetail[index]"
                v-model:value="model.transformDetail[index].quantity"
                :precision="2"
                :step="0.1"
              />
            </template>
            <template v-if="column.dataIndex === 'quantity_need' && model.transformDetail">
              <InputNumber
                :min="0.01"
                :max="maxquantity"
                v-if="model.transformDetail[index]"
                v-model:value="model.transformDetail[index].quantity_need"
                :precision="2"
                :step="0.1"
              />
            </template>
            <template v-if="column.dataIndex === 'desc' && model.transformDetail">
              <Textarea v-model:value="model.transformDetail[index].desc" :autoSize="false" />
            </template>
            <template v-if="column.dataIndex === 'remark' && model.transformDetail">
              <Textarea v-model:value="model.transformDetail[index].remark" :autoSize="false" />
            </template>
          </template>
        </BasicTable>
      </FormItemRest>
    </template>
  </BasicForm>
</template>

<script setup lang="ts" name="StockingTransform">
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { schemas, columns, salesWorkList, allSalesWorkList } from '/@/views/erp/warehouseTransfer/datas/Modal'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { Form, message } from 'ant-design-vue'
import { StockingItem } from '/@/components/StockingOperate/src/types'
import { nextTick, onMounted, ref } from 'vue'
import { getWarehouseList } from '../datas/stockingTransform.datas'
import { InputNumber, Textarea, Input, Select } from 'ant-design-vue'
import { editInventoryst } from '/@/api/erp/inventory'
import { useMessage } from '/@/hooks/web/useMessage'
import { DrawerInstance } from '/@/components/Drawer'
//下拉
import { getWorkList } from '/@/api/commonUtils/index'
import { getItemRequest } from '/@/api/commonUtils/index'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { cloneDeep, debounce } from 'lodash-es'
import defaultUser from '/@/utils/erp/defaultUser'
const maxquantity = ref(0)

getWarehouseList()

const props = withDefaults(
  defineProps<{
    stockingList: StockingItem[]
  }>(),
  {
    stockingList: () => []
  }
)
console.log(props.stockingList)

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()

const { createMessage } = useMessage()
const FormItemRest = Form.ItemRest
const [registerForm, { setFieldsValue, validate }] = useForm({
  schemas: [
    ...schemas,
    {
      field: 'transformDetail',
      label: '转换商品明细',
      component: 'Select',
      slot: 'transformDetail',
      rules: [{ required: true, validator: validateTransformDetail }]
    }
  ],
  showActionButtonGroup: false,
  colon: true
})

const [registerTable, { setTableData }] = useTable({
  columns: columns.filter((item) => !['origin_stocking_id', 'unit', 'unit_price', 'soder_qty_stocking', 'status'].includes(item.dataIndex)),
  pagination: false,
  scroll: { y: 500 },
  dataSource: []
})
onMounted(() => {
  const stockingList = ref<any>([])

  stockingList.value = cloneDeep(props.stockingList)
  stockingList.value.forEach((item) => {
    item.origin_source_uniqid = item.source_uniqid
    item.source_uniqid = undefined
    item.request_id = undefined
    item.origin_name = item.name
    item.quantity = item.qty_stocking
    // item.qty_stocking = item.qty_stocking
    item.quantity_need = 1
    item.origin_qty_stocking = item.qty_stocking
  })
  setFieldsValue({
    transformDetail: stockingList.value,
    applicant: defaultUser!.userId,
    inCharge: defaultUser!.userId,
    processor: defaultUser!.userId
  })
  nextTick(() => {
    setTableData(stockingList.value)
  })
})

function validateTransformDetail(_rule: any, value: any[]) {
  if (!value || value.length === 0) return Promise.reject('请选择转换商品')
  const validQuantityRes = value.every((item) => item.quantity > 0 && item.quantity <= item.qty_stocking)
  const validQuantityNeed = value.every((item) => item.quantity_need > 0 && item.quantity_need <= maxquantity.value)
  const validRelateRes = value.every((item) => item.work_id && item.request_id)
  if (!validRelateRes) {
    return Promise.reject('必须关联订单商品')
  }
  if (!validQuantityRes) {
    return Promise.reject('库存要转换的数量，不应超过库存数量')
  }
  if (!validQuantityNeed) {
    return Promise.reject('关联订单商品转换数量，不应超过订单需求数量')
  }
  return Promise.resolve()
}

async function submit({ changeOkLoading, closeDrawer }) {
  await changeOkLoading(true)
  try {
    const { applicant, inCharge, processor, desc, transformDetail } = await validate()

    const datalist: Array<number> = []
    transformDetail.forEach((item) => {
      if (item.work_id) {
        datalist.push(item.work_id)
      }
    })
    if (new Set(datalist).size !== 1) {
      message.error('只能选择相同的关联订单！')
      return
    }
    const items = transformDetail.map((item) => ({
      origin_stocking_id: item.id,
      work_id: item.work_id,
      request_id: item.request_id,
      // purchase_id: '',
      unit_price: item.unit_price || 0,
      unit: item.unit || '',
      name: item.name,
      puid: item.puid || '',
      imgs: item.imgs || [],
      warehouse_id: item.warehouse_id,
      pkg_num: 0,
      pkg_received: 0,
      qty_total: 0,
      qty_received: 0,
      qty_defective: 0,
      quantity_need: item.quantity_need,
      quantity: item.quantity,
      desc: item.desc,
      remark: item.remark
    }))
    console.log(transformDetail)

    const params = {
      doc: {
        applicant,
        inCharge,
        processor,
        desc
      },
      items
    }
    console.log(params)

    const result: any = await editInventoryst(params)
    console.log(result)

    if (result.type === 'success') {
      createMessage.success(result.msg)
      closeDrawer()
      emits('success')
    } else if (result.type === 'error') {
      createMessage.error(result.message)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
//销售订单
// async function request(_, params) {
//   const req = await getWorkList({ type: 3, pageSize: 99999, item_left: 3, status: [1, 2, 3, 4, 5] })
//   const newlist = req.items.filter((item) => item.strid !== params.strid)
//   return newlist
// }

async function searchSalesWork(val) {
  if (!val) return
  const { items } = await getWorkList({ item_left: 1, source_uniqid: val, type: 3, status: [2, 3, 4, 5], pageSize: 9999, order: 1 })
  salesWorkList.value = items
  allSalesWorkList.value = [...salesWorkList.value, ...items]
}

defineExpose({
  submit
})
</script>

<style scoped lang="less"></style>
