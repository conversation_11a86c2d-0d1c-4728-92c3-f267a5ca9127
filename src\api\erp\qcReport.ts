import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel'
import { IGetQcReport, QualityDetectionReportItem } from './modle/qcReportModel'

enum Api {
  GetQcReport = '/erp/qc/getList'
}

export const getQcReport = (params?: Partial<IGetQcReport> & Partial<BasicPageParams>) =>
  defHttp.get<BasicFetchResult<QualityDetectionReportItem>>({ url: Api.GetQcReport, params })
