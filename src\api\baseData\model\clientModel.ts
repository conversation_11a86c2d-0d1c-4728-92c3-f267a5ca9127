import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'

export type ClientParams = BasicPageParams & {
  name?: string
  id?: string
  email?: string
  contact?: string
  location?: string
  remark?: string
  id?: number
}

// export type AccountListGetResultModel = BasicFetchResult<AccountListItem>
export type ClientGetResult = BasicFetchResult<{
  id: 60
  name: string
  email: null | string
  creator: null | string
  contact: string
  location: string
  remark: null | string
  created_at: string
  updated_at: string
  creator_name: string
}>

export interface CreateUpdateClientParams {
  name: string
  email?: string
  contact: string
  location?: string
  remark?: string
  id: number
}
