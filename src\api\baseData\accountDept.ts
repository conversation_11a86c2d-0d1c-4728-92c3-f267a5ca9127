import { defHttp } from '/@/utils/http/axios'

import * as accountDeptT from './model/accountDeptModel'

enum Api {
  GetAccountDeptList = '/erp/ad/get',
  GetDept = '/erp/ad/getDeptahs',
  AccountDeptRelate = '/erp/ad/relevancy'
}

export const getAccountDeptList = (params: accountDeptT.AccountDeptListParams) =>
  defHttp.get<accountDeptT.IGetAccountDeptListResult[]>({ url: Api.GetAccountDeptList, params })

export const getDept = (params?: {}) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetDept, params })

export const accountDeptRelate = (params: accountDeptT.IAccDeptRelate) =>
  defHttp.post({ url: Api.AccountDeptRelate, params }, { successMessageMode: 'message' })
