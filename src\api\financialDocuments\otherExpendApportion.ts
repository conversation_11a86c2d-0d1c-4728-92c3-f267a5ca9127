import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { otherExpend } from './modle/types'

enum Api {
  //列表
  GetOtherExpendList = '/erp/stock/ods/getList',
  //详情
  GetOtherExpenddetail = '/erp/stock/ods/detail',
  //分摊编辑
  GetOtherExpendupdate = '/erp/stock/ods/update',
  stockodsetCorresPondent = '/erp/stock/od/setCorresPondent'
}

export const getOtherExpendList = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.GetOtherExpendList, params })
export const getOtherExpenddetail = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.GetOtherExpenddetail, params })
export const getOtherExpendupdate = (params?: {}) => defHttp.post<BasicFetchResult<otherExpend>>({ url: Api.GetOtherExpendupdate, params })
export const stockodsetCorresPondent = (params?: {}) =>
  defHttp.post({ url: Api.stockodsetCorresPondent, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
