import { defHttp } from '/@/utils/http/axios'
// import { LoginResultModel, RegistResultModel, GetUserInfoModel } from './model/userModel'
import { GetUserInfoModel } from './model/userModel'
import { getMenuListResultModel } from './model/menuModel'
import { ErrorMessageMode } from '/@/types/axios'

enum Api {
  Login = '/user/login',
  Regist = '/user/regist',
  Logout = '/user/logout',
  ResetPasswd = '/user/resetPassword',
  GetUserInfo = '/user/getUserInfo',
  GetPermCode = '/user/getPermCode',
  GetMenuList = '/user/getMenuList'
}

/**
 * @description: user login api
 */
export function loginApi(
  params: {
    username: string
    password: string
  },
  mode: ErrorMessageMode = 'modal'
) {
  return defHttp.post<{
    userId: string | number
    token: string
  }>(
    {
      url: Api.Login,
      params
    },
    {
      errorMessageMode: mode
    }
  )
}

export function registApi(
  params: {
    username: string
    name: string
    password: string
    remark: string
  },
  mode: ErrorMessageMode = 'modal'
) {
  return defHttp.post<{
    userId: string | number
    username: string
    name: string
  }>(
    {
      url: Api.Regist,
      params
    },
    {
      errorMessageMode: mode
    }
  )
}

export const resetPassword = (
  params: { id: number | string; passwordOld: string; passwordNew: string },
  mode: ErrorMessageMode = 'modal'
) =>
  defHttp.post<{
    userId: string | number
  }>({ url: Api.ResetPasswd, params }, { errorMessageMode: mode })

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' })
}

export function getPermCode() {
  return defHttp.get<number[]>({ url: Api.GetPermCode })
}

export function doLogout() {
  return defHttp.get({ url: Api.Logout })
}

export const getMenuList = () => {
  return defHttp.get<getMenuListResultModel>({ url: Api.GetMenuList })
}
