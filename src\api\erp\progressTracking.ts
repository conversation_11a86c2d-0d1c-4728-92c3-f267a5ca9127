import type { BasicPageParams } from '../model/baseModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  GetProgressTracking = '/erp/project/reqLog/getList',
  Remind = '/erp/project/reqLog/urgeProcessing'
}

//获取退货单列表
export const getProgressTracking = (params?: BasicPageParams & { source_uniqid?: string; request_id: string }) =>
  defHttp.get({ url: Api.GetProgressTracking, params })

//一键催办
export const remind = (params: { work_id: number }) =>
  defHttp.get({ url: Api.Remind, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
