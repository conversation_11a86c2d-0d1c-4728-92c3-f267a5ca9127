<template>
  <TreeSelect v-bind="getAttrs" @change="handleChange" @dropdown-visible-change="handleDropdownVisibleChange">
    <template #[item]="data" v-for="item in Object.keys($slots)">
      <slot :name="item" v-bind="data || {}"></slot>
    </template>
    <template #suffixIcon v-if="loading">
      <LoadingOutlined spin />
    </template>
    <template #notFoundContent v-if="loading">
      <span>
        <LoadingOutlined spin class="mr-1" />
        {{ t('component.form.apiSelectNotFound') }}
      </span>
    </template>
  </TreeSelect>
</template>
<script lang="ts" setup name="ApiTreeSelect">
import { computed, watch, ref, onMounted, unref, useAttrs } from 'vue'
import { TreeSelect, TreeSelectProps } from 'ant-design-vue'
import { isArray, isFunction } from '/@/utils/is'
import { get, omit } from 'lodash-es'
import { propTypes } from '/@/utils/propTypes'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { useI18n } from '/@/hooks/web/useI18n'

const props = defineProps({
  api: { type: Function as PropType<(arg?: Recordable) => Promise<Recordable>> },
  params: { type: Object },
  immediate: { type: Boolean, default: true },
  resultField: propTypes.string.def(''),
  lazyLoad: propTypes.bool.def(false),
  treeSelectProps: { type: Object as PropType<TreeSelectProps> }
})
const emit = defineEmits(['options-change', 'change', 'dropdown-visible-change'])
const attrs = useAttrs()
const { t } = useI18n()
const treeData = ref<Recordable[]>([])
const isFirstLoaded = ref<Boolean>(false)
const loading = ref(false)
const getAttrs = computed(() => {
  return {
    ...(props.api ? { treeData: unref(treeData) } : {}),
    ...omit(props.treeSelectProps, ['treeData']),
    ...attrs
  }
})

function handleChange(...args) {
  emit('change', ...args)
}

watch(
  () => props.params,
  () => {
    !unref(isFirstLoaded) && fetch()
  },
  { deep: true }
)

watch(
  () => props.immediate,
  (v) => {
    v && !isFirstLoaded.value && fetch()
  }
)

onMounted(() => {
  props.immediate && fetch()
})

async function fetch() {
  const { api } = props
  if (!api || !isFunction(api)) return
  loading.value = true
  treeData.value = []
  let result
  try {
    result = await api(props.params)
  } catch (e) {
    console.error(e)
  }
  loading.value = false
  if (!result) return
  if (!isArray(result)) {
    result = get(result, props.resultField)
  }
  treeData.value = (result as Recordable[]) || []
  isFirstLoaded.value = true
  emit('options-change', treeData.value)
}

function handleDropdownVisibleChange(open: boolean) {
  // immediate：false,lazyLoad：true才会触发重新加载
  if (open && props.lazyLoad && !props.immediate) fetch()
  emit('dropdown-visible-change', open)
}
</script>
