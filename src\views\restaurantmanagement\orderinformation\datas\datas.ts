import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import dayjs from 'dayjs'

export const columns: BasicColumn[] = [
  {
    title: '订单编号',
    dataIndex: 'order_number',
    width: 120
  },
  {
    title: '结账日期',
    dataIndex: 'checkout_at',
    width: 120
  },
  {
    title: '下单时间',
    dataIndex: 'order_at',
    width: 120
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 120,
    customRender: ({ record }) => {
      return h('span', {}, record.type === 100 ? '堂食' : '外卖')
    }
  },
  {
    title: '实际付款金额',
    dataIndex: 'payed',
    width: 120
  },
  {
    title: '订单状态名称',
    dataIndex: 'status_name',
    width: 120
  },
  {
    title: '订单原价金额',
    dataIndex: 'amount',
    width: 120
  },
  {
    title: '订单优惠金额',
    dataIndex: 'discount',
    width: 120
  },
  {
    title: '订单收入金额',
    dataIndex: 'income',
    width: 120
  },
  {
    title: '订单菜品总价',
    dataIndex: 'goods_total_price',
    width: 120
  },
  {
    title: '税额',
    dataIndex: 'tax_amount',
    width: 120
  },
  {
    title: '税率',
    dataIndex: 'tax_rate',
    width: 120
  },
  {
    title: '订单来源名称',
    dataIndex: 'source',
    width: 120
  },
  {
    title: '订单子来源',
    dataIndex: 'sub_source',
    width: 120
  },
  {
    title: '桌牌号',
    dataIndex: 'desk_number',
    width: 120
  },
  {
    title: '桌台区域',
    dataIndex: 'desk_area',
    width: 120
  },
  {
    title: '取餐号',
    dataIndex: 'take_number',
    width: 120
  },
  {
    title: '订单类型名称',
    dataIndex: 'type_name',
    width: 120
  },
  {
    title: '签单部门',
    dataIndex: 'sign_department',
    width: 120
  },
  {
    title: '备注',
    dataIndex: 'comment',
    width: 120
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'data',
    label: '结账日期',
    component: 'SingleRangeDate',
    defaultValue: [dayjs().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().subtract(1, 'day').format('YYYY-MM-DD 23:59:59')],
    colProps: { span: 8 },
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'order_number',
    label: '订单单号',
    component: 'Input',
    colProps: { span: 8 }
  }
]
