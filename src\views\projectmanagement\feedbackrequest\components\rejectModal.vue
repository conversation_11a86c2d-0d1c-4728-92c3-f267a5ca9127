<template>
  <BasicModal @register="register" title="驳回" width="500px" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { ratingfeedbacksetManageReject, ratingfeedbacksetReject } from '/@/api/projectmanagement/feedbackrequest'
const types = ref()
const emit = defineEmits(['success', 'register'])
const [register, { changeLoading, closeModal }] = useModalInner((data) => {
  types.value = data.type
  resetFields()

  setProps({
    schemas: [
      {
        field: 'id',
        label: 'id',
        component: 'Input',
        show: false
      },
      {
        field: types.value == 1 ? 'status_remark' : 'manager_status_remark',
        label: '驳回原因',
        required: true,
        component: 'InputTextArea'
      }
    ]
  })
  setFieldsValue({ id: data.records.id })
})

const [registerForm, { validate, resetFields, setProps, setFieldsValue }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  baseColProps: { span: 21 }
})

async function handleOk() {
  try {
    changeLoading(true)
    const formdata = await validate()
    types.value == 1 ? await ratingfeedbacksetReject(formdata) : ratingfeedbacksetManageReject(formdata)
    changeLoading(false)
    closeModal()
    emit('success')
  } catch (e) {
    console.log(e)
  } finally {
    changeLoading(false)
  }
}
</script>
