import { defHttp } from '/@/utils/http/axios'

enum Api {
  CreateSaleOrder = '/erp/project/wkr/addSalesOrder',
  //crm创建订单
  updateOrCreateSalesOrder = '/open/wkr/updateOrCreateSalesOrder',
  crmgetProjectByCrm = '/erp/project/pro/getProjectByCrm'
}

interface IItems {
  name: string
  imgs?: string[]
  puid?: string
  unit: string
  unitPrice: number
  quantity: number
  totalAmount?: number
  desc?: string
  remark?: string
  items?: Partial<IItems>[]
}

interface IParams {
  src: string
  tpuid: string
  deptId?: string
  creator: number
  currency?: string
  exchangeRate?: string
  receivable: number
  submitedAt: string
  client: {
    name: string
    contact: string
    location: string
  }
  items: IItems[]
  deposit?: {
    name: string
    contact: string
    rate: number
    account: string
  }
}

export const createSaleOrder = (params?: IParams) =>
  defHttp.post({ url: Api.CreateSaleOrder, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const updateOrCreateSalesOrder = (params?: {}) =>
  defHttp.post({ url: Api.updateOrCreateSalesOrder, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const crmgetProjectByCrm = (params?: {}) => defHttp.get({ url: Api.crmgetProjectByCrm, params })
