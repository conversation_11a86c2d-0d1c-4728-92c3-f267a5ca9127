import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import { getDeptSelectTree } from '/@/api/admin/dept'
import dayjs from 'dayjs'
import {
  getDeptColumns,
  getDeptStatistics,
  getSalesDetailStatistics,
  getSalesPerformance,
  getOperateDept,
  getOperatePerson
} from '/@/api/Performance/statistics'
import { h, ref } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { cloneDeep } from 'lodash-es'
import { getDept } from '/@/api/erp/systemInfo'
import { getAccountList } from '/@/api/commonUtils'
import { useCacheColumns } from '/@/hooks/setting/useCacheColumns'
import { isNull } from '/@/utils/is'

export const mapTableAction = ref({})
export const dataSumGather = ref({})
// export const salesTotal = ref({})
// export const onlineDeptTotal = ref({})
// export const operateDeptTotal = ref({})
const { createMessage } = useMessage()
const { hasPermission } = usePermission()

function getAllIds(node): number[] {
  let ids: number[] = [node.id]

  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      ids = ids.concat(getAllIds(child))
    }
  }

  return ids
}

export const searchFormSchema: FormSchema[] = [
  {
    field: 'yearMonth',
    label: '年月',
    required: true,
    defaultValue: dayjs().format('YYYYMM'),
    component: 'DatePicker',
    componentProps: {
      picker: 'month',
      valueFormat: 'YYYYMM',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getDeptSelectTree,
        immediate: false,
        lazyLoad: true,
        multiple: true,
        treeCheckable: true,
        maxTagCount: 3,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          optionFilterProp: 'name',
          treeDefaultExpandAll: true,
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          },
          onSelect: (_, node) => {
            const ids = getAllIds(node)
            formModel.dept_ids = ids
          }
        }
      }
    },
    colProps: {
      span: 12
    }
  },
  {
    field: 'inCharge',
    label: '成员',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

export const onlinePersonalColumns: BasicColumn[] = [
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 200,
    resizable: true
  },
  {
    title: '名称',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '目标金额',
    dataIndex: 'target_sum_amount',
    width: 150,
    resizable: true,
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    ifShow: hasPermission(421),
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth <= +dayjs().format('YYYYMM') || !hasPermission(354)
      return !hasPermission(354)
    }
    // sorter: true
  },
  {
    title: '已完成业绩',
    dataIndex: 'finish_amount',
    width: 150,
    resizable: true,
    sorter: true,
    ifShow: hasPermission(421)
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return text ? text + '%' : ''
    }
  },
  {
    title: '个人业绩额排行',
    dataIndex: 'finish_amount_order',
    width: 150,
    resizable: true
  },
  {
    title: '个人承诺创客中心业绩目标',
    dataIndex: 'dept_cls',
    ifShow: hasPermission(421)
  },
  {
    title: '职称L',
    dataIndex: 'target_title',
    width: 150,
    edit: false,
    editComponent: 'Input',
    editComponentProps: {
      require: true
    }
  }
]

const originPersonalDeptColumns = ref([])
const originDepartmentDetpColumns = ref([])
const originOperationDeptColumns = ref<BasicColumn[]>([])

// 动态显示隐藏列，如果有操作过后还有动态列，则重新将动态列添加过去
export function handleColumnsChange(curColumn, tableKey) {
  const mapTableKey = {
    onlinePersonalTable: 'dept_cls',
    onlineDeptTable: 'dept_finish_amount',
    operationCenterDeptTable: 'dept_cls'
  }
  const mapColumns = {
    onlinePersonalTable: originPersonalDeptColumns.value,
    onlineDeptTable: originDepartmentDetpColumns.value,
    operationCenterDeptTable: originOperationDeptColumns.value
  }
  const deptColumns = mapColumns[tableKey]
  const key = mapTableKey[tableKey]
  if (!key) return
  const curColumnKey = curColumn.map((item) => item.dataIndex)
  if (curColumnKey.includes(key)) {
    const columns = mapTableAction.value?.[tableKey]?.tableAction?.getColumns()
    // console.log(columns)
    const newColumns = cloneDeep(columns)
    const deptClsIdx = columns.findIndex((item) => item.dataIndex === key)
    newColumns[deptClsIdx].children = deptColumns
    // newColumns.splice(deptClsIdx, 1, originDeptColumn.value)
    console.log(newColumns)
    mapTableAction.value?.[tableKey]?.tableAction?.setColumns(newColumns)
  }
}

export const onlinePersonalTableConfig = {
  title: '线上个人业绩统计',
  api: async (params) => {
    const res = await getSalesPerformance(params)
    dataSumGather.value['onlinePersonalTable'] = res.dataSum?.[0] || {}
    // onlineDeptTotal.value = res.dataSum
    return res
  },
  searchInfo: {
    type: 1
  },
  columns: onlinePersonalColumns,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  afterFetch: async (res) => {
    try {
      // 吐血，editRender也是只能识别最外层的dataIndex值，不过没有则为空，只能在这里做一个妥协
      const { items } = await getDeptColumns()
      for (const item of res) {
        const { target } = item
        for (const v of target) {
          item[v.dept_id] = v.target_amount
        }
      }
      const { getTypeColumns } = useCacheColumns()
      let cacheColumns = getTypeColumns('/performance/statistics/onlinePersonalTable')
      cacheColumns =
        cacheColumns?.length > 0
          ? cacheColumns.map((item) => {
              const newCol = onlinePersonalColumns.find((deptCol) => item.dataIndex === deptCol.dataIndex)
              return newCol ? newCol : item
            })
          : onlinePersonalColumns
      // 请求动态表头（运营中心下的部门）
      // const originColumn = onlinePersonalColumns.map((item) =>
      const originColumn = cacheColumns.map((item) =>
        item.dataIndex === 'dept_cls'
          ? {
              ...item,
              children: items.map((dept) => ({
                title: dept.name.replace('运营中心', '').replace('创客中心', ''),
                dataIndex: dept.id,
                width: 150,
                edit: true,
                defaultValue: 0,
                editComponent: 'Input',
                editComponentProps: {
                  min: 0.01
                },
                editDynamicDisabled: () => {
                  // console.log(record)
                  // return !hasPermission(361) || +record.yearMonth <= +dayjs().format('YYYYMM')
                  return !hasPermission(361)
                },
                editRender: ({ record }) => {
                  const deptId = dept.id
                  const curTarget = record.target.find((item) => +item.dept_id === +deptId)
                  if (!curTarget) return ''
                  // record[deptId] = curTarget.target_amount
                  return curTarget.target_amount
                }
              }))
            }
          : item
      )

      originPersonalDeptColumns.value = originColumn.find((item) => item.dataIndex === 'dept_cls')?.children

      mapTableAction.value?.onlinePersonalTable?.tableAction?.setColumns(originColumn)

      return res
    } catch (err) {
      console.log(err)
      createMessage.error('获取创客中心失败！')
    }
  },
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: sortInfo.field
      }
    }
    // return sortInfo
  }
}

export const onlineDepartmentTableColumn: BasicColumn[] = [
  {
    title: '线上部门',
    dataIndex: 'dept_name',
    width: 150
  },
  {
    title: '目标业绩',
    dataIndex: 'target_amount',
    width: 150,
    ifShow: hasPermission(421),
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(400)
      return !hasPermission(611)
    }
  },
  {
    title: '已完成业绩',
    // dataIndex: 'dept_finish_amount',
    dataIndex: 'finish_amount',
    ifShow: hasPermission(421),
    width: 150
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 150
  },
  {
    title: '部门业绩额排名',
    dataIndex: 'finish_amount_order',
    width: 150
  },
  {
    title: '部门业绩率排名',
    dataIndex: 'target_order',
    width: 150
  }
  // {
  //   title: '个人承诺运营中心业绩目标',
  //   dataIndex: 'dept_cls'
  // }
]

export const onlineDepartmentTableConfig = {
  title: '线上部门业绩统计',
  api: async (params) => {
    const res = await getDeptStatistics(params)
    dataSumGather.value['onlineDeptTable'] = res.dataSum
    // onlineDeptTotal.value = res.dataSum
    return res
  },
  searchInfo: {
    type: 1
  },
  columns: onlineDepartmentTableColumn,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema.filter((item) => !['dept_id', 'inCharge'].includes(item.field)),
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  }
  // afterFetch: async (res) => {
  //   try {
  //     // 请求动态表头（运营中心下的部门）
  //     const { items } = await getDeptColumns()
  //     for (const item of res) {
  //       const { target } = item
  //       for (const v of target) {
  //         item[v.clear_dept_id] = v.finish_amount
  //       }
  //     }
  //     const { getTypeColumns } = useCacheColumns()
  //     let cacheColumns = getTypeColumns('/performance/statistics/onlineDeptTable')
  //     cacheColumns = cacheColumns?.length > 0 ? cacheColumns : onlineDepartmentTableColumn
  //     // 请求动态表头（运营中心下的部门）
  //     // const originColumn = onlineDepartmentTableColumn.map((item) =>
  //     const originColumn = cacheColumns.map((item) =>
  //       item.dataIndex === 'dept_finish_amount'
  //         ? {
  //             ...item,
  //             children: [
  //               ...items.map((dept) => ({
  //                 title: dept.name.replace('运营中心', '').replace('创客中心', ''),
  //                 dataIndex: dept.id,
  //                 width: 150
  //               })),
  //               {
  //                 title: '异常带单',
  //                 dataIndex: 9999,
  //                 width: 150
  //               },
  //               {
  //                 title: '公司渠道',
  //                 dataIndex: 9998,
  //                 width: 150
  //               },
  //               {
  //                 title: '合计',
  //                 dataIndex: 'finish_amount',
  //                 width: 150
  //               }
  //             ]
  //           }
  //         : item
  //     )
  //
  //     originDepartmentDetpColumns.value = originColumn.find((item) => item.dataIndex === 'dept_finish_amount')?.children
  //     mapTableAction.value?.onlineDeptTable?.tableAction?.setColumns(originColumn)
  //     return res
  //   } catch (err) {
  //     console.log(err)
  //     createMessage.error('获取创客中心失败！')
  //   }
  // }
}

export const offlinePersonalTableColumn: BasicColumn[] = [
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 200,
    resizable: true
  },
  {
    title: '成员',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '目标金额',
    dataIndex: 'target_sum_amount',
    width: 150,
    resizable: true,
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(400)
      return !hasPermission(400)
    },
    // sorter: true,
    ifShow: hasPermission(421)
  },
  {
    title: '已完成业绩',
    dataIndex: 'finish_amount',
    width: 150,
    resizable: true,
    sorter: true,
    ifShow: hasPermission(421)
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return text ? text + '%' : ''
    }
  },
  {
    title: '个人业绩额排行',
    dataIndex: 'finish_amount_order',
    width: 150,
    resizable: true
  },
  // {
  //   title: '个人承诺运营中心业绩目标',
  //   dataIndex: 'dept_cls'
  // },
  {
    title: '职称L',
    dataIndex: 'target_title',
    width: 150,
    edit: false,
    editComponent: 'Input',
    editComponentProps: {
      require: true
    }
  }
]

export const offlineDepartmentTableColumn: BasicColumn[] = [
  {
    title: '线下部门',
    dataIndex: 'dept_name'
    // width: 150
  },
  {
    title: '目标业绩',
    dataIndex: 'target_amount',
    ifShow: hasPermission(421),
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(400)
      return !hasPermission(612)
    }
    // width: 150
  },
  {
    title: '已完成业绩',
    dataIndex: 'finish_amount',
    ifShow: hasPermission(421)
    // width: 150
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score'
    // width: 150
  },
  {
    title: '部门业绩额排名',
    dataIndex: 'finish_amount_order'
    // width: 150
  },
  {
    title: '部门业绩率排名',
    dataIndex: 'target_order',
    width: 150
  }
]

export const offlineDepartmentTableConfig = {
  title: '线下部门业绩统计',
  api: async (params) => {
    const res = await getDeptStatistics(params)
    dataSumGather.value['offlineDeptTable'] = res.dataSum || {}
    return res
  },
  searchInfo: {
    type: 2
  },
  columns: offlineDepartmentTableColumn,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema.filter((item) => !['dept_id', 'inCharge'].includes(item.field)),
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  }
}

export const offlinePersonalTableConfig = {
  title: '线下个人业绩统计',
  api: async (params) => {
    const res = await getSalesPerformance(params)
    dataSumGather.value['offlinePersonalTable'] = res.dataSum[0] || {}
    return res
  },
  searchInfo: {
    type: 2
  },
  columns: offlinePersonalTableColumn,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: sortInfo.field
      }
    }
    // return sortInfo
  }
}

export const guideTableColumn: BasicColumn[] = [
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 200,
    resizable: true
  },
  {
    title: '成员',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '目标金额',
    dataIndex: 'target_sum_amount',
    width: 150,
    resizable: true,
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(404)
      return !hasPermission(404)
    },
    // sorter: true,
    ifShow: hasPermission(421)
  },
  {
    title: '已完成业绩',
    dataIndex: 'finish_amount',
    width: 150,
    resizable: true,
    sorter: true,
    ifShow: hasPermission(421)
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return text ? text + '%' : ''
    }
  },
  {
    title: '个人业绩额排行',
    dataIndex: 'finish_amount_order',
    width: 150,
    resizable: true
  },
  {
    title: '职称S',
    dataIndex: 'target_title',
    width: 150,
    resizable: true,
    edit: false,
    editComponent: 'Input'
    // editDynamicDisabled: ({ record }) => {
    //   return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(354)
    // },
  }
]

export const guideTableConfig = {
  title: '导购部业绩统计',
  api: async (params) => {
    const res = await getSalesPerformance(params)
    dataSumGather.value['guideTable'] = res.dataSum[0] || {}
    return res
  },
  searchInfo: {
    type: 3
  },
  columns: guideTableColumn,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: sortInfo.field
      }
    }
    // return sortInfo
  }
}

export const salesDetailColumns = [
  {
    title: '项目经理',
    dataIndex: 'inCharge_name'
  },
  {
    title: '方案经理',
    dataIndex: 'program_incharge_name'
  },
  {
    title: '导购员',
    dataIndex: 'guide_name'
  },
  {
    title: '销售单号',
    dataIndex: 'sales_strid'
  },
  {
    title: '客户',
    dataIndex: 'client_name'
  },
  {
    title: '客户国家',
    dataIndex: 'country_name'
  },
  {
    title: '开单日期',
    dataIndex: 'submited_at'
  },
  {
    title: '过去业绩加减单',
    dataIndex: 'over_amount'
  },
  {
    title: '当月新增开单业绩',
    dataIndex: 'now_amount'
  },
  {
    title: '开单金额',
    dataIndex: 'bill_amount'
  },
  {
    title: '凤凰计划',
    dataIndex: 'phoenix_plan'
  },
  // {
  //   title: '凤凰计划金额',
  //   dataIndex: 'phoenix_amount'
  // },
  {
    title: '2025年业绩核定值',
    dataIndex: 'reality_amount'
  },
  {
    title: '2025年业绩核定值（产品部）',
    dataIndex: 'product_amount',
    width: 220,
    resizable: true
  },
  {
    title: '2025年业绩核定值（业务部）',
    dataIndex: 'business_amount',
    width: 220,
    resizable: true
  },
  {
    title: '业务部门',
    dataIndex: 'operation_name',
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    resizable: true
  },
  {
    title: '核定日期',
    dataIndex: 'ach_app_at'
  },
  {
    title: '水单日期',
    dataIndex: 'fund_at'
  },
  {
    title: '渠道来源',
    dataIndex: 'source2'
  },
  {
    title: '附件',
    dataIndex: 'files2',
    customRender: ({ value }) => {
      if (isNull(value)) return '-'
      return h(
        'div',
        {},
        value
          ?.filter((item) => !isNull(item))
          .map((item) =>
            h(
              'div',
              {
                style: 'cursor: pointer; color: #9090e9; overflow: hidden; text-overflow: ellipsis; white-space: nowrap',
                onClick: (e) => {
                  e.stopPropagation()
                  window.open(item)
                }
              },
              item.replace(/.*\/(.*?)$/, '$1')
            )
          )
      )
    }
  }
]

export const salesDetailTableConfig = {
  title: '销售单明细业绩报表',
  api: async (params) => {
    if (!params.yearMonth && !params.startDate && !params.endDate) {
      createMessage.error('核定日期和年月最少填写一项！')
      return false
    }
    const res = await getSalesDetailStatistics(params)
    dataSumGather.value['salesDetailTable'] = res.dataSum[0] || {}
    // salesTotal.value = res.dataSum[0]
    return res
  },
  columns: salesDetailColumns,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'date',
        label: '核定日期',
        component: 'SingleRangeDate',
        componentProps: { startPickerProps: { showTime: true }, endPickerProps: { showTime: true } }
        // defaultValue: [dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD HH:mm:ss')],
        // required: true
      },
      {
        field: 'yearMonth',
        label: '年月',
        // required: true,
        defaultValue: dayjs().format('YYYYMM'),
        component: 'DatePicker',
        componentProps: {
          picker: 'month',
          valueFormat: 'YYYYMM',
          style: {
            width: '100%'
          }
        }
      },
      ...cloneDeep(searchFormSchema).filter((item) => !['yearMonth', 'inCharge'].includes(item.field)),
      { field: 'sales_strid', label: '销售单号', component: 'Input' },
      {
        field: 'operation',
        label: '创客中心',
        component: 'PagingApiSelect',
        componentProps: {
          api: getDept,
          params: { status: 1, is_audit: 1, is_operate: 1 },
          // params: { status: 1, is_audit: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            optionFilterProp: 'name',
            showSearch: true,
            placeholder: '请选择',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      {
        field: 'inCharge',
        label: '项目经理',
        component: 'PagingApiSelect',
        componentProps: {
          api: getAccountList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          selectProps: {
            fieldNames: { key: 'id', value: 'id', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true
          }
        },
        itemProps: {
          validateTrigger: 'blur'
        }
      },
      {
        field: 'program_incharge',
        label: '方案经理',
        component: 'PagingApiSelect',
        componentProps: {
          api: getAccountList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          selectProps: {
            fieldNames: { key: 'id', value: 'id', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true
          }
        },
        itemProps: {
          validateTrigger: 'blur'
        }
      },
      {
        field: 'is_operation',
        label: '业务部门为空',
        component: 'Select',
        componentProps: {
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 2 }
          ]
        }
      },
      {
        field: 'fund_at',
        label: '水单日期',
        component: 'SingleRangeDate',
        componentProps: { startPickerProps: { showTime: true }, endPickerProps: { showTime: true } }
      },
      {
        field: 'type',
        label: '导购员不为空',
        defaultValue: 0,
        component: 'Select',
        componentProps: {
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ]
        }
      },
      {
        field: 'main_dept',
        label: '主部门',
        component: 'ApiTreeSelect',
        componentProps: ({ formModel }) => {
          return {
            api: getDeptSelectTree,
            immediate: false,
            lazyLoad: true,
            // multiple: true,
            // treeCheckable: true,
            // maxTagCount: 3,
            treeSelectProps: {
              fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
              placeholder: '请选择',
              showSearch: true,
              optionFilterProp: 'name',
              treeDefaultExpandAll: true,
              filterTreeNode: (search, item) => {
                if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
                return false
              },
              onSelect: (_, node) => {
                const ids = getAllIds(node)
                formModel.dept_ids = ids
              }
            }
          }
        },
        colProps: {
          span: 12
        }
      },
      {
        field: 'operation_dept',
        label: '业务主部门',
        component: 'ApiTreeSelect',
        componentProps: ({ formModel }) => {
          return {
            api: getDeptSelectTree,
            immediate: false,
            lazyLoad: true,
            // multiple: true,
            // treeCheckable: true,
            // maxTagCount: 3,
            treeSelectProps: {
              fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
              placeholder: '请选择',
              showSearch: true,
              optionFilterProp: 'name',
              treeDefaultExpandAll: true,
              filterTreeNode: (search, item) => {
                if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
                return false
              },
              onSelect: (_, node) => {
                const ids = getAllIds(node)
                formModel.dept_ids = ids
              }
            }
          }
        },
        colProps: {
          span: 12
        }
      }
    ],
    fieldMapToTime: [
      ['date', ['startDate', 'endDate'], ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']],
      ['fund_at', ['fund_at_start', 'fund_at_end'], ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']]
    ],
    baseColProps: {
      span: 6
    },
    actionColOptions: {
      span: 24
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: sortInfo.field
      }
    }
    // return sortInfo
  }
}

const operationCenterColumns: BasicColumn[] = [
  {
    title: '创客中心',
    dataIndex: 'dept_name',
    width: 150,
    resizable: true
  },
  {
    title: '成员',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '目标业绩',
    dataIndex: 'target_amount',
    width: 150,
    resizable: true,
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: ({ record }) => {
      console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(404)
      return !hasPermission(413)
    },
    ifShow: hasPermission(421)
    // sorter: true
  },
  {
    title: '已完成业绩',
    dataIndex: 'finish_amount',
    width: 150,
    resizable: true,
    ifShow: hasPermission(421)
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text + '%'
    }
  },
  {
    title: '完成业绩排名',
    dataIndex: 'finish_amount_order',
    width: 150,
    resizable: true
  },
  {
    title: '职称P',
    dataIndex: 'target_title',
    width: 150,
    resizable: true,
    edit: false,
    editComponent: 'Input'
    // editDynamicDisabled: ({ record }) => {
    //   return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(354)
    // },
  }
]

export const deptSalesDetailTableConfig = {
  ...salesDetailTableConfig,
  title: '本部门自建业绩',
  api: async (params) => {
    if (!params.yearMonth && !params.startDate && !params.endDate) {
      createMessage.error('核定日期和年月最少填写一项！')
      return false
    }
    const res = await getSalesDetailStatistics({ ...params, is_self_build: 2 })
    dataSumGather.value['salesDetailTable'] = res.dataSum[0] || {}
    // salesTotal.value = res.dataSum[0]
    return res
  }
}

const operationCenterConfig = {
  title: '业务部门 - 成员业绩统计',
  api: async (params) => {
    const data = await getOperatePerson(params)
    dataSumGather.value['operationCenterTable'] = data.dataSum[0] || {}
    return data
  },
  columns: operationCenterColumns,
  // searchInfo: {
  //   type: 4
  // },
  formConfig: {
    labelWidth: 120,
    schemas: [
      ...searchFormSchema.filter((item) => !['dept_id'].includes(item.field)),
      {
        field: 'operation',
        label: '业务部门',
        component: 'ApiTreeSelect',
        componentProps: {
          api: getDeptSelectTree,
          immediate: false,
          lazyLoad: true,
          treeSelectProps: {
            fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
            placeholder: '请选择',
            showSearch: true,
            optionFilterProp: 'name',
            treeDefaultExpandAll: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
              return false
            }
          }
        }
      }
    ],
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  sortFn: (sortInfo) => {
    if (sortInfo.order) {
      const map = {
        ascend: 'asc',
        descend: 'desc'
      }
      return {
        sort: map[sortInfo.order],
        order_by: sortInfo.field
      }
    }
    // return sortInfo
  }
}

const operationCenterDeptColumns: BasicColumn[] = [
  {
    title: '业务部门',
    dataIndex: 'dept_name',
    width: 150,
    resizable: true
  },
  {
    title: '目标业绩',
    dataIndex: 'target_amount',
    width: 200,
    resizable: true,
    ifShow: hasPermission(421),
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0
    },
    editDynamicDisabled: () => {
      // console.log(record)
      // return +record.yearMonth < +dayjs().format('YYYYMM') || !hasPermission(400)
      return !hasPermission(613)
    }
  },
  {
    title: '完成业绩',
    dataIndex: 'dept_cls',
    ifShow: hasPermission(421)
  },
  {
    title: '业绩完成率',
    dataIndex: 'finish_score',
    width: 150,
    resizable: true,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '部门业绩额排行',
    dataIndex: 'finish_amount_order',
    width: 150,
    resizable: true
  },
  {
    title: '部门业绩率排行',
    dataIndex: 'target_order',
    width: 150,
    resizable: true
  }
]

const operationCenterDeptConfig = {
  title: '业务部门 - 部门业绩统计',
  api: async (params) => {
    const res = await getOperateDept(params)
    dataSumGather.value['operationCenterDeptTable'] = res.dataSum
    // operateDeptTotal.value = res.dataSum
    return res
  },
  columns: operationCenterDeptColumns,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema.filter((item) => item.field !== 'inCharge'),
    baseColProps: {
      span: 6
    }
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: true,
  afterFetch: (res) => {
    const [first] = res
    const { target } = first
    if (!target || target.length === 0) return res

    // 后端将头部放到返回的数据里了，吐血
    const originColumns: BasicColumn[] = []
    for (const deptItem of res) {
      for (const dept of deptItem.target) {
        deptItem[dept.dept_id] = dept.finish_amount
      }
    }
    for (const dept of target) {
      originColumns.push({ title: dept.dept_name, dataIndex: dept.dept_id, width: 100, resizable: true })
    }
    originColumns.push({ title: '合计', dataIndex: 'finish_amount', width: 100, resizable: true })

    const { getTypeColumns } = useCacheColumns()
    let cacheColumns = getTypeColumns('/performance/statistics/operationCenterDeptTable')
    cacheColumns =
      cacheColumns?.length > 0
        ? cacheColumns.map((item) => {
            const newCol = operationCenterDeptColumns.find((deptCol) => item.dataIndex === deptCol.dataIndex)
            return newCol ? newCol : item
          })
        : operationCenterDeptColumns

    const deptCls = cacheColumns.find((column) => column.dataIndex === 'dept_cls')
    if (deptCls) {
      deptCls.children = originColumns
      originOperationDeptColumns.value = originColumns
      mapTableAction.value?.operationCenterDeptTable?.tableAction?.setColumns(cacheColumns)
    }

    return res
  }
}

export const tabConfigList = [
  {
    key: 6,
    tabName: '销售单明细业绩',
    config: salesDetailTableConfig,
    value: 'salesDetailTable',
    ifShow: hasPermission(403)
  },
  {
    key: 9,
    tabName: '本部门自建业绩',
    config: deptSalesDetailTableConfig,
    value: 'deptSalesDetailTable',
    ifShow: hasPermission(717)
  },
  {
    key: 1,
    tabName: '线上 - 成员业绩统计',
    config: onlinePersonalTableConfig,
    value: 'onlinePersonalTable',
    ifShow: hasPermission(397),
    searchInfo: {
      type: 1
    }
  },
  {
    key: 2,
    tabName: '线上 - 部门业绩统计',
    config: onlineDepartmentTableConfig,
    value: 'onlineDeptTable',
    ifShow: hasPermission(398),
    searchInfo: {
      type: 1
    }
  },
  {
    key: 3,
    tabName: '线下 - 成员业绩统计',
    config: offlinePersonalTableConfig,
    value: 'offlinePersonalTable',
    ifShow: hasPermission(399),
    searchInfo: {
      type: 2
    }
  },
  {
    key: 4,
    tabName: '线下 - 部门业绩统计',
    config: offlineDepartmentTableConfig,
    value: 'offlineDeptTable',
    ifShow: hasPermission(401),
    searchInfo: {
      type: 2
    }
  },
  {
    key: 5,
    tabName: '导购部业绩',
    config: guideTableConfig,
    value: 'guideTable',
    ifShow: hasPermission(402),
    searchInfo: {
      type: 3
    }
  },
  {
    key: 7,
    tabName: '业务部门 - 成员业绩统计',
    config: operationCenterConfig,
    value: 'operationCenterTable',
    ifShow: hasPermission(412)
    // searchInfo: {
    //   type: 4
    // }
  },
  {
    key: 8,
    tabName: '业务部门 - 部门业绩统计',
    config: operationCenterDeptConfig,
    value: 'operationCenterDeptTable',
    ifShow: hasPermission(448)
  }
]
