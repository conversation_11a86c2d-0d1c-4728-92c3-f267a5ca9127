import { h } from 'vue'
import { getDeptTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/erp/systemInfo'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullAndUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '菜品名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '菜品成本价',
    dataIndex: 'cost',
    width: 200,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return isNullAndUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'green' : 'red' }, text == 1 ? '启用' : '未启用')
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 200,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    label: '菜品名称',
    field: 'name',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps() {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      }
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: {
      span: 8
    }
  }
]
