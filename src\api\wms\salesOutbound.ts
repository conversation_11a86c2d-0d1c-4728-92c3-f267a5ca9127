import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { SalesOutBoundItem } from '/@/api/wms/model/types'

enum Api {
  GetSalesOutboundList = '/erp/wms/sor/getList',
  GetBuybackList = '/erp/wms/pr/getBuybackList'
}

export const getSalesOutboundList = (params?: {}) => defHttp.get({ url: Api.GetSalesOutboundList, params })

export const getBuybackList = (params?: {}) => defHttp.get<BasicFetchResult<SalesOutBoundItem>>({ url: Api.GetBuybackList, params })
