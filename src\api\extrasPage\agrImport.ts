import { defHttp } from '/@/utils/http/axios'

enum Api {
  // 批量入库导入检查
  ImportEwCheck = '/AGR/importEwCheck',
  //提交入库
  SubmitEw = '/AGR/submitEw',
  ImportOwCheck = '/AGR/importOwCheck',
  SubmitOw = '/AGR/submitOw',
  ImportPurchaseReturnCheck = '/AGR/importPurchaseReturnCheck',
  SubmitPurchaseReturn = '/AGR/submitPurchaseReturn',
  ImportRequestReturnCheck = '/AGR/importRequestReturnCheck',
  SubmitRequestReturn = '/AGR/submitRequestReturn',
  ImportStockingReturnCheck = '/AGR/importStockingReturnCheck',
  SubmitStockingReturn = '/AGR/submitStockingReturn',
  ImportPackageCheck = '/AGR/importPackageCheck',
  SubmitPackage = '/AGR/submitPackage'
}

/** 批量入库导入检查*/
export const importEwCheck = (params: Recordable) => defHttp.post({ url: Api.ImportEwCheck, params }, { errorMessageMode: 'message' })

/**提交入库 */
export const submitEw = (params: Recordable) => defHttp.post({ url: Api.SubmitEw, params }, { errorMessageMode: 'message' })

export const importOwCheck = (params: Recordable) => defHttp.post({ url: Api.ImportOwCheck, params }, { errorMessageMode: 'message' })

export const submitOw = (params: Recordable) => defHttp.post({ url: Api.SubmitOw, params }, { errorMessageMode: 'message' })

export const importPurchaseReturnCheck = (params: { data: { un: string; quantity: string }[] }) =>
  defHttp.post({ url: Api.ImportPurchaseReturnCheck, params }, { errorMessageMode: 'message' })

export const submitPurchaseReturn = (params: Recordable) =>
  defHttp.post({ url: Api.SubmitPurchaseReturn, params }, { errorMessageMode: 'message' })

export const importRequestReturnCheck = (params: { data: { un: string; quantity: string }[] }) =>
  defHttp.post({ url: Api.ImportRequestReturnCheck, params }, { errorMessageMode: 'message' })

export const submitRequestReturn = (params: Recordable) =>
  defHttp.post({ url: Api.SubmitRequestReturn, params }, { errorMessageMode: 'message' })

export const importStockingReturnCheck = (params: { data: { un: string; quantity: string }[] }) =>
  defHttp.post({ url: Api.ImportStockingReturnCheck, params }, { errorMessageMode: 'message' })

export const submitStockingReturn = (params: Recordable) =>
  defHttp.post({ url: Api.SubmitStockingReturn, params }, { errorMessageMode: 'message' })

export const importPackageCheck = (params: { data: { un: string; quantity: string }[] }) =>
  defHttp.post({ url: Api.ImportPackageCheck, params }, { errorMessageMode: 'message' })

export const submitPackage = (params: Recordable) => defHttp.post({ url: Api.SubmitPackage, params }, { errorMessageMode: 'message' })
