export interface IGetInWarehouseListDoc {
  id?: number
  dept_id: number
  supplier_id: number
  status?: number
  waybill_num?: string
  erp_num?: string
  pkg_num: string
}

export interface IGetInWarehouseListItems {
  id: number
  request_id: number
  purchase_id: number
  name: string
  puid: string
  imgs: string[]
  warehouse_id: number
  unit: string
  unit_price: number
  pkg_num: number
  pkg_received: number
  qty_total: number
  qty_received: number
  qty_defective: number
  qty_stocking: number
  desc: string
  remark: string
  status: number
  received_at: string
}

export interface IGetInWarehouseWork {
  name: string
  dept_id: number
  auditor: number
  inCharge: number
}

export interface IGetInWarehouseList {
  id: number
  work_id: number
  dept_id: number
  supplier_id: number
  updated_at_start: string
  updated_at_end: string
  order_by: string
  sort: string
  name: string
  puid: number
  status: number[]
  work_strid: string
}

export interface IGetInWarehouseDetail {
  doc_in_id: number
  order_by: string
  sort: string
  id: number
}
