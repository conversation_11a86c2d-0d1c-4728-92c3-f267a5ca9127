export interface SalesTopListItem {
  /**
   * 审核人
   */
  auditor_name: string
  /**
   * 客户名称
   */
  client_name: string
  /**
   * 应付金额
   */
  cost: string
  /**
   * 创建时间
   */
  created_at: string
  /**
   * 创建人
   */
  creator_name: string
  /**
   * 货币
   */
  currency: string
  /**
   * 部门名称
   */
  department: string
  /**
   * 汇率
   */
  exchange_rate: string
  id: string
  /**
   * 负责人
   */
  inCharge_name: string
  /**
   * 已付金额
   */
  paid: string
  /**
   * 应收数量
   */
  receivable: string
  /**
   * 已收数量
   */
  received: string
  /**
   * 实收金额
   */
  received_actual: string
  /**
   * 备注
   */
  remark: string
  /**
   * 渠道来源名称
   */
  source: string
  /**
   * 状态
   */
  status: string
  /**
   * 订单号
   */
  strid: string
  /**
   * 提交时间
   */
  submited_at: string
  /**
   * 订单类型
   */
  type: string
  [property: string]: any
}

export interface SalesSubListItem {
  id: number | string
  /**
   * 批号
   */
  batch_code: string
  /**
   * 创建时间
   */
  created_at: string
  /**
   * 描述
   */
  desc: string
  /**
   * 产品图片，多张图
   */
  imgs: string
  /**
   * 产品名称
   */
  name: string
  /**
   * 产品唯一码
   */
  puid: string
  /**
   * 需求总数量
   */
  qty_request: string
  /**
   * 需求剩余数量
   */
  qty_request_left: string
  /**
   * 备注
   */
  remark: string
  /**
   * 总金额
   */
  total_amount: string
  /**
   * 类型
   */
  type: string
  /**
   * 单位
   */
  unit: string
  /**
   * 单价
   */
  unit_price: string
  /**
   * 销售订单id
   */
  work_id: string
  [property: string]: any
  dept_id: number
  client_id: number
  client_name: string
  inCharge: number
}

export interface SalesListParams {
  /**
   * 根据部门名称模糊搜索
   */
  dept_id?: string
  /**
   * 页码默认为1
   */
  page?: string
  /**
   * 每页条数默认为10
   */
  pageSize?: number
  /**
   * 根据订单目前状态查询订单，0-待执行 1-执行中 2-备货中 5-已在库（备货完成）15-已结束（可修改）
   */
  status?: string
  [property: string]: any
}

export interface SalesDetailParams {
  /**
   * 二级销售订单id
   */
  work_id?: string
  [property: string]: any
}

export interface SalesRelateListParams {
  /**
   * work订单表id
   */
  work_id?: string | number
  [property: string]: any
}

// 销售订单核对
export interface saleOrderVerify {
  Verification_date: string
  order_no: string
  status: number
}

//编辑销售订单
export interface IEditSaleOrder {
  name: string
  source: string
  source_uniqid: string
  dept_id: number
  inCharge: number
  currency: string
  receivable: number
  exchange_rate: string
  client_id: number
  client_name: string
  items: {
    name: string
    id: number
    unit: string
    unit_price: string
    qty_request: number
    desc: string
    remark: string
  }[]
}

// 手续费付款单
export interface commissionPaymentOrder {
  payment_date: string
  order_no: string
  fund_return: string
  remarks: string
}

// 采购订单
export interface PurchaseOrderList {
  created_at: string
  dept_id: number
  files: string[]
  id: number
  imgs: string[]
  processor: string
  supplier_id: number
  strid: string
  supplier_name: string
  updated_at: string
  work_id: number
  [property: string]: any
}

export interface PurchaseOrderListParams {
  created_at_end?: string
  created_at_start?: string
  dept_id?: number
  id?: number
  /**
   * 包含明细的商品name模糊查询
   */
  name?: string
  order_by?: string
  page?: number
  pageSize?: number
  processor?: string
  /**
   * 包含明细商品puid模糊查询
   */
  puid?: string
  sort?: string
  supplier_id?: number
  updated_at_end?: string
  updated_at_start?: string
  work_id?: number
  [property: string]: any
}

export interface CreateProductParams {
  desc?: string
  imgs?: string[]
  name?: string
  puid?: string
  qty_purchased?: number
  qty_wait_received?: number
  remark?: string
  request_id?: number
  supplier_id?: string
  total_cost?: number
  unit?: string
  unit_price?: number
  [property: string]: any
}

export interface Doc {
  dept_id: number
  files: string[]
  // imgs?: string[];
  processor?: string
  has_fund?: number
  id?: number
  supplier_id?: number
  work_id?: number
  [property: string]: any
}

export interface Work {
  auditor?: number
  /**
   * 销售单work的id
   */
  basic_work_id?: number
  client_id?: number
  client_name?: string
  cost?: number
  currency: string
  dept_id: number
  inCharge: number
  name?: string
  /**
   * 销售单work的id
   */
  parent_id?: number
  total_price: number
  [property: string]: any
}

export interface CreateOrEditPurchase {
  doc: Doc
  items: CreateProductParams[]
  work?: Work
  [property: string]: any
}

export interface EditItem {
  desc?: string
  /**
   * item 的id
   */
  id: number
  // imgs?: string[];
  name?: string
  puid?: string
  qty_purchased?: number
  qty_wait_received?: number
  remark?: string
  request_id?: number
  total_cost?: number
  unit?: string
  unit_price?: number
  [property: string]: any
}

export interface PurchaseDetail {
  created_at: string
  desc: string
  doc_id: number
  id: number
  imgs: string[]
  name: string
  puid: string
  qty_purchased: number
  qty_wait_received: number
  remark: string
  request_id: number
  total_cost: string
  type: number
  unit: string
  unit_price: string
  updated_at: string
  [property: string]: any
}

// 盘点表
export interface SearchInventory {
  name?: string
  wasehouse_id?: number
  creator?: number
  start_time?: string | number
  end_time?: string | number
  doc_id?: number | string
}
export interface InventoryParams {
  /**
   * 申请人id
   */
  applicant: number
  /**
   * 盆点描述
   */
  desc1: string
  /**
   * 库存描述
   */
  desc2?: string
  /**
   * 图片json
   */
  imgs?: string[]
  /**
   * 负责人id
   */
  inCharge?: number
  /**
   * 名称或商品名称
   */
  name?: string
  /**
   * 要接受的包裹数
   */
  pkg_num?: number
  /**
   * 已经收到的包裹数
   */
  pkg_received?: number
  /**
   * 员工id
   */
  processor?: number
  /**
   * 报废的商品数
   */
  qty_defective?: number
  /**
   * 实际入库商品数
   */
  qty_received?: number
  /**
   * 现有的商品数
   */
  qty_stocking?: number
  /**
   * 要收到的商品数
   */
  qty_total?: number
  /**
   * 库存备注
   */
  remark?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 单价
   */
  unit_price?: number
  /**
   * 仓库id
   */
  warehouse_id: number
  [property: string]: any
}

// 出库单
export interface OutWarehouseParams {
  applicant?: number
  checkout_at_end?: string
  checkout_at_start?: string
  confirmed_at_end?: string
  confirmed_at_start?: string
  created_at_end: string
  created_at_start: string
  id?: number
  inCharge?: number
  /**
   * 包含明细的商品name模糊查询
   */
  name?: string
  order_by?: string
  /**
   * 包含明细商品puid模糊查询
   */
  puid?: string
  sort?: string
  updated_at_end?: string
  updated_at_start?: string
  [property: string]: any
}

export interface OutWarehouseItem {
  applicant?: number
  applicant_name?: string
  checkout_at?: string
  confirmed_at?: string
  created_at?: string
  id?: number
  inCharge?: number
  inCharge_name?: string
  item?: OutWarehouseItemDetail[]
  remark?: null
  status?: number
  strid?: string
  updated_at?: string
  [property: string]: any
}

export interface OutWarehouseItemDetail {
  batch_code?: number
  created_at?: string
  dept_id?: number
  doc_id?: number
  id?: number
  quantity?: number
  request_id?: number
  stocking_id?: number
  updated_at?: string
  warehouse_id?: number
  work_id?: number
  [property: string]: any
}

export interface AddOutWarehouseData {
  doc: AddOutWarehouseDoc
  items: AddOutWarehouseItem[]
  [property: string]: any
}

export interface AddOutWarehouseDoc {
  applicant: number
  checkout_at?: string
  confirmed_at?: string
  /**
   * 有值会更新,不提供会新增
   */
  id?: number
  inCharge: number
  remark?: string
  status?: number
  [property: string]: any
}

export interface AddOutWarehouseItem {
  batch_code: number
  dept_id: number
  /**
   * 有值会更新,不提供会新增
   */
  id?: number
  quantity: number
  request_id: number
  stocking_id: number
  warehouse_id: number
  work_id: number
  [property: string]: any
}

export interface OrderOutDetail {
  /**
   * 最大数量
   */
  max_quantity: string
  /**
   * 当前数量
   */
  quantity: number
  /**
   * 销售单号
   */
  strid: string
  [property: string]: any
}

// 质检
export interface QualityDetectionItem {
  auditor: number
  basic_work_id: number
  client_id: number
  client_name: string
  cost: null
  created_at: string
  creator: number
  currency: string
  dept_id: number
  est_finished_at: null
  exchange_rate: null
  id: number
  inCharge: number
  item_request_count: number
  name: string
  paid: null
  paid_actual: null
  parent_id: number
  project_id: null
  project_number: null
  qc_item_count: number
  qc_rate: number
  receivable: string
  received: null
  received_actual: number
  remark: string
  source: null
  source_uniqid: null
  start_at: null
  status: number
  strid: string
  submited_at: null
  total_price: null
  type: number
  updated_at: string
  doc_qc_report_ids: number[]
  [property: string]: any
}

export interface QualityDetectionParams {
  basic_work_id?: number
  /**
   * work id
   */
  id?: number
  name?: string
  parent_id?: number
  source_uniquid?: string
  /**
   * work status
   */
  status?: number
  strid?: string
  /**
   * work type
   */
  type?: number
  [property: string]: any
}

export interface GenerateQcReportParams {
  /**
   * 质检内容
   */
  content: string
  dept_id: number
  /**
   * 是否二次质检 , 0:否,1:是
   */
  double_check: number
  /**
   * 不提供则创建,否则跟进id更新
   */
  id?: number
  /**
   * 图片
   */
  images: string[]
  /**
   * 关联item_request的id数组
   */
  item_ids: number[]
  /**
   * 订单work的id
   */
  order_id: number
  /**
   * 质检时期ID 1 => '生产中',\n  2 => '生产完成',\n  3 => '包装后',
   */
  qc_stage_id: number
  /**
   * 质检方式ID  1 => '线上质检',\n  2 => '到厂质检',\n  3 => '到仓质检',\n  4 => '他人代检',
   */
  qc_type_id: number
  /**
   * 质检结果
   */
  result: string
  /**
   * 质检人ID
   */
  user_id: number
  [property: string]: any
}

export interface QualityDetectionReportParams {
  content?: string
  double_check?: number
  id?: number
  order_id?: number
  page?: string
  pageSize?: string
  qc_stage_id?: number
  qc_type_id?: number
  result?: string
  stocking_id?: number
  user_id?: number
  work_id?: number
  [property: string]: any
}

interface QualityDetectionReportItems {
  batch_code: number
  created_at: string
  desc: string
  id: number
  imgs: string[]
  name: string
  parent_uniqid: string
  pivot: Pivot
  puid: string
  qty_request: number
  qty_request_left: number
  remark: string
  total_amount: string
  type: number
  uniqid: string
  unit: string
  unit_price: string
  updated_at: string
  work_id: number
  [property: string]: any
}

interface Pivot {
  qc_report_id: number
  request_id: number
  [property: string]: any
}

export type QualityDetectionItemReport = Omit<QualityDetectionReportParams, 'page' | 'pageSize' | 'stocking_id'> & {
  images?: string[]
  item?: QualityDetectionReportItems[]
  created_at?: string
  updated_at?: string
}

export interface OutWareHouseItemDetail {
  batch_code: number
  created_at: string
  dept_id: number
  doc_id: number
  id: number
  /**
   * 库存数量
   */
  qty_stocking: number
  /**
   * 出库数量
   */
  quantity: number
  /**
   * 需求id
   */
  request_id: number
  /**
   * 状态：，0 未出库 1准备出库 2已出库
   */
  status: number
  stocking_id: number
  updated_at: string
  /**
   * 仓库id
   */
  warehouse_id: number
  work_id: number
  [property: string]: any
}

export interface EditStockingParams {
  /**
   * 描述
   */
  desc?: string
  /**
   * 库存id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 单价
   */
  unit_price: string
  /**
   * 仓库ID
   */
  warehouse_id: number
  [property: string]: any
}
