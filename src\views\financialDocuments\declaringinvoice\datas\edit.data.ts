import { BasicColumn } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    dataIndex: 'pur_strid',
    title: '采购单号',
    width: 200,
    resizable: true
  },
  {
    dataIndex: 'number',
    title: '发票号码',
    width: 200,
    resizable: true
  },
  {
    dataIndex: 'tax_service_name',
    title: '品名',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Textarea'
  },
  {
    dataIndex: 'size',
    title: '规格型号',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    dataIndex: 'unit',
    title: '单位',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    dataIndex: 'quantity',
    title: '数量',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    dataIndex: 'unit_price',
    title: '单价',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    dataIndex: 'amount',
    title: '金额',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    dataIndex: 'tax_rate',
    title: '税率',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01,
      max: 100
    }
  },
  {
    dataIndex: 'tax_amount',
    title: '税额',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01,
      max: 100
    }
  },
  {
    dataIndex: 'tax_total_amount',
    title: '价税合计',
    width: 200,
    resizable: true,
    defaultHidden: false,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    dataIndex: 'pur_id',
    title: 'pur_id',
    width: 200,
    resizable: true,
    defaultHidden: true
  }
]
