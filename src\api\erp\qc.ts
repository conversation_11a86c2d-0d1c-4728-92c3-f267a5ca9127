import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetQcList = '/erp/qr/getList',
  AddOrEditQc = '/erp/qr/update',
  GetQcDetail = '/erp/qr/details',
  SetQcStatus = '/erp/qr/setStatus',
  DelQc = '/erp/qr/deleteReport',
  NullifyQc = '/erp/qr/setCancel',
  QrsetImagesAndVideos = '/erp/qr/setImagesAndVideos',

  //新版
  qrngetList = '/erp/qrn/getList',
  qrndeleteReport = '/erp/qrn/deleteReport',
  qrnsetStatus = '/erp/qrn/setStatus',
  qrndetails = '/erp/qrn/details',
  qrnsetCancel = '/erp/qrn/setCancel',
  qrnsetQcAbnormalRemark = '/erp/qrn/setQcAbnormalRemark',
  qrnupdate = '/erp/qrn/update',
  qrnsetProgramStatus = '/erp/qrn/setProgramStatus'
}
// 质检单列表
export const getQcList = (params, responseType = 'json', opt = {}) => defHttp.get({ url: Api.GetQcList, params, responseType }, opt)

// 新增质检单
export const addOrEditQc = (data) => defHttp.post({ url: Api.AddOrEditQc, data })

// 质检单详情
export const getQcDetail = (params: { id: number }) => defHttp.get({ url: Api.GetQcDetail, params })

// 质检单审核
export const setQcStatus = (params: { id: number; status: 1 | 0 }) => defHttp.get({ url: Api.SetQcStatus, params })

// 质检单删除
export const delQc = (data: { id: number }) => defHttp.post({ url: Api.DelQc, data })

// 质检单废弃
export const nullifyQc = (params: { id: number; cancel_remark: string }) => defHttp.get({ url: Api.NullifyQc, params })
// 质检单更改图片和视频
export const QrsetImagesAndVideos = (params?: {}) =>
  defHttp.post({ url: Api.QrsetImagesAndVideos, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

//新版
export const qrngetList = (params?: {}, responseType = 'json', opt = {}) => defHttp.get({ url: Api.qrngetList, params, responseType }, opt)
export const qrndeleteReport = (params?: {}) =>
  defHttp.get({ url: Api.qrndeleteReport, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const qrnsetStatus = (params?: {}) =>
  defHttp.get({ url: Api.qrnsetStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const qrndetails = (params?: {}) => defHttp.get({ url: Api.qrndetails, params })
export const qrnsetCancel = (params?: {}) =>
  defHttp.get({ url: Api.qrnsetCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const qrnsetQcAbnormalRemark = (params?: {}) =>
  defHttp.get({ url: Api.qrnsetQcAbnormalRemark, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const qrnupdate = (params?: {}) =>
  defHttp.post({ url: Api.qrnupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const qrnsetProgramStatus = (params?: {}) =>
  defHttp.post({ url: Api.qrnsetProgramStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
