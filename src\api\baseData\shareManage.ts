//分摊管理
import { defHttp } from '/@/utils/http/axios'
import type { IShareManageList, ICreateShareManage } from './model/shareManage'
import { BasicPageParams } from '../model/baseModel'

enum Api {
  GetShareManageList = '/erp/share/getList',
  UpdateShareStatus = '/erp/share/setStatus',
  CreateShareManage = '/erp/share/create',
  ShareManageDetail = '/erp/share/details'
}

export const getShareManageList = (params: Partial<IShareManageList> & Partial<BasicPageParams>) =>
  defHttp.get({ url: Api.GetShareManageList, params })

export const updateShareStatus = (params: { id: number; status: 0 | 1 }) => defHttp.get({ url: Api.UpdateShareStatus, params })

export const createShareManage = (params?: ICreateShareManage) =>
  defHttp.post({ url: Api.CreateShareManage, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const shareManageDetail = (params: { id: number }) => defHttp.get({ url: Api.ShareManageDetail, params })
