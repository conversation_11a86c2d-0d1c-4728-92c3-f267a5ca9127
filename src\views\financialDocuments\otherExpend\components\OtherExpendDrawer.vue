<template>
  <BasicDrawer @register="registerDrawer" :visible="false" v-bind="$attrs" showFooter width="90%" @ok="debouncehandleOk">
    <Descriptions title="资金流水" v-if="getcapitalDetails" bordered class="mb-10">
      <DescriptionsItem label="流水日期">{{ init_record.occurrence_at }}</DescriptionsItem>
      <DescriptionsItem label="流水金额">{{ formateerNotCurrency.format(init_record.amount) }}</DescriptionsItem>
      <DescriptionsItem label="流水剩余金额">{{ formateerNotCurrency.format(init_record.amount_left) }}</DescriptionsItem>
    </Descriptions>
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>

    <Descriptions title="支出明细" />
    <DescriptionsItem>
      <BasicTable @register="register" :canResize="false">
        <template #toolbar>
          <Popover placement="left" trigger="click" v-model:visible="texdisabled" @visible-change="handleVisibleChange">
            <template #content>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <span>小票单号</span>
                  <PagingApiSelect v-model:value="receiptID" v-bind="purchasePagingSelectConfig" />
                </div>
                <div class="w-[300px] mr-2">
                  <span>部门</span>
                  <Input v-model:value="department" disabled />
                </div>
              </div>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <span>小票金额</span>
                  <InputNumber v-model:value="income" disabled controls :precision="2" :min="0" />
                </div>
                <div class="w-[300px] mr-2">
                  <span>绑定项目</span>
                  <PagingApiSelect v-model:value="projectID" v-bind="projectPagingSelectConfig" />
                </div>
              </div>
              <div class="flex justify-end mt-2">
                <a-button type="primary" @click="handleAddGoods">确定添加</a-button>
              </div>
            </template>
            <Button type="primary" :disabled="csvalue !== 5">餐厅报销新增</Button>
          </Popover>
          <Button type="primary" :disabled="csvalue == 5 ? true : disabledaction" @click="handleAdd">新增</Button>
          <Dropdown :disabled="csvalue == 5 ? true : disabledaction">
            <a class="ant-dropdown-link" @click.prevent>
              <a-button>
                <template #icon><download-outlined /> </template>明细文件 <download-outlined />
              </a-button>
            </a>
            <template #overlay>
              <Menu @click="handleMenuClick">
                <MenuItem key="upload"><upload-outlined /> 导入明细</MenuItem>
                <MenuItem key="export"><download-outlined />模板</MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
          </template>
          <template v-if="column.key === 'is_check'">
            <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
          </template>
          <template v-if="column.key === 'is_check2'">
            <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
          </template>
          <template v-if="column.key === 'files'">
            <div v-for="(newVal, index) in record.files" :key="index">
              <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
            >
          </template>
        </template>
      </BasicTable>
    </DescriptionsItem>
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
    <UploadModal @register="registerUploadFilsModal" @success="handleSuccess" />
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>
<script lang="tsx" setup>
import { ref, unref, reactive, onBeforeUnmount } from 'vue'
import { cloneDeep, debounce } from 'lodash-es'
import {
  Button,
  Descriptions,
  DescriptionsItem,
  Upload,
  UploadFile,
  message,
  Tag,
  Dropdown,
  MenuItem,
  Menu,
  Popover,
  Input,
  InputNumber
} from 'ant-design-vue'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { postaddExpendList, postupdateExpendList } from '/@/api/financialDocuments/otherExpend'
import { detailsList } from '/@/api/financialDocuments/otherExpend'
import { useMessage } from '/@/hooks/web/useMessage'
import { getDept } from '/@/api/erp/systemInfo'
import {
  updateFormSchemaFn,
  updataexpenseDetails,
  excelHeader,
  inChargeList,
  getWorkListFn,
  purchasePagingSelectConfig,
  projectPagingSelectConfig,
  department,
  income,
  purchasedata
} from '../datas/drawer'
import { checkMap } from '../datas/datas'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { watch } from 'vue'
import UploadModal from './DrawerUploadModal.vue'
import { useModal } from '/@/components/Modal'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { RouteLocationNormalizedLoaded, useRoute } from 'vue-router'
import { ImpExcelModal } from '/@/components/Excel'
import { transformData2Import, IMP_EXCEL_END } from '../datas/importModal'
import { createDisClause, stockodcreateSplit } from '/@/api/financialDocuments/capitalFlow'
import defaultUser from '/@/utils/erp/defaultUser'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { add, sub } from '/@/utils/math'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getProjectSalesOrder } from '/@/api/projectOverview'
import { getWorkList } from '/@/api/commonUtils'
import dayjs from 'dayjs'
//路由
const route = useRoute()
const routeName = unref<RouteLocationNormalizedLoaded>(route).name

//附件
const filesList = ref<UploadFile[]>([])
//子传父
const emit = defineEmits(['success', 'register'])
//保存点击,其他禁用
const currentEditKeyRef = ref('')
//form初始化
const [registerForm, { resetFields, setFieldsValue, validate, resetSchema, getFieldsValue }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 4 }
})
//获取状态
const isUpdate = ref(false)
//类别
const istype = ref()
//传数据
let recordData = reactive<any>({})
const { createMessage: msg } = useMessage()
// //提示
// const meesg = ref(false)
//明细table
const tabeldata = ref<any>([])
//保存
const save = ref(false)
//work列表
const saleslist = ref<Array<any>>([])
//资金流水生成接口
const getcapitalDetails = ref(false)
//资金流水id
const capitalId = ref(0)
//资金流水剩余金额
const capital_cost = ref(0)

const formexchangerate = ref('1.000000')
const formcurrency = ref('人民币')

//直接流水数据
const init_record = ref()
//disabled
const disabledaction = ref(false)
//分摊部门
const departmentArray = ref([])
//分摊部门
const departmentidArray = ref([])
//分摊人员
const personnelArray = ref([])
//普通record
const props_record = ref()
//获取drawer数据
const csvalue = ref()
//按部门
const isdeptbatch = ref(0)
//餐厅报销
const texdisabled = ref(false)
const receiptID = ref()
const projectID = ref()

const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    console.log(data)

    save.value = false
    show.value = false
    disabledaction.value = false
    resetFields()
    changeLoading(true)
    currentEditKeyRef.value = ''
    filesList.value = []
    isUpdate.value = data.isUpdate
    istype.value = data.type
    saleslist.value = []
    deletearr.value = []
    props_record.value = data.record
    csvalue.value = 1
    departmentArray.value = []
    personnelArray.value = []

    setColumns(
      await updataexpenseDetails(
        [],
        getfundDetails,
        data.type,
        data.record?.order,
        data.record?.is_check == 2 || data.record?.is_check2 == 2
      )
    )
    await resetSchema(
      await updateFormSchemaFn(
        routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0,
        data.record && data.type == 'add' ? true : false,
        data.type
      )
    )
    if (unref(isUpdate)) {
      // setFieldsValue(data.record)
      filesList.value = data.record?.files?.map((item) => ({ url: item, name: item, uid: item }))
      csvalue.value = data.record.order
      // 异步获取详细列表
      const list = await detailsList({ id: data.record.id })
      // 初始化表格数据
      tabeldata.value = cloneDeep(list.items.sales)
      recordData = list.items
      // 处理状态数据
      tabeldata.value.forEach((val) => {
        if (val.is_check2 === 2) {
          save.value = true
          disabledaction.value = true
          val.reject_remark = list.items.reject_remark
        }
        if (!val.parent_strid) {
          val.parent_strid = null
        }
      })
      // 计算初始成本
      const initcost = tabeldata.value.filter((item) => item.is_cancel !== 1).reduce((pre, item) => add(pre, item.amount, 2), 0)
      const initforeign = tabeldata.value
        .filter((item) => item.is_cancel !== 1)
        .reduce((pre, item) => add(pre, item.foreign_currency_amount, 2), 0)
      await setFieldsValue({
        sales_work_id: list.items?.sales_work_id,
        doc_source_uniqid: list.items?.doc_source_uniqid,
        ...data.record,
        rate: data.record.exchange_rate,
        cost: initcost,
        foreign_currency_amount: initforeign
      })
      setTableData(tabeldata.value)
      reload()
    } else {
      if (data.record) {
        init_record.value = data.record
        getcapitalDetails.value = true
        capitalId.value = data.record.id
        capital_cost.value = data.record.amount_left ? Number(data.record.amount_left) : 0
        await setFieldsValue({
          collection_at: data.record.occurrence_at
        })
      } else {
        getcapitalDetails.value = false
      }
      setTableData([])
    }

    if ((data.type = 'add')) {
      setFieldsValue({
        applicant: defaultUser!.userId
      })
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})
//初始化tabel
const [
  register,
  { setTableData, deleteTableDataRecord, getDataSource, updateTableDataRecord, getColumns, setColumns, reload, setLoading }
] = useTable({
  title: '',
  showIndexColumn: false,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})
//action
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || disabledaction.value,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        label: '取消明细',
        color: 'error',
        ifShow: record.is_check == 2 && record.is_check2 == 2 && !record.is_cancel,
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        popConfirm: {
          okText: '确定',
          title: '请检查当前取消的明细是否正确,取消过后不能在进行更改,是否取消当前明细',
          placement: 'left',
          confirm: Canceldetail.bind(null, record)
        }
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'right',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || disabledaction.value,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel
        ifShow: record.is_check2 !== 2 && !record.is_cancel
      },
      {
        label: '附件',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || disabledaction.value,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleAddFile.bind(null, record)
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || disabledaction.value,
        ifShow: record.is_check2 !== 2 && !record.is_cancel && record.is_check !== 2,
        // ifShow: !(record.is_check == 2) && record.is_check2 !== 2,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}
function createDropDownActions(record) {
  return [
    {
      label: '部门分摊',
      // onClick: handlefundDetails.bind(null, record),
      popConfirm: {
        title: (
          <div class="w-100">
            <div style={{ fontSize: '18px', color: 'red' }}>请选择分摊部门,进行金额的平均分摊,可多选部门</div>
            <PagingApiSelect
              placeholder="请选择分摊部门"
              api={(params) => getDept({ ...params, status: 1, is_show: 1, is_audit: 1 })}
              pagingMode={true}
              search-mode={true}
              always-load={false}
              v-model:value={departmentArray.value}
              return-params-field="id"
              mode="multiple"
              onChange={(_, shall) => {
                departmentidArray.value = shall.map((item) => ({
                  name: item.name,
                  id: item.id
                }))
              }}
              select-props={{ fieldNames: { value: 'name', label: 'name' }, placeholder: '请选择', style: { width: '100%', zIndex: 9999 } }}
              resultField="items"
            />
          </div>
        ),
        confirm: handleapportion.bind(null, record, 'department'),
        placement: 'left',
        disabled: csvalue.value !== 4 || !record.amount || record.is_check2 == 2 || record.is_check == 2
      }

      // disabled: recordData.order == 2
    },
    {
      label: '人员分摊',
      popConfirm: {
        title: (
          <div class="w-100">
            <div style={{ fontSize: '18px', color: 'red' }}>请选择分摊人员,进行金额的平均分摊,可多选人员</div>
            <PagingApiSelect
              placeholder="请选择分摊人员"
              api={(params) => getCreatorList({ ...params, status: 1 })}
              pagingMode={true}
              search-mode={true}
              always-load={false}
              v-model:value={personnelArray.value}
              return-params-field="id"
              mode="multiple"
              select-props={{ fieldNames: { value: 'name', label: 'name' }, placeholder: '请选择', style: { width: '100%', zIndex: 9999 } }}
              resultField="items"
              onChange={(_, shall) => {
                if (!shall) return
                shall.forEach((val) => {
                  if (!inChargeList.value.some((item) => item.id === val.id)) {
                    inChargeList.value.push(val)
                  }
                })
              }}
            />
          </div>
        ),
        confirm: handleapportion.bind(null, record, 'share_inCharge'),
        placement: 'left',
        disabled: csvalue.value !== 4 || !record.amount || record.is_check2 == 2 || record.is_check == 2
      }
    },
    {
      label: '项目分摊',
      onClick: handlefundDetails.bind(null, record),
      placement: 'left',
      disabled:
        csvalue.value !== 1 || !record.amount || record.is_check2 == 2 || record.is_check == 2 || currentEditKeyRef.value
          ? true
          : false || disabledaction.value
    }
  ]
}
//
//修改数据
async function getfundDetails(val: any) {
  if (!val) return
  if (val.account_name) {
    updateTableDataRecord(currentEditKeyRef.value, { account_code: val.account_code })
    return
  }
  const newcolumns = getColumns()

  if (val.value === 5) {
    newcolumns.forEach((item) => {
      if (item.dataIndex === 'corres_pondent') {
        item.editComponent = 'Input'
        item.editComponentProps = {}
      }
    })
  } else {
    newcolumns.forEach((item) => {
      if (item.dataIndex === 'corres_pondent') {
        item.editComponent = 'PagingApiSelect'
        item.editComponentProps = (fromat) => {
          return {
            api: getWorkListFn(fromat.record.corres_type),
            resultField: 'items',
            searchMode: true,
            pagingMode: true,
            params: {
              type: 3,
              status: [1, 3, 4, 5, 15]
            },
            selectProps: {
              fieldNames: {
                key: 'key',
                value: 'name',
                label: 'name'
              },
              showSearch: true,
              placeholder: '请选择',
              optionFilterProp: 'name',
              allowClear: true,
              style: {
                width: '100%'
              }
            }
          }
        }
      }
    })
  }
  await setColumns(newcolumns)
}

// 存储编辑前的record
const beforeRecord = ref()
const show = ref(false)
async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
  save.value = true
  show.value = true
  const newcolumns = getColumns()

  if (record.corres_type === 5) {
    newcolumns.forEach((item) => {
      if (item.dataIndex === 'corres_pondent') {
        item.editComponent = 'Input'
        item.editComponentProps = {}
      }
    })
  } else {
    newcolumns.forEach((item) => {
      if (item.dataIndex === 'corres_pondent') {
        item.editComponent = 'PagingApiSelect'
        item.editComponentProps = (fromat) => {
          return {
            api: getWorkListFn(fromat.record.corres_type),
            resultField: 'items',
            searchMode: true,
            pagingMode: true,
            params: {
              type: 3,
              status: [1, 3, 4, 5, 15]
            },
            selectProps: {
              fieldNames: {
                key: 'key',
                value: 'name',
                label: 'name'
              },
              showSearch: true,
              placeholder: '请选择',
              optionFilterProp: 'name',
              allowClear: true,
              style: {
                width: '100%'
              }
            }
          }
        }
      }
    })
  }
  await setColumns(newcolumns)
}

function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    amount: beforeRecord.value.amount,
    source_uniqid: beforeRecord.value.source_uniqid,
    account_name: beforeRecord.value.account_name,
    account_code: beforeRecord.value.account_code,
    department: beforeRecord.value.department,
    clear_department: beforeRecord.value.clear_department,
    desc: beforeRecord.value.desc,
    parent_strid: beforeRecord.value.parent_strid
  })

  record.onEdit?.(false, false)
  save.value = false
  show.value = false
}

async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      const errorMessages = {
        1: '当前订单类型销售单号必选',
        2: '当前订单类型采购单号必选',
        3: '当前订单类型OA单号必选'
      }

      // 检查订单类型的特定字段
      if ([1, 2].includes(csvalue.value) && !record.parent_strid) {
        return message.error({ content: errorMessages[csvalue.value] })
      }
      if (csvalue.value === 3 && !record.order_no) {
        return message.error({ content: errorMessages[csvalue.value] })
      }

      // 检查通用字段
      const validations = [
        { field: record.account_code || record.account_name, message: '明细当中支出科目为必填项,请完成填写' },
        { field: record.desc, message: '明细当中摘要为必填项,请完成填写' },
        {
          field: !(!record.foreign_currency_amount && record.exchange_rate !== '1.000000'),
          message: '明细当中外币金额为必填项,请完成填写'
        },
        { field: record.amount, message: '明细当中支出金额为必填项,请完成填写' },
        { field: record.department, message: '明细当中支出部门为必填项,请完成填写' },
        { field: !(record.corres_type && !record.corres_pondent), message: '选取往来单位' },
        { field: !(!record.corres_type && record.corres_pondent), message: '选取往来单位类型' }
      ]

      for (const validation of validations) {
        console.log(validation.field)

        if (!validation.field) {
          message.error({ content: validation.message })
          save.value = true
          return
        }
      }

      const da = await getDataSource()
      const costdata = da
        .filter((item) => {
          return !item.is_cancel && item.amount
        })
        .reduce((pre, item) => {
          return add(pre, item.amount, 2)
        }, 0)

      const foreigndata = da
        .filter((item) => {
          return !item.is_cancel && item.foreign_currency_amount
        })
        .reduce((pre, item) => {
          return add(pre, item.foreign_currency_amount, 2)
        }, 0)
      setFieldsValue({ cost: costdata, foreign_currency_amount: foreigndata })
      save.value = false
      show.value = false
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      msg.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      msg.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    msg.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//添加明细
async function handleAdd() {
  const newRowDataItem = {
    parent_strid: null,
    source_uniqid: null,
    account_name: null,
    exchange_rate: formexchangerate.value,
    currency: formcurrency.value,
    foreign_currency_amount: 0
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}
//复制明细
function handlecopylink(record) {
  const newrecord = formatObject(record)
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
  costprice()
}
//添加附件
const [registerUploadFilsModal, { openModal: openModalUplad }] = useModal()
function handleAddFile(record) {
  openModalUplad(true, record)
}
const deletearr = ref<any>([])
//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
  if (record.id && istype.value !== 'add') {
    const dataobj = {
      account_code: record.account_code,
      account_name: record.account_name,
      amount: record.amount,
      dept_id: record.dept_id,
      id: record.id,
      parent_id: record.parent_id,
      type: 3,
      work_id: record.work_id
    }
    deletearr.value.push(dataobj)
  }
  costprice()
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
//计算cost价格
function costprice() {
  const da = getDataSource()
  const costdata = da
    .filter((item) => {
      return !item.is_cancel && item.amount
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)
  const foreigndata = da
    .filter((item) => {
      return !item.is_cancel && item.foreign_currency_amount
    })
    .reduce((pre, item) => {
      return add(pre, item.foreign_currency_amount, 2)
    }, 0)
  setFieldsValue({ cost: costdata, foreign_currency_amount: foreigndata })
}

//提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const values = await validate()
    values['is_finance'] = routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0
    values['id'] = recordData.id
    delete values.doc_source_uniqid
    delete values.rate
    const isChancel: any = ref([])
    if (show.value) {
      throw new Error('请先保存')
    }
    //明细填写
    const mesg = ref()
    //获取保存的数据
    const tabledatas: any = await formatSubmit()
    const showdata = ref<any>([])
    const filesboolean = tabledatas.find((obj: any) => obj.files && obj.files.length > 0)
    if (!filesboolean && (!values.files || values.files.length == 0)) {
      throw new Error('票据附件与明细附件至少上传一个')
    }

    for (let item of tabledatas) {
      if (!item.account_code || !item.account_name || !item.department) {
        save.value = true
        throw new Error('请填写完整信息,不能提交空明细')
      } else {
        save.value = false
      }
      if (item.is_check2 == 2 && item.is_cancel !== 1) {
        throw new Error('请先取消明细在提交')
      }

      if (item.is_cancel == 1) {
        isChancel.value.push(item.is_cancel)
      }

      if (istype.value !== 'add') {
        tabeldata.value.forEach(() => {
          if (item.strid && item.parent_strid && !item.par_work_id) {
            Reflect.set(item, 'par_work_id', item.parent_id)
          }
          if (!item.par_work_id) {
            return Reflect.set(item, 'par_work_id', null)
          }
        })
        Reflect.set(item, 'type', item.strid ? 2 : 1)
      }

      if (item.share_inCharge_name) {
        inChargeList.value.forEach((items) => {
          if (items.name == item.share_inCharge_name) {
            item.share_inCharge = items.id
          }
        })
      }
      const { corres_type, share_source, share_inCharge_name } = item
      item.corres_type = corres_type || null
      item.share_source = share_source || null
      item.share_inCharge = share_inCharge_name ? item.share_inCharge : null
      item.is_last_disburse = item.is_last_disburse || null
      // 判断是否有值，如果是空字符串则设为0，其他情况保持原值
      item.foreign_currency_amount = item.foreign_currency_amount === '' ? 0 : item.foreign_currency_amount || 0
      showdata.value.push(item.is_check2)
    }

    if (deletearr.value.length > 0) {
      tabledatas.push(...deletearr.value)
    }

    tabledatas.forEach((item: any) => {
      delete item.strid
      delete item.department
      delete item.clear_department
      delete item.source_uniqid
      delete item.parent_strid
      delete item.parent_id
      delete item.reject_remark1
      delete item.reject_remark2
      delete item.rate
      delete item.currency
      delete item.share_inCharge_name
      if (isdeptbatch.value == 0) {
        delete item.inCharge
      }
    })
    const params = {
      sales: tabledatas || [],
      doc: values
    }
    if (isUpdate.value && isChancel.value.length > 0 && isChancel.value.length == tabledatas.length) {
      if (new Set(isChancel.value).size == 1) {
        params.doc['status'] = 16
      }
    }
    if (values.order !== 5) {
      if (values.cost <= 0) {
        throw new Error('本单的应付金额应大于0')
      }
    }
    if (values.order == 5) {
      if (values.cost !== 0) {
        throw new Error('本单的应付金额应等于0')
      }
    }

    if (params.sales.every((item: any) => item.type == 3) || params.sales.length == 0) {
      throw new Error('请完整的填入支出明细并保存')
    }
    console.log(params)
    //创建
    if (save.value == false) {
      delete params.doc.is_dept_batch
      unref(isUpdate) ? null : delete params.doc.id
      isdeptbatch.value == 0 ? null : delete params.doc.cost

      if (!unref(isUpdate)) {
        if (getcapitalDetails.value) {
          params.sales.forEach((item: any) => {
            return delete item.type
          })
          if (params.doc.cost > capital_cost.value) {
            throw new Error('支出金额不能大于剩余金额')
          }
        }
        mesg.value = getcapitalDetails.value
          ? await createDisClause({ ...params, fund_id: capitalId.value })
          : isdeptbatch.value == 0
          ? await postaddExpendList({ ...params })
          : await stockodcreateSplit({ ...params })
      } else {
        console.log('1')
        mesg.value = await postupdateExpendList({ ...params })
      }
      if (mesg.value.type == 'success' || mesg.value.news == 'success') {
        msg.success({ content: '提交成功' })
        closeDrawer()
        emit('success')
        close()
        setTimeout(() => {
          changeOkLoading(false)
        }, 1000)
      } else {
        throw new Error(mesg.value.message)
      }
    } else {
      save.value = false
      throw new Error('请先检查数据是否完整')
    }
  } catch (err: any) {
    changeOkLoading(false)
    if (!err.message) {
      message.error(`请先填写信息,在提交`)
    } else {
      message.error(`${err.message}`)
    }
    throw new Error(`${err}`)
  }
}
const debouncehandleOk = debounce(handleOk, 200)

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    changeOkLoading(true)
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return {
        url: (item.url as string) || (item.response as string),
        uid: item.uid,
        name: item.name
      }
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
//取消时间
async function Canceldetail(record) {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = String(currentDate.getMonth() + 1).padStart(2, '0')
  const day = String(currentDate.getDate()).padStart(2, '0')
  const hours = ('0' + currentDate.getHours()).slice(-2)
  const minutes = ('0' + currentDate.getMinutes()).slice(-2)
  const seconds = ('0' + currentDate.getSeconds()).slice(-2)
  const datasource = await getDataSource()
  const costdata = datasource
    .filter((item) => {
      return item.id !== record.id && !item.is_cancel
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)
  const foreigndata = datasource
    .filter((item) => {
      return item.id !== record.id && !item.is_cancel
    })
    .reduce((pre, item) => {
      return add(pre, item.foreign_currency_amount, 2)
    }, 0)
  setFieldsValue({ cost: costdata, foreign_currency_amount: foreigndata })

  record.cancel_at = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  record.is_cancel = 1
  reload()
}
//明细附件上传成功
function handleSuccess(record) {
  handleSave(record)
}
//导入
//execl上传
const [registerUploadModal, { openModal }] = useModal()
function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader)
  } else if (key === 'upload') {
    openModal(true, {
      sheetName: 'Sheet1',
      headerRow: 2,
      startCell: 'A3',
      endCell: `E${IMP_EXCEL_END}`
    })
  }
}
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  const hide = message.loading('正在导入数据，请稍后...', 0)
  const fromdata = getFieldsValue()
  try {
    const newData = await transformData2Import(data, fromdata)
    //看导入前table是否有数据
    const initn_table = await formatSubmit()
    if (initn_table) {
      newData.unshift(...initn_table)
    }
    await setTableData(newData)
    await costprice()
    hide()
    currentEditKeyRef.value = ''
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)

    hide()
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.reject('Fail')
  }
}

//附件预览

// 预览
const { createMessage } = useMessage()
const [registerModal, { openModal: openMODAL }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openMODAL(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

//分摊部门
function handleapportion(record, type: string) {
  setLoading(true)
  currentEditKeyRef.value = ''
  const Arraylist = type == 'department' ? departmentArray.value : personnelArray.value
  console.log(departmentidArray.value)

  if (type !== 'department' && !record.department) {
    return message.error('请先选择部门在进行人员分摊')
  }
  const totalamount = Number(record.amount)
  //评价值
  const average = Math.round(totalamount / Arraylist.length)
  //最后一个值
  const lastamount = sub(totalamount, average * (Arraylist.length - 1), 2)

  const newrecord = formatObject(record)
  const newRowDataItem = {
    ...newrecord,
    amount: average
  }
  const lastNewRowDataItem = {
    ...newRowDataItem,
    amount: lastamount
  }
  let dataSource = cloneDeep(getDataSource())
  // 添加所有的部门，除了最后一个
  Arraylist.slice(0, -1).forEach((item) => {
    const row = type == 'department' ? { ...newRowDataItem, department: item } : { ...newRowDataItem, share_inCharge_name: item }
    dataSource = [...dataSource, row]
  })

  // 添加最后一个部门
  const lastRow =
    type == 'department'
      ? { ...lastNewRowDataItem, department: Arraylist[Arraylist.length - 1] }
      : { ...lastNewRowDataItem, share_inCharge_name: Arraylist[Arraylist.length - 1] }
  dataSource = [...dataSource, lastRow]
  if (type == 'department') {
    const updatedDataSource = dataSource.map((item) => {
      const matchedDept = departmentidArray.value.find((dept) => dept.name === item.department)
      if (matchedDept) {
        return {
          ...item,
          dept_id: matchedDept.id
        }
      }
      return item
    })
    setTableData(updatedDataSource)
  } else {
    setTableData(dataSource)
  }

  deleteTableDataRecord(record.key)
  if (record.id && istype.value !== 'add') {
    const dataobj = {
      account_code: record.account_code,
      account_name: record.account_name,
      amount: record.amount,
      dept_id: record.dept_id,
      id: record.id,
      parent_id: record.parent_id,
      type: 3,
      work_id: record.work_id
    }
    deletearr.value.push(dataobj)
  }
  costprice()
  setLoading(false)
}
//项目分摊
async function handlefundDetails(record) {
  try {
    setLoading(true)
    disabledaction.value = true
    //获取项目
    const { items: workitems } = await getWorkList({ type: 3, id: record.parent_id ?? record.par_work_id })
    const { items } = await getProjectSalesOrder({ id: workitems[0].project_number, pageSize: 999 })
    //区分数组
    const datasura = items.filter((item) => item.status > 0)
    //计算总额
    const totalAmount = datasura.reduce((pre, cur) => {
      return pre + Number(cur.receivable_left)
    }, 0)
    //计算占比
    for (const val of datasura) {
      val.percent = Math.round((Number(val.receivable_left) / totalAmount) * record.amount)
    }
    datasura[datasura.length - 1].percent = sub(
      record.amount,
      datasura.slice(0, -1).reduce((pre, cur) => {
        return pre + Number(cur.percent)
      }, 0),
      2
    )
    //表格渲染新数据
    const newrecord = formatObject(record)
    delete newrecord.id
    const tabled = datasura.map((item) => {
      return {
        ...newrecord,
        par_work_id: item.id,
        parent_strid: item.source_uniqid,
        amount: item.percent,
        department: item.department,
        dept_id: item.dept_id,
        clear_department: item.operation_name,
        clear_dept_id: item.operation
      }
    })
    const dataSource = [...tabled].concat(cloneDeep(getDataSource())).filter((item) => item.key !== record.key)
    setTableData(dataSource)
    setLoading(false)
    disabledaction.value = false
    //删除时
    if (record.id && istype.value !== 'add') {
      const dataobj = {
        account_code: record.account_code,
        account_name: record.account_name,
        amount: record.amount,
        dept_id: record.dept_id,
        id: record.id,
        parent_id: record.parent_id,
        type: 3,
        work_id: record.work_id
      }
      deletearr.value.push(dataobj)
    }
    costprice()
  } catch (e) {
    console.log(e)
    setLoading(false)
    disabledaction.value = false
  }
}

async function handleFieldValueChange(key, value) {
  if (key === 'order') {
    csvalue.value = value
    currentEditKeyRef.value = ''
    const datasource = formatSubmit()
    const newdata = datasource
      .filter((item: any) => item.id)
      .map((item) => {
        return {
          ...item,
          type: 3
        }
      })
    istype.value !== 'add' ? deletearr.value.push(...newdata) : ''
    setTableData([])
    await setColumns(
      await updataexpenseDetails(
        [],
        getfundDetails,
        istype.value,
        value,
        props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
      )
    )
    disabledaction.value = value == 5 ? true : false
  }
  if (key == 'is_dept_batch') {
    isdeptbatch.value = value
  }
  if (key == 'exchange_rate' || key == 'rate') {
    const data = getDataSource()
    const formdata = await getFieldsValue()
    formexchangerate.value = value
    formcurrency.value = formdata.currency
    data.forEach((item) => {
      item.exchange_rate = value
      item.currency = formdata.currency
      item.amount = undefined
      item.foreign_currency_amount = undefined
    })
    const newcolumns = await updataexpenseDetails(
      [],
      getfundDetails,
      istype.value,
      value,
      props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
    )
    newcolumns.map((item) => {
      if (item.dataIndex == 'foreign_currency_amount') {
        item.defaultHidden = !['人民币', 'CNY'].includes(formdata.currency) ? false : true
      }
    })
    await setColumns(newcolumns)
    setFieldsValue({ cost: 0, foreign_currency_amount: 0 })
    setTableData(data)
  }
}
// 关闭抽屉时重置状态
function close() {
  // 重置所有响应式变量
  filesList.value = []
  isUpdate.value = false
  istype.value = undefined
  recordData = {}
  tabeldata.value = []
  save.value = false
  saleslist.value = []
  getcapitalDetails.value = false
  capitalId.value = 0
  capital_cost.value = 0
  init_record.value = undefined
  disabledaction.value = false
  departmentArray.value = []
  personnelArray.value = []
  props_record.value = undefined
  csvalue.value = undefined
  isdeptbatch.value = 0
  currentEditKeyRef.value = ''
  beforeRecord.value = undefined
  show.value = false
  deletearr.value = []
  formexchangerate.value = '1.000000'
  formcurrency.value = '人民币'
  console.log('抽屉关闭，所有状态已重置')
}

onBeforeUnmount(() => {
  isdeptbatch.value = 0
  close()
})

//餐厅
function handleVisibleChange(value) {
  texdisabled.value = csvalue.value == 5 ? value : false
}

async function handleAddGoods() {
  // 验证必填字段
  if (!receiptID.value) return message.warning('请先选择小票单号')
  if (!projectID.value) return message.warning('请先选择绑定项目')

  const { items } = await getProjectSalesOrder({ id: projectID.value, pageSize: 999 })
  if (!items || items.length === 0) {
    return message.warning('未找到相关销售订单数据')
  }
  console.log(items)

  // 获取需要分配的收入金额
  const incomeAmount = Number(income.value || 0)
  if (incomeAmount <= 0) {
    return message.warning('收入金额必须大于0')
  }

  //区分数组
  const datasura = items.filter((item) => item.status > 0)

  //计算总额
  const totalAmount = datasura.reduce((pre, cur) => {
    return pre + Number(cur.receivable)
  }, 0)
  if (totalAmount <= 0) {
    return message.warning('销售订单总金额必须大于0')
  }

  for (const val of datasura) {
    val.percent = Math.round((Number(val.receivable) / totalAmount) * incomeAmount)
  }
  datasura[datasura.length - 1].percent = sub(
    incomeAmount,
    datasura.slice(0, -1).reduce((pre, cur) => {
      return pre + Number(cur.percent)
    }, 0),
    2
  )
  console.log(datasura)

  const tabled = datasura.map((item) => {
    return {
      par_work_id: item.id,
      parent_strid: item.source_uniqid,
      amount: item.percent,
      department: item.department,
      dept_id: item.dept_id,
      clear_department: item.operation_name,
      clear_dept_id: item.operation,
      account_name: '其他业务支出-客户招待费',
      exchange_rate: '1.000000',
      currency: '人民币',
      account_code: '29',
      desc: `票号:${purchasedata.value.order_number},挂账金额:${purchasedata.value.income}元,分摊时间:${dayjs(new Date()).format(
        'YYYY-MM-DD'
      )}`
    }
  })
  tabled.unshift({
    dept_id: purchasedata.value.sign_dept_id,
    department: purchasedata.value.department,
    account_name: purchasedata.value.category,
    account_code: purchasedata.value.category_id.toString(),
    amount: Number(-purchasedata.value.income),
    exchange_rate: '1.000000',
    currency: '人民币',
    desc: `票号:${purchasedata.value.order_number},挂账金额:${purchasedata.value.income}元,分摊时间:${dayjs(new Date()).format(
      'YYYY-MM-DD'
    )}`
  })
  const dataSource = [...tabled].concat(cloneDeep(getDataSource())).filter((item) => {
    // 将 receivable 转为数字并检查是否不为0
    const numAmount = Number(item.amount)
    return !isNaN(numAmount) && numAmount !== 0
  })
  setTableData(dataSource)
  setLoading(false)
  disabledaction.value = false
  costprice()
}
</script>
