import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetSalesList = '/erp/stock/getSaleList',
  SetSaleRemark = '/erp/stock/setSaleRemark'
}

export const getSaleList = (params: {
  source_uniqid: string
  is_have_stocking: number
  is_have_no_Inventory: number
  page: number
  pageSize: number
  status: number
}) =>
  defHttp.get({
    url: Api.GetSalesList,
    params
  })

export const setSaleRemark = (data: { id: number; remark: string; true_quantity: number }) => defHttp.post({ url: Api.SetSaleRemark, data })
