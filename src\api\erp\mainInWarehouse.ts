import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetMainInWarehouse = '/erp/stock/ew/getList',
  EditUpdateInWarehouseNew = '/erp/stock/ew/updateNew',
  ApproveInWarehouse = '/erp/stock/ew/setStatusNew'
}

export const getMainInWarehouse = (params) => defHttp.get({ url: Api.GetMainInWarehouse, params })

// 编辑或更新主入库单
export const editUpdateInWarehouseNew = (data) => defHttp.post({ url: Api.EditUpdateInWarehouseNew, data })

// 主入库单审核
export const approveInWarehouse = (data: { doc_id: number; status: number; volume?: number; weight?: number }) =>
  defHttp.post({ url: Api.ApproveInWarehouse, data })
