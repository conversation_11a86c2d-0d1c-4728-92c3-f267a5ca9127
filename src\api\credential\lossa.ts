import { defHttp } from '/@/utils/http/axios'

enum Api {
  getplList = '/erp/cert/pl/getList',
  checkOutpl = '/erp/cert/pl/checkOut',
  setStatuspl = '/erp/cert/pl/setStatus',
  setdelete = '/erp/cert/pl/delete'
}

export const getplList = (params?: {}) => defHttp.get({ url: Api.getplList, params })

export const checkOutpl = (params?: {}) =>
  defHttp.get({ url: Api.checkOutpl, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const setStatuspl = (params?: {}) =>
  defHttp.get({ url: Api.setStatuspl, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const setdelete = (params?: {}) =>
  defHttp.get({ url: Api.setdelete, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
