import { defHttp } from '/@/utils/http/axios'

enum Api {
  meituanmfgetOrderList = '/meituan/mf/getOrderList',
  meituanmfcreateOrderToInfoCert = '/meituan/mf/createOrderToInfoCert'
}

export const meituanmfgetOrderList = (params?: {}) => {
  return defHttp.get({
    url: Api.meituanmfgetOrderList,
    params
  })
}
export const meituanmfcreateOrderToInfoCert = (params?: {}) => {
  return defHttp.get({
    url: Api.meituanmfcreateOrderToInfoCert,
    params
  })
}
