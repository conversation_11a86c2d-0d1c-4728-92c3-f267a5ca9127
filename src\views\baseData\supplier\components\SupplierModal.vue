<template>
  <BasicModal @register="registerModal" v-bind="$attrs" :title="getTitle" width="80%" @ok="handleOk">
    <div class="min-h-100">
      <BasicForm @register="registerForm">
        <template #businessicense>
          <Upload
            v-model:file-list="businessicense"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handleFileRequest"
            :multiple="true"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #contract>
          <Upload
            v-model:file-list="contracts"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handlecontractsFileRequest"
            :multiple="true"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #names="{ model }">
          <Input v-if="!model.is_company" v-model:value="model.name" />
          <!-- <Select
            v-else
            show-search
            allow-clear
            @search="handlesearch"
            v-model:value="model.name"
            :options="qccarray"
            :fieldNames="{ value: 'name', label: 'name' }"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            :not-found-content="null"
            @change="
              (_, shall:any) => {
                model.credit_code = shall.CreditCode
                model.oper_name = shall.OperName
                model.company_status = shall.Status
                model.address = shall.Address
              }
            "
          /> -->
          <div v-else>
            <InputSearch v-model:value="model.name" enter-button="搜索" :loading="searchlodaing" @search="search" />
            <!-- <Button type="primary" @click="search" :loading="searchlodaing">搜索</Button> -->
            <div class="serchdiv" v-if="qccarray.length > 0 && qccshow">
              <ul ref="list" class="serchul">
                <li class="serchli" v-for="item in qccarray" :key="item.name" @click="select(item)">{{ item.Name }}</li>
              </ul>
            </div>
          </div>
        </template>
      </BasicForm>
    </div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts" name="ClientModal">
import { ref, computed, unref, watch, onMounted, onBeforeUnmount } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { updateSupplier, createSupplier, smqccsearchKey } from '/@/api/baseData/supplier'
import { suppliercolumns, schemasFn } from '../datas/SupplierModal'
import { message, Upload, UploadFile, Input, InputSearch, Button } from 'ant-design-vue'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { PlusOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { cloneDeep } from 'lodash-es'
// import { useSupplierStore } from '/@/store/modules/supplier'

// const supplierStore = useSupplierStore()
const emit = defineEmits(['success', 'register'])
//营业执照
const businessicense = ref<UploadFile[]>([])
//合同
const contracts = ref<UploadFile[]>([])

const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, getFieldsValue }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 8 },
  schemas: schemasFn(false),
  colon: true
})

const isUpdate = ref(false)
const record = ref()

//保存点击,其他禁用
const currentEditKeyRef = ref('')

const getTitle = computed(() => (!unref(isUpdate) ? '新增供应商' : '更新供应商'))

const [registerModal, { changeOkLoading, closeModal }] = useModalInner(async (data) => {
  await resetFields()
  isUpdate.value = data.isUpdate
  businessicense.value = []
  contracts.value = []
  currentEditKeyRef.value = ''
  setTableData([])
  await updateSchema(schemasFn(false, 'index', { updateSchema, getFieldsValue }))
  if (data.isUpdate) {
    // 更新数据
    await setFieldsValue({ ...data?.record, dept_id: data?.record?.dept_id ? data.record.dept_id : void 0 })
    record.value = { ...data?.record, dept_id: data?.record?.dept_id ? data.record.dept_id : void 0 }
    const imgsdata =
      typeof data.record.business_license === 'string' ? JSON.parse(data.record.business_license) : data.record.business_license
    const imgsdata2 = typeof data.record.contract === 'string' ? JSON.parse(data.record.contract) : data.record.contract
    businessicense.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    contracts.value = (
      typeof imgsdata2 === 'string'
        ? [{ url: imgsdata2, uid: +new Date().toString(), name: imgsdata2 }]
        : (imgsdata2 as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    setTableData(data.record.item)
  }
})
const [registerTable, { deleteTableDataRecord, getColumns, getDataSource, setTableData, updateTableDataRecord }] = useTable({
  showIndexColumn: false,
  title: '供应商分属账户',
  columns: suppliercolumns,
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action'
  }
})

async function handleOk() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    const business = formatSubmit()
    // TODO custom api

    for (const key in values) {
      values[key] = values[key] ?? null
    }
    delete values.deposit_send_person_name
    delete values.no_deposit_send_person_name
    console.log(values)

    if (unref(isUpdate)) {
      await updateSupplier({
        id: unref(record).id,
        ...values,
        Invoicing_is_self: values.Invoicing_is_self ?? undefined,
        tax_point: values.tax_point ?? undefined,
        ticket_point: values.ticket_point ?? undefined,
        items: business
      })
    } else {
      await createSupplier({
        ...values,
        Invoicing_is_self: values.Invoicing_is_self ?? undefined,
        tax_point: values.tax_point ?? undefined,
        ticket_point: values.ticket_point ?? undefined,
        items: business
      })
    }

    // console.log(values)
    await closeModal()
    changeOkLoading(false)
    message.success(unref(isUpdate) ? '更新成功' : '新增成功')
    emit('success')
  } catch (e) {
    await changeOkLoading(false)
    console.error(e)
  }
}

//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  businessicense.value = businessicense.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: businessicense.value.map((item) => item.url)
  })
}
//附件上传
async function handlecontractsFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  contracts.value = contracts.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: contracts.value.map((item) => item.url)
  })
}

watch(
  () => businessicense.value,
  async (val) => {
    await setFieldsValue({ business_license: val?.map((item) => item.url) })
  }
)
watch(
  () => contracts.value,
  async (val) => {
    await setFieldsValue({ contract: val?.map((item) => item.url) })
  }
)

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

function handlecopylink(record) {
  const newrecord = formatObject(record) as any
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//添加明细
async function handleAdd() {
  const newRowDataItem = {}
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性
      const Columnskey = formatObject(record)
      for (const key in Columnskey) {
        // 如果不是business_type且值为空（或undefined）
        console.log(Columnskey)
        if (Columnskey[key] === '' || Columnskey[key] === undefined) {
          message.error('全为必填,请检查是否完成填写')
          return
        }
      }
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}

const qccarray = ref<any>([])
// const handlesearch = debounce(handleSearchs, 1500)
// async function handleSearchs(e) {
//   if (e.length > 0) {
//     const { Result } = await smqccsearchKey({ searchKey: e })
//     qccarray.value = Result
//   }
// }
const qccshow = ref(false)
const searchlodaing = ref(false)

// 初始化时获取元素并添加事件监听器
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
})

// 组件卸载时移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick)
})

const list = ref<any>(null)
function handleGlobalClick(event) {
  if (!list.value.contains(event.target)) {
    qccshow.value = false
  }
}
function search(e) {
  qccarray.value = []
  qccshow.value = true
  searchlodaing.value = true
  setTimeout(async () => {
    const { Result } = await smqccsearchKey({ searchKey: e })
    qccarray.value = Result
    searchlodaing.value = false
  }, 2000)
}

function select(item) {
  setFieldsValue({
    name: item.Name,
    credit_code: item.CreditCode,
    oper_name: item.OperName,
    company_status: item.Status,
    address: item.Address
  })
  qccshow.value = false
}
</script>

<style lang="less">
.serchdiv {
  position: absolute;
  width: 100%;
  background: white;
  z-index: 1;

  .serchul {
    width: 100%;
    background: white;
    .serchli {
      width: 100%;
      height: 40px;
      line-height: 40px;
      background: white;
      &:hover {
        background: #f5f5f5;
      }
    }
  }
}
</style>
