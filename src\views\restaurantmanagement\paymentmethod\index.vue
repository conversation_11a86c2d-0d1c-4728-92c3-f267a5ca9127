<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editModal @register="registereditModal" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { mfgetCheckMethod } from '/@/api/restaurantmanagement/paymentmethod'
import { BasicTable, useTable, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import editModal from './components/editModal.vue'
import { useModal } from '/@/components/Modal'

const [registereditModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  showTableSetting: true,
  useSearchForm: true,
  columns,
  api: mfgetCheckMethod,
  showIndexColumn: false,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '绑定资金资料',
      onClick: handleEdit.bind(null, record)
    }
  ]

  return editButtonList
}

function handleEdit(record) {
  console.log(record)
  openModal(true, record)
}
</script>
