<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd">凭证生成</Button>
      </template>
    </BasicTable>
    <voucherModal @register="registerModal" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { meituanmfgetOrderList } from '/@/api/restaurantmanagement/orderinformation'
import { BasicTable, useTable } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import voucherModal from './components/voucherModal.vue'
import { useModal } from '/@/components/Modal'

const [registerModal, { openModal }] = useModal()

const [registerTable] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  columns,
  api: meituanmfgetOrderList,
  useSearchForm: true,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['data', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

function handleAdd() {
  openModal(true)
}
</script>
