import { MenuItem, MenuSelectListItem } from './model/menuModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetMenuTree = '/menu/getTree',
  GetMenuSelectTree = '/menu/getSelectTree',
  CreateMenuItem = '/menu/create',
  UpdateMenuItem = '/menu/update',
  GetMenuItemDetail = '/menu/detail',
  DeleteMenuItem = '/menu/delete'
}

export const getMenuTree = (params?: { status?: number; isHidden?: number }) => defHttp.get<MenuItem[]>({ url: Api.GetMenuTree, params })

export const getMenuSelectTree = () => defHttp.get<MenuSelectListItem>({ url: Api.GetMenuSelectTree })

export const createMenuItem = (params?: MenuItem) =>
  defHttp.post<{ id: number }>({ url: Api.CreateMenuItem, params }, { successMessageMode: 'message' })

export const updateMenuItem = (params?: MenuItem) =>
  defHttp.post<{ id: number }>({ url: Api.UpdateMenuItem, params }, { successMessageMode: 'message' })

export const getMenuItemDetail = (params?: { id: number; type?: number }) => defHttp.get<MenuItem>({ url: Api.GetMenuItemDetail, params })

export const deleteMenuItem = (params?: { id: number }) =>
  defHttp.get<{ id: number }>({ url: Api.DeleteMenuItem, params }, { successMessageMode: 'message' })
