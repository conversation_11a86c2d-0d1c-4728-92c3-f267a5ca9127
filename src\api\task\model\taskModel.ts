export interface TaskItem {
  id?: number
  type: number
  name: string
  uidCreator: number
  uidInCharge: number
  nameInCharge: string
  ptid: number
  ptname: string
  rtid: number
  rtname: string
  status: number
  endTime: string
  createTime: string
  remark: string
  childNum: number
  children?: Array<TaskItem>
}

export interface TaskRelItem {
  id?: number
  ptrtid: number
  ptrname: string
  estNum: number
  num: number
  beginTime: string
}
