<template>
  <BasicModal @register="register" @ok="handleSubmit" title="编辑人员" :min-height="450">
    <BasicForm @register="RegisterForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { projectwksetDesigner } from '/@/api/erp/sales'
import { schemasPerson } from '../datas/Modal'
const emit = defineEmits(['success'])

const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  resetSchema(schemasPerson)
  resetFields()
  setFieldsValue({
    id: data.id,
    design: data.design.map((item) => item.id),
    design2d: data.design2d.map((item) => item.id),
    design3d: data.design3d.map((item) => item.id)
  })
})
const [RegisterForm, { resetFields, setFieldsValue, validate, resetSchema }] = useForm({
  labelWidth: 100,
  // schemas,
  showActionButtonGroup: false
})

async function handleSubmit() {
  try {
    changeOkLoading(true)

    const formdata = await validate()
    console.log(formdata)
    projectwksetDesigner(formdata)
    closeModal()
    changeOkLoading(false)
    emit('success')
  } catch (e) {
    console.log(e)
  }
}
</script>
