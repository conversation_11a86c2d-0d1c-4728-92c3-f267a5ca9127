export interface StockingItem {
  created_at?: string
  desc?: string
  doc_id?: number
  doc_in_id?: number
  id?: number
  imgs?: string[]
  item_out?: StockingItemOut[]
  name?: string
  origin_stocking_id?: null
  pkg_num?: number
  pkg_received?: number
  puid?: string
  purchase_id?: number
  /**
   * 可用数量
   */
  qty_available?: number
  qty_defective?: number
  /**
   * 出库数量
   */
  qty_out?: string
  qty_received?: number
  qty_stocking?: number
  qty_total?: number
  received_at?: null
  remark?: string
  request_id?: number
  src?: number
  /**
   * 来源
   */
  src_name?: string
  status?: number
  /**
   * 状态
   */
  status_name?: string
  unit?: string
  unit_price?: string
  updated_at?: string
  warehouse_id?: number
  work_id?: number
  [property: string]: any
}

export interface StockingItemOut {
  batch_code?: number
  created_at?: string
  dept_id?: number
  doc_id?: number
  id?: number
  quantity?: number
  request_id?: number
  stocking_id?: number
  updated_at?: string
  warehouse_id?: number
  work_id?: number
  [property: string]: any
}
