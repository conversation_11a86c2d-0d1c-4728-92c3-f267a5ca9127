import { create<PERSON><PERSON><PERSON><PERSON>, createD<PERSON><PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON>, createDefault<PERSON>ilterR<PERSON>, createFormItemRender } from './common'

export default {
  renderDefault: createDefaultRender(),
  renderEdit: createEditR<PERSON>(),
  renderFilter: createFilter<PERSON><PERSON>(),
  defaultFilterMethod: createDefaultFilterRender(),
  renderItemContent: createFormItemRender()
}
