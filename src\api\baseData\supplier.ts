//供应商管理
import { SupplierParams } from './model/supplierModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetSupplier = '/erp/sm/get',
  UpdateSupplier = '/erp/sm/update',
  CreateSupplier = '/erp/sm/add',
  RemoveSupplier = '/erp/sm/remove',
  DetailSupplier = '/erp/sm/detail',
  GetSupplierDebtor = '/erp/sm/getCreator',
  SetIsGbuilder = '/erp/sm/setIsGbuilder',
  //qcc
  smqccsearchKey = '/erp/sm/searchKey'
}

export const getSupplier = (params?: {}) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetSupplier, params })

export const updateSupplier = (params?: SupplierParams) => defHttp.post({ url: Api.UpdateSupplier, params })

export const createSupplier = (params?: SupplierParams) =>
  defHttp.post({ url: Api.CreateSupplier, params }, { successMessageMode: 'message' })

export const removeSupplier = (params: { id: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.RemoveSupplier, params }, { successMessageMode: 'message' })

export const detailSupplier = (params: { id: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.DetailSupplier, params })

export const getSupplierDebtor = (params?: {}) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetSupplierDebtor, params })

export const setIsGbuilder = (params: { id: number; is_Gbuilder: number }) => defHttp.get({ url: Api.SetIsGbuilder, params })
export const smqccsearchKey = (params?: {}) => defHttp.get({ url: Api.smqccsearchKey, params })
