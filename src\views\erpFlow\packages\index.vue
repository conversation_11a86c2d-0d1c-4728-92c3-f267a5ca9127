<template>
  <div class="p-4">
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Tooltip placement="top">
          <template #title> 按搜索条件导出</template>
          <Button v-if="hasPermission(550)" type="primary" @click="handleExportPackage">导出包裹</Button>
        </Tooltip>
        <Tooltip placement="top">
          <template #title>
            勾选同一张销售订单的包裹进行库存转换到新的库存订单中（包裹状态要求：已入库&未出库&未备货&未退货&非拼货&未报废&新包裹&未生成箱单的包裹）</template
          >
          <Button v-if="hasPermission(583)" type="primary" @click="handletransfer">包裹快速库存转移</Button>
        </Tooltip>
        <Button v-if="hasPermission(479)" type="primary" @click="handleCheckreater">批量设置退货并备货</Button>
        <Dropdown>
          <template #overlay>
            <Menu @click="handleMenu">
              <MenuItem key="putDownload">拼柜包裹模板下载</MenuItem>
              <MenuItem v-if="hasPermission(505)" key="putImport">拼柜包裹导入</MenuItem>
              <MenuItem v-if="hasPermission(503)" key="normalImport">普通包裹导入</MenuItem>
            </Menu>
          </template>
          <a-button type="primary">
            包裹导入操作
            <DownOutlined />
          </a-button>
        </Dropdown>
        <Dropdown>
          <template #overlay>
            <Menu @click="handleMenu">
              <!-- <MenuItem key="putDownload">拼柜包裹模板下载</MenuItem> -->
              <!-- <MenuItem v-if="hasPermission(493)" key="ExportPdf">导出包裹二维码</MenuItem> -->
              <MenuItem v-if="hasPermission(568)" key="Exportcomplex">大标签二维码（无logo）</MenuItem>
              <MenuItem v-if="hasPermission(567)" key="ExportcomplexPdf">大标签二维码（有logo）</MenuItem>
              <MenuItem v-if="hasPermission(610)" key="ExportcomplexlightfixturePdf">灯饰二维码(单商品)</MenuItem>
              <MenuItem v-if="hasPermission(664)" key="ExportcomplexlightfixturePdfNew">灯饰二维码(多商品)</MenuItem>
            </Menu>
          </template>
          <a-button type="primary">
            二维码导出
            <DownOutlined />
          </a-button>
        </Dropdown>
        <!--        <a-button v-if="hasPermission(503)" type="primary" @click="handleImportPackages">包裹导入</a-button>-->
        <Dropdown>
          <template #overlay>
            <Menu @click="handleMenu">
              <MenuItem key="download">包裹描述模板下载</MenuItem>
              <Tooltip placement="left">
                <template #title>批量导入包裹描述</template>
                <MenuItem v-if="hasPermission(480)" key="import">导入包裹描述</MenuItem>
              </Tooltip>
            </Menu>
          </template>
          <a-button type="primary">
            包裹描述导入操作
            <DownOutlined />
          </a-button>
        </Dropdown>
        <!--                <Tooltip>-->
        <!--                  <template #title>批量导入包裹描述</template>-->
        <!--                  <Button v-if="hasPermission(480)" type="primary" @click="handleMenuClick">包裹描述导入</Button>-->
        <!--                </Tooltip>-->
        <Dropdown>
          <Button type="primary">批量设置备货 <DownOutlined /></Button>
          <template #overlay>
            <Menu @click="handleMenu">
              <!-- <MenuItem key="putDownload">拼柜包裹模板下载</MenuItem> -->
              <!-- <MenuItem v-if="hasPermission(493)" key="ExportPdf">导出包裹二维码</MenuItem> -->
              <MenuItem v-if="hasPermission(479)" key="handleCheckPackage">批量设置已备货</MenuItem>
              <MenuItem v-if="hasPermission(631)" key="handlecancelPackage">批量取消备货</MenuItem>
            </Menu>
          </template>
        </Dropdown>
        <Tooltip>
          <template #title>选中包裹号进行作废,AGR系统作废包裹时只可作废已入库的包裹</template>
          <Button v-if="hasPermission(478)" type="primary" @click="handlecancellation">包裹作废</Button>
        </Tooltip>

        <Tooltip>
          <template #title>选中包裹号进行调拨</template>
          <Button v-if="hasPermission(477)" type="primary" @click="handleAllot">包裹调拨</Button>
        </Tooltip>
        <Button v-if="hasPermission(475)" type="primary" @click="handleCreate">创建包裹</Button>
        <Button v-if="hasPermission(476)" type="primary" @click="handleAbnormal">包裹转换</Button>
        <Button v-if="hasPermission(481)" type="primary" @click="handleCreateBoxingOrder">生成装箱单</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="dropCreateActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="extend-table-container">
          <BasicTable
            :key="record.status"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            class="p-4"
            @register="registerChildrenTable"
            :api="getPackageDetail.bind(null, { ids: [record.id] })"
          />
        </div>
      </template>
    </BasicTable>
    <EditDrawer @register="registerEditDrawer" @success="reload" />
    <PackagesDrawer @register="registerDrawer" @success="reload" />
    <AbnormalDrawer @register="registerAbnormalDrawer" @success="reload" />
    <CreateBoxingModal @register="registerCreateBoxingModal" @success="reload" />
    <AllotModal @register="registerModal" @success="reload" />
    <ListsDrawer @register="registerListsDrawer" />
    <uploadExcelModal @register="registerExcelModal" @success="reload" />
    <!-- <ExportModal @register="registerExportModal" /> -->
    <ImportPackagesModal @register="registerImportPackagesModal" @success="reload" />
    <ImportCustomModal @register="registerImportCustomModal" @success="reload" />
    <caseModal @register="registerCaseModal" @success="reload" />
    <warningModal @register="registerWarningModal" @skip="openCreateBoxingModal(true, { rows: getSelectRows() })" />
    <handleExportcomplexPdfModal @register="registerExportcomplexPdf" />
    <transterModal @register="registerTransferModal" @success="reload" />
    <ImagesModal @register="registerImagesModal" />
  </div>
</template>

<script setup lang="ts">
import { Button, Tooltip, Dropdown, Menu, message } from 'ant-design-vue'
import { type ActionItem, BasicTable, useTable, TableAction, TableActionType } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import PackagesDrawer from '/@/views/erpFlow/packages/components/PackagesDrawer.vue'
import {
  getPackageList,
  getPackageDetail,
  setIsStock,
  checkPackage,
  setIsInRetreat,
  getPackageListExcel,
  packagecancelStock
} from '/@/api/erpFlow/packages'
import { childRenColumns, columns, searchSchemas } from './datas/datas'
import EditDrawer from '/@/views/erpFlow/packages/components/EditDrawer.vue'
import AbnormalDrawer from '/@/views/erpFlow/packages/components/AbnormalDrawer.vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import CreateBoxingModal from '/@/views/erpFlow/packages/components/CreateBoxingModal.vue'
import AllotModal from './components/AllotModal.vue'
import ListsDrawer from './components/ListsDrawer.vue'
import { ref } from 'vue'
import uploadExcelModal from './components/uploadExcelModal.vue'
import { usePermission } from '/@/hooks/web/usePermission'
// import ExportModal from '/@/views/erpFlow/packages/components/ExportModal.vue'
import ImportPackagesModal from '/@/views/erpFlow/packages/components/ImportPackagesModal.vue'
import { DownOutlined } from '@ant-design/icons-vue'
import axios from 'axios'
import ImportCustomModal from '/@/views/erpFlow/packages/components/ImportCustomModal.vue'
import caseModal from './components/casesModal.vue'
import { useRoute } from 'vue-router'
import handleExportcomplexPdfModal from './components/ExportcomplexitempdfModal.vue'
import transterModal from './components/transterModal.vue'
import warningModal from './components/warningModal.vue'
import { PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const'
import ImagesModal from './components/ImagesModal.vue'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const pathname = window.location.pathname

const MenuItem = Menu.Item

const downloading = ref(false)

const loading = ref<boolean>(false)

const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})

const { createMessage } = useMessage()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerAbnormalDrawer, { openDrawer: openAbnormalDrawer, setDrawerProps: setAbnormalDrawer }] = useDrawer()

const [registerEditDrawer, { openDrawer: openEditDrawer, setDrawerProps: setEditDrawerProps }] = useDrawer()

// const [registerExportModal, { openModal: openExportModal }] = useModal()

const [registerCreateBoxingModal, { openModal: openCreateBoxingModal }] = useModal()
const [registerImagesModal, { openModal: openImagesModal }] = useModal()

const [registerWarningModal, { openModal: openWarningModal, setModalProps: setWarningModalProps }] = useModal()

const [registerTransferModal, { openModal: openTransferModal, setModalProps: setTransferModalProps }] = useModal()

const [registerTable, { reload, clearSelectedRowKeys, getSelectRows, getSelectRowKeys, getForm, setLoading }] = useTable({
  title: '包裹',
  api: getPackageList,
  columns: columns,
  formConfig: {
    name: 'searchForm',
    labelWidth: 100,
    schemas: searchSchemas,
    alwaysShowLines: 1,
    baseColProps: {
      span: 6
    },
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  isTreeTable: true,
  useSearchForm: true,
  showTableSetting: true,
  rowKey: 'id',
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action'
    // slots: { customRender: 'action' },
  },
  rowSelection: {
    preserveSelectedRowKeys: false,
    getCheckboxProps(record) {
      return { disabled: !!record.is_old }
    }
  },
  afterFetch: (res) => {
    clearSelectedRowKeys()
    return res
  },
  pagination: {
    pageSize: 10,
    pageSizeOptions: [...PAGE_SIZE_OPTIONS, '300', '500', '1000'],
    position: ['bottomRight']
  }
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  afterFetch: (res) => {
    console.log(res)

    return res[0].items
  },
  useSearchForm: false,
  isTreeTable: false,
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})

function handleCreate() {
  setDrawerProps({ title: '新建包裹', showFooter: true })
  openDrawer(true, { type: 'add' })
}

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record, 'edit'),
      disabled: [record.is_old, record.is_out, record.is_in, record.is_cancel, record.is_stock, record.is_join, record.is_scrap].some(
        (item) => ![0].includes(item)
      ),
      ifShow: hasPermission(482)
    }
    // {
    //   icon: 'material-symbols:assignment-add-outline',
    //   label: '生成装箱单',
    //   onClick: handleCreateBoxingOrder.bind(null, record)
    // }
  ]
}

function dropCreateActions(record) {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission(490)
    },
    {
      icon: 'ant-design:eye-outlined',
      label: '包裹调拨/转换列表',
      onClick: handletransition.bind(null, record),
      ifShow: hasPermission(483)
    },
    {
      icon: 'clarity:note-edit-line',
      label: '描述编辑',
      onClick: handleEdit.bind(null, record, 'desc'),
      disabled: record.is_out == 2 || record.is_join,
      ifShow: hasPermission(484)
    },
    {
      icon: 'material-symbols:assignment-add-outline',
      label: '修改包裹信息',
      onClick: handlecases.bind(null, record),
      ifShow: hasPermission(515)
    },
    {
      label: '包裹入库图片',
      onClick: handleImage.bind(null, record)
    }
  ]
}

function handleEdit(record, type) {
  setEditDrawerProps({ title: '编辑包裹', showFooter: true })
  openEditDrawer(true, { record, type: 'edit', isUpdate: type == 'edit' })
}

//打包件数更改
const [registerCaseModal, { openModal: opencaseModal }] = useModal()
function handlecases(record) {
  opencaseModal(true, record)
}

function handleDetail(record) {
  setEditDrawerProps({ title: '包裹详情', showFooter: false })
  openEditDrawer(true, { record, type: 'detail', isUpdate: false })
}

function handleAbnormal() {
  const selectRow = getSelectRows()
  if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
  const [firstSelect] = selectRow
  if (!selectRow.every((item) => item.is_in === firstSelect.is_in) || firstSelect.is_in === 1) {
    return createMessage.error('只允许全部已入库包裹或全部未入库包裹进行包裹转换')
  }
  const isSelectRowInStatus = selectRow.every((item) => item.is_in === 2)
  for (const item of selectRow) {
    const result = verifySelect(item, [
      { field: 'is_old', value: 0, condition: 'equal' },
      { field: 'is_out', value: 0, condition: 'equal' },
      { field: 'is_cancel', value: 0, condition: 'equal' },
      { field: 'is_scrap', value: 0, condition: 'equal' },
      { field: 'is_join', value: 0, condition: 'equal' }
    ])
    if (!result) return
  }
  setAbnormalDrawer({ title: '包裹异动', showFooter: true })
  openAbnormalDrawer(true, { type: 'edit', recordIds: getSelectRows().map((item) => item.id), Status: isSelectRowInStatus })
}

const messagekey = ref('')
async function handleCreateBoxingOrder() {
  try {
    setLoading(true)
    // const messagekey = ref('')
    message.success({ content: '正在审核中，请耐心等待...', key: messagekey.value, duration: 0, style: { fontSize: '24px' } })
    const selectRow = getSelectRows()
    if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
    if ([...new Set(selectRow.map((item) => item.project_number))].length !== 1) return createMessage.error('只允许同一个项目创建装箱单')
    for (const item of selectRow) {
      const result = verifySelect(item, [
        { field: 'packing_id', value: null, condition: 'equal' },
        { field: 'is_old', value: 0, condition: 'equal' },
        { field: 'is_out', value: 0, condition: 'equal' },
        { field: 'is_cancel', value: 0, condition: 'equal' },
        { field: 'is_scrap', value: 0, condition: 'equal' },
        { field: 'is_retreat', value: 0, condition: 'equal' }
      ])
      if (!result) return
    }
    const ids = getSelectRowKeys()
    console.log(ids)

    if (!['/sp/', '/sptest/'].includes(pathname)) {
      const { items } = await checkPackage({ ids: ids })
      if (items?.saleList.length > 0) {
        message.destroy(messagekey.value)
        openWarningModal(true, { record: items.saleList, type: 'packages' })
        setWarningModalProps({ title: '欠款警告' })
      } else {
        message.destroy(messagekey.value)
        openCreateBoxingModal(true, { rows: getSelectRows() })
      }
    } else {
      openCreateBoxingModal(true, { rows: getSelectRows() })
    }
  } catch (err) {
    console.log(err)
    setLoading(false)
    setTimeout(() => {
      message.destroy(messagekey.value)
    }, 1000)
  } finally {
    setLoading(false)
    setTimeout(() => {
      message.destroy(messagekey.value)
    }, 1000)
  }
}

//包裹调拨
const [registerModal, { openModal }] = useModal()
function handleAllot() {
  const selectRow = getSelectRows()
  if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
  for (const item of selectRow) {
    const result = verifySelect(item, [
      { field: 'warehouse_id', value: '', condition: 'diff' },
      { field: 'warehouse_item_id', value: '', condition: 'diff' },
      { field: 'is_old', value: 0, condition: 'equal' },
      { field: 'is_out', value: 0, condition: 'equal' },
      { field: 'is_cancel', value: 0, condition: 'equal' },
      { field: 'is_scrap', value: 0, condition: 'equal' }
    ])
    if (!result) return
  }
  openModal(true, { selectRow, type: 'edit' })
}

function handlecancellation() {
  const selectRow = getSelectRows()
  if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
  const arrays = [
    { field: 'is_old', value: 0, condition: 'equal' },
    { field: 'is_out', value: 0, condition: 'equal' },
    { field: 'is_cancel', value: 0, condition: 'equal' },
    { field: 'is_scrap', value: 0, condition: 'equal' },
    {
      field: 'is_in',
      value: 0,
      condition: 'equal',
      errMsg: '入库中和已入库的包裹不允许进行作废！'
    },
    { field: 'packing_id', value: null, condition: 'equal' }
  ]
  if (['/sp/', '/sptests/'].includes(window.location.pathname)) {
    const index = arrays.findIndex((item) => item.field === 'is_in')
    if (index !== -1) {
      arrays[index] = { field: 'is_in', value: 2, condition: 'equal', errMsg: '未入库和入库中的包裹不允许进行作废！' }
    } else {
      // 如果没有找到，则直接添加新对象
      arrays.push({ field: 'is_in', value: 2, condition: 'equal', errMsg: '未入库和入库中的包裹不允许进行作废！' })
    }
  }
  for (const item of selectRow) {
    const result = verifySelect(item, arrays)
    if (!result) return
  }
  openModal(true, { selectRow, type: 'clean' })
}

//列表页
const [registerListsDrawer, { openDrawer: openListsDrawer }] = useDrawer()

function handletransition(record) {
  openListsDrawer(true, record)
}
//包裹导入
const [registerExcelModal, { openModal: openExcelModal }] = useModal()
function handleMenuClick() {
  openExcelModal(true)
}

async function handleDownload(params) {
  try {
    downloading.value = true
    const response = await axios.get(params.url, {
      responseType: 'blob'
    })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response.data)
    downloadLink.download = params.fileName

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
  } catch (e) {
    createMessage.error('下载失败')
  } finally {
    downloading.value = false
  }
}

function handleMenu({
  key
}: {
  key:
    | 'download'
    | 'import'
    | 'trigger'
    | 'putDownload'
    | 'putImport'
    | 'normalImport'
    | 'ExportPdf'
    | 'Exportcomplex'
    | 'ExportcomplexPdf'
    | 'ExportcomplexlightfixturePdf'
    | 'ExportcomplexlightfixturePdfNew'
    | 'handleCheckPackage'
    | 'handlecancelPackage'
}) {
  if (key === 'download' && downloading.value) return createMessage.error('正在下载中，请勿重复点击')
  const mapFn = {
    import: handleMenuClick,
    download: handleDownload,
    trigger: handleMenuClick,
    normalImport: handleImportPackages,
    putDownload: handleDownload,
    putImport: handleImportCustom,
    // ExportPdf: handleExportPdf,
    ExportcomplexPdf: handleExportcomplexModal,
    Exportcomplex: handleExportcomplexModal,
    ExportcomplexlightfixturePdf: handleExportcomplexModal,
    ExportcomplexlightfixturePdfNew: handleExportcomplexModal,
    handleCheckPackage: handleCheckPackage,
    handlecancelPackage
  }

  const mapParams = {
    download: {
      url: 'https://img.gbuilderchina.com/erp/purchase/20240903/172580941166d666800d3a8392706055.xlsx',
      fileName: `包裹描述导入-${+new Date()}.xlsx`
    },
    putDownload: {
      url: 'https://img.gbuilderchina.com/erp/purchase/20241015/1729084907670e0ca083771555113865.xlsx',
      fileName: `拼柜包裹导入-${+new Date()}.xlsx`
    },
    ExportcomplexPdf: {
      isPdf: true,
      lightfixture: false
    },
    Exportcomplex: {
      isPdf: false,
      lightfixture: false
    },
    ExportcomplexlightfixturePdf: {
      lightfixture: true,
      isNewStyle: false
    },
    ExportcomplexlightfixturePdfNew: {
      lightfixture: true,
      isNewStyle: true
    }
  }

  const params = mapParams[key] || {}

  // 调用相应的函数
  mapFn[key]?.(params)
}

const [registerImportPackagesModal, { openModal: openImportPackagesModal }] = useModal()
function handleImportPackages() {
  openImportPackagesModal(true)
}

const [registerImportCustomModal, { openModal: openImportCustomModal }] = useModal()
function handleImportCustom() {
  openImportCustomModal(true)
}

async function handleCheckPackage() {
  try {
    const selectRow = getSelectRows()
    if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
    if (!selectRow.every((item) => item.packing_strid)) return createMessage.error('未生成装箱单不允许设置已备货！')
    for (const item of selectRow) {
      const result = verifySelect(item, [
        { field: 'warehouse_id', value: '', condition: 'diff' },
        { field: 'warehouse_item_id', value: '', condition: 'diff' },
        { field: 'is_old', value: 0, condition: 'equal' },
        { field: 'is_out', value: 0, condition: 'equal' },
        { field: 'is_cancel', value: 0, condition: 'equal' },
        { field: 'is_stock', value: 0, condition: 'equal' },
        { field: 'is_in', value: 2, condition: 'equal' },
        { field: 'is_out', value: 0, condition: 'equal' },
        { field: 'is_scrap', value: 0, condition: 'equal' }
      ])
      if (!result) return
    }
    loading.value = true
    const { msg } = await setIsStock({ stockList: getSelectRows().map((item) => ({ id: item.id })), stock_source: 1 })
    if (msg === 'success') {
      createMessage.success('设置备货成功')
      await reload()
      return
    }
  } catch (err) {
    createMessage.error('设置备货失败')
  } finally {
    loading.value = false
  }
}
async function handlecancelPackage() {
  try {
    const selectRow = getSelectRows()
    if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
    if (!selectRow.every((item) => item.packing_strid)) return createMessage.error('未生成装箱单不允许取消备货！')
    for (const item of selectRow) {
      const result = verifySelect(item, [
        { field: 'warehouse_id', value: '', condition: 'diff' },
        { field: 'warehouse_item_id', value: '', condition: 'diff' },
        { field: 'is_old', value: 0, condition: 'equal' },
        { field: 'is_out', value: 0, condition: 'equal' },
        { field: 'is_cancel', value: 0, condition: 'equal' },
        { field: 'is_stock', value: 0, condition: 'diff', errMsg: '包裹未备货，不允许取消备货！' },
        { field: 'is_in', value: 2, condition: 'equal' },
        { field: 'is_out', value: 0, condition: 'equal' },
        { field: 'is_scrap', value: 0, condition: 'equal' }
      ])
      if (!result) return
    }
    loading.value = true
    const { msg } = await packagecancelStock({
      stockList: getSelectRows().map((item) => ({ id: item.id, packing_id: item.packing_id })),
      cancel_stock_source: 1
    })
    if (msg === 'success') {
      createMessage.success('取消备货成功')
      await reload()
      return
    }
  } catch (err) {
    createMessage.error('取消备货失败')
  } finally {
    loading.value = false
  }
}

/**
 * verifySelect: 选中行验证函数，全部验证通过为true，否则为false
 * @param row 当前行数据 <any>
 * @param verify 验证数据集合
 * @param verify.field 验证的字段 <string>
 * @param verify.value 与验证字段值进行判断的值 <any>
 * @param verify.condition equal | diff <'equal' | 'diff'>
 * @param verify.errMsg 发生错误时的提示信息, 通用的错误提示信息，可以再mapErrMsg中配置 <string>
 * @returns verifyResult 返回验证结果 <boolean>
 */
function verifySelect(row: Object, verify: Array<{ field: string; value: any; condition: 'equal' | 'diff'; errMsg?: string }>) {
  let verifyResult = true
  const mapErrMsg = {
    packing_id: '已装箱包裹不得进行此操作',
    warehouse_id: '包裹无仓库不得进行此操作',
    warehouse_item_id: '包裹无仓位不得进行此操作',
    is_old: '旧包裹不得进行此操作',
    is_out: '已出库包裹不得进行此操作',
    is_cancel: '已作废包裹不得进行此操作',
    is_stock: '已备货包裹不得进行此操作',
    is_in: '未入库包裹不得进行此操作',
    is_retreat: '未退货包裹才能进行此操作',
    is_scrap: '请选择未报废包裹进行此操作'
  }
  const mapFn = {
    equal,
    diff
  }
  function equal(verifyItem) {
    return row[verifyItem.field] == verifyItem.value
  }

  function diff(verifyItem) {
    return row[verifyItem.field] != verifyItem.value
  }

  for (const item of verify) {
    const result = mapFn[item.condition]?.(item)
    if (!result) {
      verifyResult = false
      createMessage.error(item.errMsg ?? mapErrMsg[item.field])
      break
    }
  }
  return verifyResult
}

// function handleExportPdf() {
//   const rows = getSelectRows()
//   if (rows.length === 0) return createMessage.error('请选择需要导出二维码的包裹！')
//   openExportModal(true, { rows })
// }
//退货并备货
function handleCheckreater() {
  const selectRow = getSelectRows()
  if (selectRow.length === 0) return createMessage.error('请先勾选包裹')
  if (!selectRow.every((item) => item.is_retreat == 1)) return createMessage.error('退货中才能设置退货并备货！')
  if ([...new Set(selectRow.map((item) => item.doc_retreat_in_warehouse_id))].length !== 1)
    return createMessage.error('只允许同一张的入库退货设置退货并备货!')
  const params = selectRow.map((item) => {
    return {
      id: item.id,
      strid: item.strid,
      doc_retreat_in_warehouse_id: item.doc_retreat_in_warehouse_id
    }
  })
  setIsInRetreat({ packageList: params })
  reload()
}

//包裹导出
async function handleExportPackage() {
  try {
    // exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await getPackageListExcel({ ...params, is_excel: 1 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `包裹-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
    setLoading(false)
  } catch (err) {
    createMessage.error('导出失败')
    setLoading(false)

    throw new Error(err)
  } finally {
    setLoading(false)
    // exporting.value = false
  }
}
//复杂二维码
const [registerExportcomplexPdf, { openModal: openExportcomplexPdf }] = useModal()
function handleExportcomplexModal(type) {
  const rowskeys = getSelectRowKeys()
  // const Rows = getSelectRows()
  console.log(type)

  if (rowskeys.length === 0) return createMessage.error('请选择需要导出二维码的包裹！')
  // for (const items of Rows) {
  //   if (items.purchase_strids.length == 0) {
  //     return createMessage.error('当前选中包裹中,部分包裹没有采购单,无法进行打印二维码！')
  //   }
  // }

  openExportcomplexPdf(true, {
    rowskeys,
    type: type.isPdf,
    lightfixture: type.lightfixture,
    isNewStyle: type.isNewStyle
  })
}
//包裹转移
function handletransfer() {
  const rowskeys = getSelectRows()
  const Rows = getSelectRows()
  if (rowskeys.length === 0) return createMessage.error('请选择需要转移的包裹！')
  const sourceuniqids = Rows.every((item) => item.source_uniqids.length === 1)
  if (!sourceuniqids) {
    return createMessage.error('请选择销售订单唯一的包裹！')
  }
  const sourcevalue = Rows.every((item, index, array) => {
    const currentSourceUniqid = item.source_uniqids[0]?.source_uniqid
    const referenceSourceUniqid = array[0].source_uniqids[0]?.source_uniqid
    return index === 0 || currentSourceUniqid === referenceSourceUniqid
  })
  if (!sourcevalue) {
    return createMessage.error('请选择销售订单相同的包裹！')
  }
  const condition = Rows.every(
    (item) =>
      item.is_out == 0 &&
      item.is_in == 2 &&
      item.is_cancel == 0 &&
      item.is_stock == 0 &&
      item.is_old == 0 &&
      item.is_retreat == 0 &&
      item.is_scrap == 0 &&
      item.packing_id == null
  )
  console.log(condition)
  if (!condition) {
    return createMessage.error('包裹状态要求：已入库&未出库&未备货&未退货&非拼货&未报废&新包裹&未生成箱单的包裹！')
  }
  openTransferModal(true, { recoed: Rows })
  setTransferModalProps({ title: '包裹转移', width: '800px' })
}

function handleImage(record) {
  openImagesModal(true, { data: record.inWarehouse_imgs })
}
</script>
<style scoped lang="less">
.extend-table-container {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
