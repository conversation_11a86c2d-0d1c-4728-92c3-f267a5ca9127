import { defHttp } from '/@/utils/http/axios'

enum Api {
  mfgetMenuCost = '/meituan/mf/getMenuCost',
  mfupdateMenuCost = '/meituan/mf/updateMenuCost',
  mfsetStatusMenuCost = '/meituan/mf/setStatusMenuCost'
}

export const mfgetMenuCost = (params?: {}) => {
  return defHttp.get({
    url: Api.mfgetMenuCost,
    params
  })
}
export const mfupdateMenuCost = (params?: {}) => {
  return defHttp.post(
    {
      url: Api.mfupdateMenuCost,
      params
    },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
export const mfsetStatusMenuCost = (params?: {}) => {
  return defHttp.post(
    {
      url: Api.mfsetStatusMenuCost,
      params
    },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
}
