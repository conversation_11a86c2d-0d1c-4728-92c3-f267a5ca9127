<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :show-footer="propsData.type === 'create'" @ok="handleSubmit" width="90%">
    <BasicForm @register="registerForm" />
    <template v-if="propsData.type === 'create'">
      <BasicTable @register="registerTable" @fetch-success="onFetchSuccess">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'proportion'">
            <template v-if="record.is_audit !== 1"> <span></span></template>
            <template v-else>
              <FormItemRest>
                <InputNumber v-model:value="editableData[record.id]" :min="0" :max="100" :precision="2">
                  <template #addonAfter>%</template>
                </InputNumber>
              </FormItemRest>
            </template>
          </template>
        </template>
      </BasicTable>
    </template>
    <template v-else>
      <BasicTable @register="registerDetailTable" />
    </template>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue'
import { Form, InputNumber, message } from 'ant-design-vue'

import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { getDeptTree } from '/@/api/admin/dept'
import { add } from '/@/utils/math'
import { createShareManage, shareManageDetail } from '/@/api/baseData/shareManage'

import { TType, columns, getSchemasList, detailsColumns } from '../datas/drawer'
import { isDef } from '/@/utils/is'

const FormItemRest = Form.ItemRest
let editableData = reactive({})

const emit = defineEmits(['success', 'register'])

const propsData = ref<{ type: TType }>({ type: 'create' })
const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    changeLoading(true)
    await resetFields()
    propsData.value = data
    updateSchema(getSchemasList(data.type))
    editableData = {}
    if (data.type === 'detail') {
      const { items } = await shareManageDetail({ id: data.record.id })
      setFieldsValue(items)
      setTableData(items.details)
    }
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { setFieldsValue, validate, updateSchema, resetFields }] = useForm({
  schemas: getSchemasList('create'),
  labelWidth: 100,
  baseColProps: { span: 12 },
  showActionButtonGroup: false
})

const [registerTable, { expandAll }] = useTable({
  title: '',
  api: getDeptTree,
  searchInfo: { status: 1, is_auth: 0 },
  columns,
  isTreeTable: true,
  pagination: false,
  striped: false,
  showTableSetting: false,
  rowKey: 'id',
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  size: 'small',
  afterFetch: onAfterFetch
})

function onFetchSuccess() {
  nextTick(expandAll)
}

function onAfterFetch(data) {
  getProductionIds(data)
  return data
}

function getProductionIds(inputArr) {
  inputArr.forEach((item) => {
    if (item.isProduction === 1) {
      editableData[item.id] = 0
    }
    if (item.children) {
      getProductionIds(item.children)
    }
  })
}

//计算editableData里所有的value加起来是不是刚好等于100
function getSum() {
  let sum = 0
  for (const key in editableData) {
    sum = add(sum, editableData[key])
  }
  if (sum !== 100) {
    message.error(`分摊比例总和必须等于100%！,目前总和为${sum}%`)
    return false
  } else {
    return Object.keys(editableData).map((key) => ({
      dept_id: key,
      proportion: isDef(editableData[key]) ? String(editableData[key]) : 0
    }))
  }
}

const [registerDetailTable, { setTableData }] = useTable({
  title: '',
  columns: detailsColumns,
  pagination: false,
  striped: false,
  showTableSetting: false,
  rowKey: 'id',
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  size: 'small'
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    const result = await getSum()
    const keysLength = Object.keys(editableData).length
    if (keysLength === 0 || !result) {
      changeOkLoading(false)
      return message.error('分摊比例错误，请检查完再次提交！')
    }
    await createShareManage({ ...formData, details: result })
    await closeDrawer()
    changeOkLoading(false)
    emit('success')
  } catch (err) {
    console.error(err)
    changeOkLoading(false)
  }
}
</script>
