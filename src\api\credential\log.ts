import type { ICreateLogList } from '../model/credentialModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetLogList = '/erp/cert/info/getList',
  CreateLog = '/erp/cert/info/create'
}

export const getLogList = (params?: {}) => defHttp.get({ url: Api.GetLogList, params })

export const createLog = (params?: ICreateLogList) =>
  defHttp.get({ url: Api.CreateLog, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
