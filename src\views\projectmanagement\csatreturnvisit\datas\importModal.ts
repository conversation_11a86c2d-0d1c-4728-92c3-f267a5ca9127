import { message } from 'ant-design-vue'

export function transformData2Import(data: Recordable[]): any[] {
  const fieldMap = {
    project_number: '项目ID',
    content: '客户反馈',
    reply_content: '回复内容',
    remark: '备注',
    channel: '回访渠道'
  }

  // 定义必填字段
  const requiredFields = {
    project_number: '项目ID',
    content: '客户反馈',
    channel: '回访渠道'
  }

  // 验证Excel表头是否包含所有必填字段
  const excelHeaders = Object.keys(data[0] || {})
  const missingHeaders = Object.values(requiredFields).filter((header) => !excelHeaders.includes(header))

  if (missingHeaders.length > 0) {
    throw new Error(`导入失败：Excel表格缺少必要的列：${missingHeaders.join('、')}`)
  }

  return data.map((obj, index) => {
    const cookedData: any = {
      project_number: undefined,
      content: '',
      reply_content: '',
      remark: '',
      channel: ''
    }
    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = obj[fieldMap[key]]
      }
    }

    // 验证当前行的必填字段
    const rowNumber = index + 2 // Excel行号从2开始（第1行是表头）
    const missingRequiredFields: string[] = []

    for (const [fieldKey, fieldName] of Object.entries(requiredFields)) {
      const value = cookedData[fieldKey]
      if (value === undefined || value === null || value === '') {
        missingRequiredFields.push(fieldName)
      }
    }

    if (missingRequiredFields.length > 0) {
      throw new Error(`导入失败：第${rowNumber}行缺少必填字段：${missingRequiredFields.join('、')}`)
    }

    // 去除空格
    cookedData.project_number = cookedData.project_number ? cookedData.project_number : 0
    cookedData.content = cookedData.content ? cookedData.content : ''
    cookedData.reply_content = cookedData.reply_content ? cookedData.reply_content : ''
    cookedData.remark = cookedData.remark ? cookedData.remark : ''

    // 将渠道中文名称映射为对应的数字ID
    const channelText = String(cookedData.channel ? cookedData.channel : '').replace(/\s/g, '')
    // 从CHANNEL对象中查找对应的ID
    const channelMap = {
      WA手机版: 1,
      WA电脑版: 2,
      专员微信: 3,
      专员邮箱: 4,
      专员电话: 5,
      部门邮箱: 6
    }

    // 验证渠道字段的有效性
    if (channelText && channelMap[channelText] === undefined && !Object.values(channelMap).includes(Number(channelText))) {
      message.error(
        `导入失败：第${rowNumber}行的回访渠道"${channelText}"不在允许的选项中。允许的选项：${Object.keys(channelMap).join('、')}`
      )
      throw new Error(
        `导入失败：第${rowNumber}行的回访渠道"${channelText}"不在允许的选项中。允许的选项：${Object.keys(channelMap).join('、')}`
      )
    }

    cookedData.channel = channelMap[channelText] !== undefined ? channelMap[channelText] : channelText
    return cookedData
  })
}
