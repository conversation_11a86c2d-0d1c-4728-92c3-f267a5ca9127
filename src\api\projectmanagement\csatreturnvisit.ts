import { defHttp } from '/@/utils/http/axios'

enum Api {
  projectvistgetListByCast = '/erp/project/vist/getListByCast',
  projectvistsetVistStatus = '/erp/project/vist/setVistStatus',
  projectvistsetProjectVist = '/erp/project/vist/setProjectVist',
  projectvistupdateVistLog = '/erp/project/vist/updateVistLog',
  projectvistsetIsCheck = '/erp/project/vist/setIsCheck',
  projectvistsetFinishCast = '/erp/project/vist/setFinishCast',
  projectvistimplodeProjectVist = '/erp/project/vist/implodeProjectVist'
}

export const projectvistgetListByCast = (params) => defHttp.get({ url: Api.projectvistgetListByCast, params })
export const projectvistgetListByCastexcel = (params) =>
  defHttp.get({ url: Api.projectvistgetListByCast, params, responseType: 'blob' }, { isTransformResponse: false })

export const projectvistsetVistStatus = (params) =>
  defHttp.post({ url: Api.projectvistsetVistStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectvistsetProjectVist = (params) =>
  defHttp.post({ url: Api.projectvistsetProjectVist, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectvistupdateVistLog = (params) =>
  defHttp.post({ url: Api.projectvistupdateVistLog, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectvistsetIsCheck = (params) =>
  defHttp.post({ url: Api.projectvistsetIsCheck, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectvistsetFinishCast = (params) =>
  defHttp.post({ url: Api.projectvistsetFinishCast, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectvistimplodeProjectVist = (params) =>
  defHttp.post({ url: Api.projectvistimplodeProjectVist, params }, { successMessageMode: 'message', errorMessageMode: 'none' })
