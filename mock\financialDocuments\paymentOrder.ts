import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
const paymentOrderList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      date: '@date',
      order_no: '@guid',
      department: '数据中心[EN:Date Center]',
      'supplier|1': ['供应商1', '供应商2'],
      account_name: '@ctitle',
      account_bank: '建设银行',
      'account_bank_no|10': '@natural(1, 1000)',
      fund_info: '@ctitle',
      amount: '@natural(1, 1000)',
      'status|1': [0, 1],
      requirement_details: [
        '@guid',
        '@guid',
        '@guid'
        // {
        //   purchase_order_no: '@guid'
        //   // total_demand: '@natural(1, 1000)',
        //   // amount_paid: '@natural(1, 1000)',
        //   // current_payment_amount: '@natural(1, 1000)'
        // },
        // {
        //   purchase_order_no: '@guid'
        //   // total_demand: '@natural(1, 1000)',
        //   // amount_paid: '@natural(1, 1000)',
        //   // current_payment_amount: '@natural(1, 1000)'
        // },
        // {
        //   purchase_order_no: '@guid'
        //   // total_demand: '@natural(1, 1000)',
        //   // amount_paid: '@natural(1, 1000)',
        //   // current_payment_amount: '@natural(1, 1000)'
        // }
      ]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/paymentOrder',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(paymentOrderList)
    }
  }
] as MockMethod[]
