import { MockMethod } from 'vite-plugin-mock'
import { resultSuccess } from '../_util'
const capitalFlowList = (function () {
  const result: any[] = []
  for (let i = 0; i < 5; i++) {
    result.push({
      created_at: '@date',
      creator: '@ctitle',
      'from_plaform|1': ['平台1', '平台2'],
      from_account: '@guid',
      from: '@ctitle',
      from_category: '@ctitle',
      'from_currency|1': ['USD', 'CNY'],
      'to_plaform|1': ['平台1', '平台2'],
      to_account: '@guid',
      to: '@ctitle',
      to_category: '@ctitle',
      'to_currency|1': ['USD', 'CNY'],
      fee: '@natural(1, 100)',
      'payment_person|1': [1, 2, 3, 4, 5]
    })
  }
  return result
})()

export default [
  {
    url: '/api/erp/capitalFlow',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(capitalFlowList)
    }
  }
] as MockMethod[]
