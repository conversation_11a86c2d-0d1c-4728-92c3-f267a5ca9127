import { defHttp } from '/@/utils/http/axios'
import { PackageInfo } from '/@/api/erp/modle/packageTypes'
enum Api {
  // GetPackingList = '/erp/packing/getPackingList',
  GetPackingListByDate = '/erp/packing/getPackingListByDate',
  DeletePacking = '/erp/packing/deletePacking',
  SetPackingStatus = '/erp/packing/setPackingStatus',
  GetPackingDetail = '/erp/packing/detailToPc',
  UpdatePacking = '/erp/packing/updatePacking',
  ExportPackage = '/erp/packing/exportPacking',
  //审批状态
  setApprovedStatus = '/erp/packing/setApprovedStatus',
  //申请
  applyApproved = '/erp/packing/applyApproved',
  //作废
  setIsCancel = '/erp/packing/setIsCancel',
  //申请列表
  getApplyApprovedList = '/erp/packing/getApplyApprovedList',
  packingcancelStatus = '/erp/packing/cancelStatus',
  removePackingPackage = '/erp/packing/deletePackingPackage',
  packingexportWarehousePacking = '/erp/packing/exportWarehousePacking',
  packingexportDeliverPacking = '/erp/packing/exportDeliverPacking',
  packinguploadPackingFiles = '/erp/packing/uploadPackingFiles',
  packinggetPackingFilesList = '/erp/packing/getPackingFilesList'
}

export const getPackingListByDate = (params?: any) => defHttp.get({ url: Api.GetPackingListByDate, params })

export const updatePackage = (data: PackageInfo) => defHttp.post<{ msg: string }>({ url: Api.UpdatePacking, data })

export const getPackingDetail = (params: { id: number }) => defHttp.get({ url: Api.GetPackingDetail, params })

export const delPacking = (id: number) => defHttp.get<{ msg: string }>({ url: Api.DeletePacking, params: { id } })

export const setPackingStatus = (params: number[]) => defHttp.post<{ msg: string }>({ url: Api.SetPackingStatus, params })

export const exportPackage = (isTest: boolean, ids: number[]) =>
  defHttp.post({ url: Api.ExportPackage, data: { ids }, responseType: isTest ? 'json' : 'blob' }, { isTransformResponse: isTest })

export const setApprovedStatus = (params: {}) =>
  defHttp.post({ url: Api.setApprovedStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const applyApproved = (params: {}) =>
  defHttp.post({ url: Api.applyApproved, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const setIsCancel = (params: {}) =>
  defHttp.post({ url: Api.setIsCancel, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const getApplyApprovedList = (params: {}) => defHttp.get({ url: Api.getApplyApprovedList, params })
export const packingcancelStatus = (params: {}) =>
  defHttp.post({ url: Api.packingcancelStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const removePackingPackage = (params) => defHttp.post({ url: Api.removePackingPackage, params })

export const packingexportWarehousePacking = (params) => defHttp.post({ url: Api.packingexportWarehousePacking, params })
export const packingexportDeliverPacking = (isTest: boolean, ids: number[]) =>
  defHttp.post(
    { url: Api.packingexportDeliverPacking, data: { ids }, responseType: isTest ? 'json' : 'blob' },
    { isTransformResponse: isTest }
  )
export const packinguploadPackingFiles = (params) =>
  defHttp.post({ url: Api.packinguploadPackingFiles, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const packinggetPackingFilesList = (params) => defHttp.get({ url: Api.packinggetPackingFilesList, params })
